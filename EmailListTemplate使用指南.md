# 访问示例页面

为了便于测试和演示邮件列表模板，我们提供了一个完整的示例页面。

## 访问地址

通过以下URL可以直接访问示例页面：

```
http://你的域名/emailListExample
```

例如：
- 本地开发环境：`http://localhost:8080/emailListExample`
- 线上环境：`http://your-domain.com/emailListExample`

## 示例页面功能

示例页面包含三个不同配置的邮件列表演示：

1. **示例1：基础邮件列表**
   - 启用所有功能（批量操作、分页、列宽调整）
   - 包含自定义搜索功能
   - 展示完整的邮件列表功能

2. **示例2：简化邮件列表**
   - 禁用批量操作和分页
   - 显示基础的邮件列表
   - 适用于简单的邮件展示场景

3. **示例3：自定义邮件操作**
   - 展示如何添加自定义操作
   - 演示邮件操作的回调函数

## 数据来源

示例页面的数据通过以下方式获取：

1. **主要数据源**：后端API `/getSampleEmails`
2. **备用数据**：如果后端API不可用，则使用本地备用数据

## 后端支持

示例页面由以下后端action支持：

- **页面渲染**：`AdminIndexController.emailListExample()`
- **数据获取**：`AdminIndexController.getSampleEmails()`

## 使用建议

1. 在开发新页面前，先访问示例页面了解模板功能
2. 复制示例页面的配置代码作为起点
3. 根据实际需求调整模板参数
4. 参考示例页面的JavaScript代码实现自定义功能

## 权限要求

访问示例页面需要以下权限：
- 登录系统
- 拥有`dashboard`权限（或系统管理员权限）

如果无法访问，请检查用户权限配置。 