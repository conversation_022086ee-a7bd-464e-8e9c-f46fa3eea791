-- 检查邮件ID 1939503822424436736 的内联附件情况

-- 1. 查看邮件基本信息
SELECT 
    id,
    subject,
    from_address,
    account_id,
    LENGTH(content_html) as html_length,
    CASE 
        WHEN content_html LIKE '%cid:%' THEN '包含CID引用'
        ELSE '不包含CID引用'
    END as has_cid_reference
FROM email_messages 
WHERE id = 1939503822424436736;

-- 2. 查看该邮件的所有附件记录
SELECT 
    id,
    file_name,
    cid,
    status,
    CASE status
        WHEN 0 THEN '未下载'
        WHEN 1 THEN '下载中'
        WHEN 2 THEN '已下载'
        WHEN 3 THEN '下载失败'
        WHEN 4 THEN '已跳过'
        WHEN 5 THEN '待处理'
        ELSE CONCAT('状态', status)
    END as status_text,
    file_size,
    path,
    error_message,
    created_at
FROM email_attachments 
WHERE email_id = 1939503822424436736
ORDER BY created_at;

-- 3. 检查是否有CID为 '2659A0D2-9F6A-4489-BA52-D3EB2F5EFF00' 的附件
SELECT 
    id,
    file_name,
    cid,
    status,
    file_size,
    path,
    error_message
FROM email_attachments 
WHERE email_id = 1939503822424436736 
  AND cid = '2659A0D2-9F6A-4489-BA52-D3EB2F5EFF00';

-- 4. 查看该邮件HTML内容中的CID引用（部分内容）
SELECT 
    id,
    SUBSTRING(content_html, LOCATE('cid:', content_html), 100) as cid_context
FROM email_messages 
WHERE id = 1939503822424436736 
  AND content_html LIKE '%cid:%';

-- 5. 统计该邮件的附件情况
SELECT 
    COUNT(*) as total_attachments,
    SUM(CASE WHEN cid IS NOT NULL AND cid != '' THEN 1 ELSE 0 END) as inline_attachments,
    SUM(CASE WHEN cid IS NULL OR cid = '' THEN 1 ELSE 0 END) as normal_attachments,
    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as downloaded_attachments,
    SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as failed_attachments
FROM email_attachments 
WHERE email_id = 1939503822424436736;

-- 6. 如果需要清理附件记录重新处理（谨慎使用）
-- DELETE FROM email_attachments WHERE email_id = 1939503822424436736;
