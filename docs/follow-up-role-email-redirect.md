# 跟单角色邮件操作重定向功能

## 功能概述

当用户只有跟单角色且邮件是客户发来的时候，以下操作都会自动跳转到翻译页面：
- 回复邮件
- 翻译邮件  
- 删除邮件
- 查看邮件详情

## 实现原理

### 判断条件

1. **用户角色判断**：
   - 用户只有一个角色
   - 该角色的名称包含"跟单"或SN包含"follow"

2. **邮件类型判断**：
   - 邮件是客户相关邮件
   - 发件人是客户，收件人不是客户（即客户发来的邮件）

### 技术实现

#### 后端实现

1. **统一判断接口** (`EmailMessagesAdminController.getEmailActionUrl`)
   - 接收参数：`emailId`（邮件ID）、`action`（操作类型）
   - 返回：包含跳转URL的JSON响应

2. **角色判断方法** (`checkUserOnlyHasFollowUpRole`)
   - 检查用户是否只有一个角色
   - 判断该角色是否是跟单角色

3. **邮件类型判断方法** (`checkIsCustomerEmail`)
   - 使用现有的客户邮件判断逻辑
   - 判断邮件是否是客户发来的

4. **viewEmail方法增强**
   - 在显示邮件详情前先进行判断
   - 如果满足条件，直接重定向到翻译页面

#### 前端实现

1. **统一处理方法** (`EmailListTemplate.handleEmailAction`)
   - 在按钮点击时先调用后端判断方法
   - 根据后端返回的URL执行相应操作

2. **按钮处理逻辑修改**
   - `replyEmail`：使用统一判断逻辑
   - `translateEmail`：使用统一判断逻辑
   - `deleteEmail`：使用统一判断逻辑
   - `showEmailDetail`：使用统一判断逻辑

## 修改的文件

### 后端文件
- `src/main/java/cn/jbolt/admin/emailmessages/EmailMessagesAdminController.java`
  - 添加了 `getEmailActionUrl` 方法
  - 添加了 `checkUserOnlyHasFollowUpRole` 方法
  - 添加了 `checkIsCustomerEmail` 方法
  - 修改了 `viewEmail` 方法
  - 添加了 `testActionLogic` 测试页面方法

- `src/main/java/cn/jbolt/admin/emailmessages/EmailMessagesService.java`
  - 将 `isClientEmail` 方法改为 public

### 前端文件
- `src/main/webapp/assets/js/email-list-template.js`
  - 添加了 `handleEmailAction` 统一处理方法
  - 修改了 `replyEmail`、`translateEmail`、`deleteEmail`、`showEmailDetail` 方法

### 测试文件
- `src/main/webapp/_view/admin/emailmessages/test_action_logic.html`
  - 创建了功能测试页面

## 使用方法

### 测试页面
访问 `/admin/emailMessages/testActionLogic` 可以测试功能是否正常工作。

### API接口
- **URL**: `/admin/emailMessages/getEmailActionUrl`
- **方法**: POST
- **参数**: 
  - `emailId`: 邮件ID
  - `action`: 操作类型（reply/translate/delete/view）
- **返回**: 
  ```json
  {
    "state": "ok",
    "data": {
      "url": "/admin/emailMessages/showTranslation?emailId=123&_jb_rqtype_=dialog",
      "msg": "跳转到翻译页面"
    }
  }
  ```

## 工作流程

1. 用户点击邮件操作按钮（回复/翻译/删除）或点击邮件查看详情
2. 前端调用 `handleEmailAction` 方法
3. 该方法向后端发送请求到 `getEmailActionUrl` 接口
4. 后端判断用户角色和邮件类型
5. 如果用户只有跟单角色且邮件是客户发来的，返回翻译页面URL
6. 否则返回原有逻辑的URL
7. 前端根据返回的URL执行相应操作

## 兼容性

- 完全向后兼容，不会影响现有功能
- 只在特定条件下改变按钮行为
- 对于不满足条件的用户，保持原有操作逻辑

## 注意事项

1. 跟单角色的判断基于角色名称包含"跟单"或SN包含"follow"
2. 客户邮件的判断使用现有的 `isClientEmail` 方法
3. 所有操作都会先进行权限和条件检查
4. 翻译页面会在弹窗中打开，提供良好的用户体验
