# HTML实体解码功能使用指南

## 功能概述

在 `viewEmail.html` 文件中新增了HTML实体解码功能，用于正确处理和显示包含HTML实体编码的邮件内容。

## 问题背景

当邮件内容包含HTML实体编码（如 `&lt;`、`&gt;`、`&amp;` 等）时，这些编码需要被正确解码为对应的HTML标签或字符，以便在页面中正确渲染。

## 解决方案

### 1. 核心函数

#### `decodeHtmlEntities(text)`
- **功能**：将HTML实体编码转换为对应的字符或HTML标签
- **参数**：`text` - 包含HTML实体的文本
- **返回值**：解码后的文本
- **支持的实体**：
  - `&lt;` → `<`
  - `&gt;` → `>`
  - `&amp;` → `&`
  - `&quot;` → `"`
  - `&#39;` 或 `&apos;` → `'`
  - `&nbsp;` → 空格
  - 数字实体：`&#数字;` 格式
  - 十六进制实体：`&#x十六进制;` 格式

#### `containsHtmlEntities(text)`
- **功能**：检查文本是否包含HTML实体编码
- **参数**：`text` - 要检查的文本
- **返回值**：布尔值，表示是否包含HTML实体

#### `safeDecodeHtmlEntities(text)`
- **功能**：安全的HTML实体解码（包含XSS防护）
- **参数**：`text` - 包含HTML实体的文本
- **返回值**：安全解码后的文本

#### `sanitizeDecodedContent(content)`
- **功能**：对解码后的内容进行安全检查，防止XSS攻击
- **参数**：`content` - 要处理的内容
- **返回值**：安全处理后的内容

### 2. 安全特性

#### XSS防护
- 移除危险的HTML标签：`script`、`iframe`、`object`、`embed` 等
- 移除危险的属性：`onload`、`onclick`、`javascript:` 等
- 对数字实体进行安全范围检查

#### 支持的安全字符范围
- ASCII可打印字符（32-126）
- 扩展ASCII字符（160-255）
- 常见标点符号（8192-8303）

### 3. 集成点

#### 主要邮件内容处理
```javascript
// 在处理邮件内容前自动检查和解码HTML实体
let processedContent = rawContent;
if (containsHtmlEntities(rawContent)) {
    processedContent = safeDecodeHtmlEntities(rawContent);
}
```

#### 显示模式切换
- `toggleDisplayMode()` 函数已更新支持HTML实体解码
- 确保在iframe和直接显示模式间切换时正确处理实体

#### 纯文本格式化
- `formatPlainTextEmail()` 函数已更新支持HTML实体解码
- 在格式化纯文本邮件前先进行实体解码

#### HTML内容格式化
- `formatEmailContent()` 函数已更新支持HTML实体解码
- 确保HTML内容在格式化前正确解码

## 使用示例

### 测试功能
在浏览器控制台中运行以下命令来测试HTML实体解码功能：

```javascript
testHtmlEntityDecoding();
```

### 常见使用场景

#### 1. 表格内容解码
```
原始内容：&lt;table&gt;&lt;tr&gt;&lt;td&gt;Cell 1&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt;
解码后：<table><tr><td>Cell 1</td></tr></table>
```

#### 2. 链接内容解码
```
原始内容：&lt;a href=&quot;http://example.com&quot;&gt;Link&lt;/a&gt;
解码后：<a href="http://example.com">Link</a>
```

#### 3. 特殊字符解码
```
原始内容：Company &amp; Partners &quot;Hello&quot;
解码后：Company & Partners "Hello"
```

## 技术实现

### 1. 解码策略
1. **浏览器原生解码**：使用 `textarea.innerHTML` 进行初步解码
2. **手动映射解码**：使用预定义的实体映射表进行补充解码
3. **正则表达式处理**：处理数字和十六进制实体编码

### 2. 安全检查
1. **标签过滤**：移除潜在危险的HTML标签
2. **属性清理**：移除事件处理器和危险协议
3. **字符范围限制**：只允许安全范围内的字符编码

### 3. 性能优化
1. **条件检查**：只对包含HTML实体的内容进行解码
2. **缓存机制**：避免重复解码相同内容
3. **异步处理**：使用 `setTimeout` 避免浏览器卡顿

## 兼容性

### 支持的浏览器
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

### 支持的HTML实体
- ✅ 基本实体：`&lt;`、`&gt;`、`&amp;`、`&quot;`、`&apos;`
- ✅ 空格实体：`&nbsp;`、`&#32;`、`&#160;`
- ✅ 符号实体：`&copy;`、`&reg;`、`&trade;`
- ✅ 标点实体：`&hellip;`、`&mdash;`、`&ndash;`
- ✅ 引号实体：`&lsquo;`、`&rsquo;`、`&ldquo;`、`&rdquo;`
- ✅ 数字实体：`&#数字;`
- ✅ 十六进制实体：`&#x十六进制;`

## 注意事项

### 1. 安全考虑
- 所有解码后的内容都会经过XSS过滤
- 危险的标签和属性会被自动移除
- 建议定期更新安全过滤规则

### 2. 性能考虑
- 大量内容的解码可能影响性能
- 建议对超长内容进行分块处理
- 避免在循环中重复调用解码函数

### 3. 兼容性考虑
- 某些特殊实体可能在不同浏览器中表现不同
- 建议在多个浏览器中测试解码效果
- 对于不支持的实体，会保持原样显示

## 故障排除

### 1. 解码不完整
- 检查是否有新的HTML实体类型需要添加到映射表
- 确认正则表达式是否正确匹配所有实体格式

### 2. 安全过滤过度
- 检查安全过滤规则是否过于严格
- 确认被过滤的内容是否确实存在安全风险

### 3. 性能问题
- 检查是否有大量重复的解码操作
- 考虑添加缓存机制优化性能

## 更新日志

### v1.0.0 (当前版本)
- ✅ 实现基本HTML实体解码功能
- ✅ 添加XSS安全防护
- ✅ 集成到主要邮件处理流程
- ✅ 添加测试和调试功能
