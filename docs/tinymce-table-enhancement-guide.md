# TinyMCE表格增强功能使用指南

## 问题解决

### 1. 表格无法拖动调整行高列宽问题

**原因：**
- TinyMCE默认配置中 `table_grid: false` 禁用了表格网格功能
- 缺少表格调整相关的配置选项

**解决方案：**
已在配置中启用以下选项：
```javascript
table_grid: true,                    // 启用表格网格
table_resize_bars: true,             // 启用表格调整条
table_column_resizing: true,         // 启用列宽调整
table_row_resizing: true,            // 启用行高调整
table_sizing_mode: 'relative',       // 表格尺寸模式
```

### 2. 邮件中表格变形问题

**原因：**
- 不同邮件客户端对CSS支持程度不同
- 表格样式在邮件传输过程中可能被修改
- 缺少邮件兼容的表格样式

**解决方案：**
1. **邮件兼容的表格样式**：添加了专门的CSS样式确保表格在各种邮件客户端中正确显示
2. **表格优化函数**：在发送邮件前自动优化表格HTML结构
3. **Outlook兼容性**：添加了MSO特定的样式属性

## 新增功能

### 1. 表格拖拽调整
- **列宽调整**：鼠标悬停在列边界时可拖拽调整列宽
- **行高调整**：鼠标悬停在行边界时可拖拽调整行高
- **表格网格**：显示表格网格线，便于编辑

### 2. 表格样式选项
- **基础表格**：简洁的表格样式
- **边框表格**：带边框的表格样式
- **条纹表格**：交替行背景色的表格样式

### 3. 邮件优化功能
- **一键优化**：点击"优化表格"按钮自动优化当前表格
- **自动优化**：发送邮件时自动优化所有表格
- **兼容性检查**：确保表格在主流邮件客户端中正确显示

## 使用方法

### 1. 创建表格
1. 在TinyMCE编辑器中点击"表格"按钮
2. 选择表格尺寸（行数和列数）
3. 表格创建后会自动应用基础样式

### 2. 调整表格尺寸
1. **调整列宽**：
   - 将鼠标移动到列边界
   - 当鼠标变为调整光标时，拖拽调整列宽
   
2. **调整行高**：
   - 将鼠标移动到行边界
   - 当鼠标变为调整光标时，拖拽调整行高

### 3. 应用表格样式
1. 选中表格或将光标置于表格内
2. 点击"表格样式"按钮
3. 选择合适的样式

### 4. 优化表格（发送邮件前）
1. 选中需要优化的表格
2. 点击"优化表格"按钮
3. 系统会自动应用邮件兼容的样式

## 技术实现

### 1. 配置更新
```javascript
// 表格配置增强
table_appearance_options: true,      // 启用表格外观选项
table_grid: true,                    // 启用表格网格
table_resize_bars: true,             // 启用调整条
table_column_resizing: true,         // 启用列调整
table_row_resizing: true,            // 启用行调整
table_sizing_mode: 'relative',       // 相对尺寸模式
```

### 2. 邮件兼容样式
```css
table {
    border-collapse: collapse !important;
    mso-table-lspace: 0pt !important;
    mso-table-rspace: 0pt !important;
    table-layout: fixed !important;
}
```

### 3. JavaScript增强
- `TableEnhancement` 类提供表格增强功能
- `optimizeEmailTables()` 函数优化表格HTML
- 自定义TinyMCE插件 `tableenhancement`

## 兼容性

### 支持的邮件客户端
- ✅ Outlook 2016/2019/365
- ✅ Gmail (Web/App)
- ✅ Apple Mail
- ✅ Thunderbird
- ✅ Yahoo Mail
- ✅ 企业邮箱（腾讯企业邮箱、阿里企业邮箱等）

### 支持的浏览器
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## 注意事项

1. **表格宽度**：建议表格最大宽度不超过600px，确保在移动设备上正常显示
2. **单元格内容**：避免在单元格中使用复杂的HTML结构
3. **图片处理**：表格中的图片建议使用相对较小的尺寸
4. **测试发送**：重要邮件发送前建议先发送测试邮件确认显示效果

## 故障排除

### 表格无法调整
1. 确认已启用表格网格功能
2. 检查表格是否被选中
3. 尝试刷新编辑器

### 邮件中表格显示异常
1. 使用"优化表格"功能
2. 检查表格宽度设置
3. 确认单元格内容格式正确

### 编辑器加载问题
1. 检查JavaScript文件是否正确加载
2. 确认TinyMCE版本兼容性
3. 查看浏览器控制台错误信息
