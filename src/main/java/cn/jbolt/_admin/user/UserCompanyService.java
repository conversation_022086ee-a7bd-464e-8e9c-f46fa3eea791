package cn.jbolt._admin.user;

import cn.jbolt.common.model.UserCompany;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.core.service.base.JBoltBaseService;
import cn.jbolt.extend.systemlog.ProjectSystemLogTargetType;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.Date;
import java.util.List;

/**
 * 用户分管公司管理Service
 */
public class UserCompanyService extends JBoltBaseService<UserCompany> {
    private final UserCompany dao = new UserCompany().dao();

    @Override
    protected UserCompany dao() {
        return dao;
    }

    @Override
    protected int systemLogTargetType() {
        return ProjectSystemLogTargetType.NONE.getValue();
    }

    /**
     * 获取用户管理的公司ID列表
     */
    public List<Integer> getCompanyIdsByUserId(Long userId) {
        return Db.query("select company_id from user_company where account_id=? and enable='1'", userId);
    }

    /**
     * 保存用户管理的公司
     */
    public Ret saveUserCompanies(Long userId, String companyIds) {
        if (userId == null) {
            return fail(JBoltMsg.PARAM_ERROR);
        }
        // 删除旧数据
        Db.delete("delete from user_company where account_id=?", userId);

        if (companyIds != null && !companyIds.isEmpty()) {
            String[] ids = companyIds.split(",");
            int sortRank = 1;
            for (String id : ids) {
                UserCompany userCompany = new UserCompany();
                userCompany.setAccountId(userId);
                userCompany.setCompanyId(Integer.parseInt(id));
                userCompany.setSortRank(sortRank++);
                userCompany.setEnable(true);
                userCompany.save();
            }
        }
        refreshUserEmail(userId);
        return SUCCESS;
    }

    private static void refreshUserEmail(Long userId) {
        Db.tx(() -> {
            try {
                // 第一步：删除用户现有的类型为1的邮箱关联
                Db.update("delete from user_email where user_id=? and type=1", userId);

                // 第二步：插入用户与公司客户的邮箱关联
                Db.update("insert into user_email (user_id, email, email_name, type, company_id, client_id) " +
                        "select " + userId + ", c.email, c.name, 1, cc.company_id, c.id " +
                        "from company_client cc " +
                        "         left join client c on cc.client_id = c.id " +
                        "where c.email is not null and cc.company_id in (select company_id " +
                        "                        from user_company " +
                        "                        where account_id = " + userId + " and enable=1);");

                // 事务成功
                return true;
            } catch (Exception e) {
                // 记录异常日志
                LOG.error("更新用户关联邮箱失败: " + e.getMessage(), e);
                // 返回 false 会自动回滚事务
                return false;
            }
        });

    }

    public static void refreshUserCompanyEmail(Integer companyId) {
        Db.tx(() -> {
            try {
                // 第一步：删除用户现有的类型为1的邮箱关联
                Db.update("delete from user_email where company_id=? and type=1", companyId);

                // 第二步：插入用户与公司客户的邮箱关联
                Db.update("insert into user_email (user_id, email, email_name, type, company_id, client_id) " +
                        "select u.account_id, c.email, c.name, 1, cc.company_id, c.id " +
                        "from company_client cc " +
                        "         left join client c on cc.client_id = c.id " +
                        "         left join user_company u on u.company_id = cc.company_id " +
                        "where c.email is not null and cc.company_id = " + companyId + ";");

                // 事务成功
                return true;
            } catch (Exception e) {
                // 记录异常日志
                LOG.error("更新用户关联邮箱失败: " + e.getMessage(), e);
                // 返回 false 会自动回滚事务
                return false;
            }
        });

    }

    /**
     * 获取用户管理的公司列表
     */
    public List<Record> getUserCompanyList(Integer userId) {
        return Db.find("select c.*, uc.id as user_company_id, uc.remark as user_company_remark from company c " +
                "inner join user_company uc on c.id=uc.company_id " +
                "where uc.account_id=? and uc.enable='1' order by uc.sort_rank asc", userId);
    }

    /**
     * 增加用户公司关联
     *
     * @param userId     用户ID
     * @param companyIds 公司ID列表（逗号分隔）
     * @return 操作结果
     */
    public Ret addUserCompanies(Long userId, String companyIds) {
        if (notOk(userId) || notOk(companyIds)) {
            return fail("参数错误");
        }

        try {
            // 删除已存在的关联
            String[] ids = companyIds.split(",");
            for (String companyId : ids) {
                if (notOk(companyId)) {
                    continue;
                }
                // 检查是否已存在且启用
                Record record = Db.findFirst("select * from user_company where account_id=? and company_id=?", userId, companyId);
                if (record != null) {
                    // 如果存在但被禁用，则重新启用
                    if ("0".equals(record.getStr("enable"))) {
                        Db.update("update user_company set enable='1' where id=?", record.getLong("id"));
                    }
                    continue;
                }
                // 不存在则新增
                Record newRecord = new Record();
                newRecord.set("account_id", userId)
                        .set("company_id", companyId)
                        .set("enable", "1")
                        .set("create_time", new Date());
                Db.save("user_company", newRecord);
            }
            refreshUserEmail(userId);
            return success("增加成功");
        } catch (Exception e) {
            LogKit.error("增加用户公司关联失败", e);
            return fail("操作失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户公司关联
     *
     * @param userId     用户ID
     * @param companyIds 公司ID列表（逗号分隔）
     * @return 操作结果
     */
    public Ret removeUserCompanies(Long userId, String companyIds) {
        if (notOk(userId) || notOk(companyIds)) {
            return fail("参数错误");
        }

        try {
            // 软删除（设置enable为0）
            String[] ids = companyIds.split(",");
            for (String companyId : ids) {
                if (notOk(companyId)) {
                    continue;
                }
                Db.update("update user_company set enable='0' where account_id=? and company_id=?", userId, companyId);
            }
            refreshUserEmail(userId);
            return success("删除成功");
        } catch (Exception e) {
            LogKit.error("删除用户公司关联失败", e);
            return fail("操作失败：" + e.getMessage());
        }
    }
} 