package cn.jbolt.admin.client;

import cn.jbolt._admin.permission.PermissionKey;
import cn.jbolt.admin.company.CompanyService;
import cn.jbolt.common.config.JBoltUploadFolder;
import cn.jbolt.common.model.Client;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.core.controller.base.JBoltBaseController;
import cn.jbolt.core.permission.CheckPermission;
import cn.jbolt.core.permission.JBoltAdminAuthInterceptor;
import cn.jbolt.core.permission.UnCheckIfSystemAdmin;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.core.Path;
import com.jfinal.core.paragetter.Para;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.plugin.activerecord.tx.Tx;
import com.jfinal.upload.UploadFile;

import java.util.List;

import org.apache.commons.lang3.StringUtils;

/**
 * 客户信息管理
 *
 * @ClassName: ClientAdminController
 * @author: 总管理
 * @date: 2024-05-15 12:59
 */
@CheckPermission(PermissionKey.ADMIN_CLIENT)
@UnCheckIfSystemAdmin
@Before(JBoltAdminAuthInterceptor.class)
@Path(value = "/admin/client", viewPath = "/_view/admin/client")
public class ClientAdminController extends JBoltBaseController {

    @Inject
    private ClientService service;
    @Inject
    private CompanyService companyService;

    /**
     * 首页
     */
    public void index() {
        Integer companyId = getInt("companyId");
        setAttr("companyId", companyId);
        if (companyId != null) {
            set("company", companyService.findById(companyId));
        }
        render("index.html");
    }

    /**
     * 数据源
     */
    public void datas() {
        renderJsonData(service.getAdminDatas(getPageNumber(), getPageSize(), getKeywords(), getSortColumn("sort_rank"),
                getSortType("asc"), get("name"), get("country"), getBoolean("enable"), getInt("companyId")));
    }

    /**
     * 新增
     */
    public void add() {
        Integer companyId = getInt("companyId");
        setAttr("companyId", companyId);
        render("add.html");
    }

    /**
     * 保存
     */
    @Before(Tx.class)
    public void save(@Para("client") Client client) {
        Integer companyId = getInt("companyId");
        setAttr("companyId", companyId);
        renderJson(service.save(client, companyId));
    }

    /**
     * 编辑
     */
    public void edit() {
        Integer companyId = getInt("companyId");
        setAttr("companyId", companyId);
        Client client = service.findById(getInt(0));
        if (client == null) {
            renderFail(JBoltMsg.DATA_NOT_EXIST);
            return;
        }
        set("client", client);
        render("edit.html");
    }

    /**
     * 更新
     */
    @Before(Tx.class)
    public void update(@Para("client") Client client) {
        Integer companyId = getInt("companyId");
        setAttr("companyId", companyId);
        renderJson(service.update(client, companyId));
    }

    /**
     * 删除
     */
    @Before(Tx.class)
    public void delete() {
        Integer companyId = getInt("companyId");
        Integer id = getInt("id");
        if (id == null) {
            renderJsonFail("请选择要删除的数据");
            return;
        }
        renderJson(service.deleteById(id, companyId));
    }

    /**
     * 排序 上移
     */
    @Before(Tx.class)
    public void up() {
        renderJson(service.up(getInt(0)));
    }

    /**
     * 排序 下移
     */
    @Before(Tx.class)
    public void down() {
        renderJson(service.down(getInt(0)));
    }

    /**
     * 排序 初始化
     */
    @Before(Tx.class)
    public void initSortRank() {
        renderJson(service.initSortRank());
    }

    /**
     * 进入import_excel.html
     */
    public void initImportExcel() {
        render("import_excel.html");
    }

    /**
     * 下载导入模板
     */
    public void downloadTpl() {
        renderBytesToExcelXlsFile(service.getImportExcelTpl().setFileName("客户信息导入模板"));
    }

    /**
     * 执行导入excel
     */
    public void importExcel() {
        String uploadPath = JBoltUploadFolder.todayFolder(JBoltUploadFolder.IMPORT_EXCEL_TEMP_FOLDER);
        UploadFile file = getFile("file", uploadPath);
        if (notExcel(file)) {
            renderJsonFail("请上传excel文件");
            return;
        }
        renderJson(service.importExcel(file.getFile()));
    }

    /**
     * 执行导出excel 根据查询form表单
     */
    public void exportExcelByForm() {
        Page<Client> pageData = service.getAdminDatas(getPageNumber(), getPageSize(), getKeywords(),
                getSortColumn("sort_rank"), getSortType("asc"), get("name"), get("country"), getBoolean("enable"),
                getInt("companyId"));
        if (notOk(pageData.getTotalRow())) {
            renderJsonFail("无有效数据导出");
            return;
        }
        renderBytesToExcelXlsxFile(service.exportExcel(pageData.getList()).setFileName("客户信息"));
    }

    /**
     * 执行导出excel 根据表格选中数据
     */
    public void exportExcelByCheckedIds() {
        String ids = get("ids");
        if (notOk(ids)) {
            renderJsonFail("未选择有效数据，无法导出");
            return;
        }
        List<Client> datas = service.getListByIds(ids);
        if (notOk(datas)) {
            renderJsonFail("无有效数据导出");
            return;
        }
        renderBytesToExcelXlsxFile(service.exportExcel(datas).setFileName("客户信息"));
    }

    /**
     * 执行导出excel 所有数据
     */
    public void exportExcelAll() {
        List<Client> datas = service.findAll();
        if (notOk(datas)) {
            renderJsonFail("无有效数据导出");
            return;
        }
        renderBytesToExcelXlsxFile(service.exportExcel(datas).setFileName("客户信息"));
    }

    /**
     * 切换启用状态
     */
    @Before(Tx.class)
    public void toggleEnable() {
        renderJson(service.toggleEnable(getInt(0)));
    }

    public void getClientByEmail() {
        String email = getPara("email");
        if (StringUtils.isBlank(email)) {
            renderFail("邮箱地址不能为空");
            return;
        }

        Record client = Db.findFirst("SELECT * FROM client WHERE email=?", email);
        renderJsonData(client);
    }

    /**
     * 保存客户联系人信息
     */
    public void saveClient() {
        String id = getPara("id");
        String companyId = getPara("company_id");
        String name = getPara("name");
        String email = getPara("email");
        String mobile = getPara("mobile");
        String position = getPara("position");

        if (StringUtils.isBlank(companyId)) {
            renderFail("所属公司不能为空");
            return;
        }

        if (StringUtils.isBlank(name)) {
            renderFail("联系人姓名不能为空");
            return;
        }

        if (StringUtils.isBlank(email)) {
            renderFail("邮箱地址不能为空");
            return;
        }

        boolean success = false;
        Record client = null;

        if (StringUtils.isNotBlank(id)) {
            // 更新
            client = Db.findFirst("SELECT * FROM client WHERE id=?", id);
            if (client != null) {
                client.set("company_id", companyId);
                client.set("name", name);
                client.set("email", email);
                client.set("mobile", mobile);
                client.set("position", position);
                success = Db.update("client", client);
            }
        } else {
            // 查询是否已存在
            client = Db.findFirst("SELECT * FROM client WHERE email=?", email);
            if (client != null) {
                client.set("company_id", companyId);
                client.set("name", name);
                client.set("mobile", mobile);
                client.set("position", position);
                success = Db.update("client", client);
            } else {
                // 新增
                client = new Record();
                client.set("company_id", companyId);
                client.set("name", name);
                client.set("email", email);
                client.set("mobile", mobile);
                client.set("position", position);
                success = Db.save("client", client);
            }
        }

        if (success) {
            renderJson(Ret.ok("msg", "保存成功").set("data", client));
        } else {
            renderJson(Ret.fail("保存失败"));
        }
    }

}
