package cn.jbolt.admin.company;

import static cn.jbolt.core.util.JBoltStringUtil.getDomain;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.validator.routines.EmailValidator;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jfinal.aop.Aop;
import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.Okv;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import cn.jbolt._admin.user.UserCompanyService;
import cn.jbolt.admin.emails.EmailNameCache;
import cn.jbolt.common.model.Client;
import cn.jbolt.common.model.Company;
import cn.jbolt.common.model.CompanyClient;
import cn.jbolt.common.model.UserCompany;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.core.db.sql.Sql;
import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.core.poi.excel.JBoltExcel;
import cn.jbolt.core.poi.excel.JBoltExcelHeader;
import cn.jbolt.core.poi.excel.JBoltExcelSheet;
import cn.jbolt.core.poi.excel.JBoltExcelUtil;
import cn.jbolt.core.service.base.JBoltBaseService;
import cn.jbolt.extend.systemlog.ProjectSystemLogTargetType;

/**
 * 公司信息管理
 *
 * @ClassName: CompanyService
 * @author: 总管理
 * @date: 2024-05-15 13:05
 */
public class CompanyService extends JBoltBaseService<Company> {
    private final Company dao = new Company().dao();

    @Override
    protected Company dao() {
        return dao;
    }

    @Override
    protected int systemLogTargetType() {
        return ProjectSystemLogTargetType.NONE.getValue();
    }

    /**
     * 后台管理数据查询
     *
     * @param pageNumber   第几页
     * @param pageSize     每页几条数据
     * @param keywords     关键词
     * @param sortColumn   排序列名
     * @param sortType     排序方式 asc desc
     * @param name         公司名称
     * @param label        标签
     * @param industry     行业
     * @param enable       启用/禁用
     * @param userId       用户ID
     * @param assignStatus 分配状态
     * @return
     */
    public Page<Company> getAdminDatas(int pageNumber, int pageSize, String keywords, String sortColumn, String sortType, String name, String label, String industry, Boolean enable, Long userId, String assignStatus) {
        //创建sql对象
        Sql sql = selectSql().page(pageNumber, pageSize);
        //sql条件处理
        sql.eq("name", name);
        sql.eq("label", label);
        sql.eq("industry", industry);
        sql.eqBooleanToChar("enable", enable);

        // 处理分配状态查询
        if (userId != null && assignStatus != null) {
            if ("1".equals(assignStatus)) {
                // 已分配
                Sql subSql = Sql.me(this.dbType)
                        .select("1")
                        .from("user_company", "uc");
                subSql.eqInnerColumn("uc.company_id", "company.id")
                        .eq("uc.account_id", userId)
                        .eq("uc.enable", "1");
                sql.exists(subSql);
            } else if ("0".equals(assignStatus)) {
                // 未分配
                Sql subSql = Sql.me(this.dbType)
                        .select("1")
                        .from("user_company", "uc");
                subSql.eqInnerColumn("uc.company_id", "company.id")
                        .eq("uc.account_id", userId)
                        .eq("uc.enable", "1");
                sql.notExists(subSql);
            }
        }

        //关键词模糊查询
        sql.likeMulti(keywords, "name", "nick_name", "domain", "official_website", "country", "label", "industry", "title_website", "site_description", "related_technologies", "keyword", "company_address", "remark");
        //排序
        sql.orderBy(sortColumn, sortType);
        LogKit.info("SQL查询条件: {}", sql.toString());
        Page<Company> page = paginate(sql);
        LogKit.info("查询结果总数: {}, 当前页数据量: {}", page.getTotalRow(), page.getList().size());

        // 标记是否已分配
        if (userId != null && page.getList() != null) {
            List<Integer> assignedIds = Db.query("select company_id from user_company where account_id=? and enable='1'", userId);
            for (Company company : page.getList()) {
                company.put("isAssigned", assignedIds.contains(company.getId()));
            }
        }

        return page;
    }

    /**
     * 后台管理数据查询 - 查询登录用户所拥有的公司
     *
     * @param pageNumber 第几页
     * @param pageSize   每页几条数据
     * @param keywords   关键词
     * @param sortColumn 排序列名
     * @param sortType   排序方式 asc desc
     * @param name       公司名称
     * @param label      标签
     * @param industry   行业
     * @param enable     启用/禁用
     * @return 分页数据
     */
    public Page<Company> getAdminDatas(int pageNumber, int pageSize, String keywords, String sortColumn, String sortType, String name, String label, String industry, Boolean enable) {
        //创建sql对象
        Sql sql = selectSql().page(pageNumber, pageSize);
        //sql条件处理
        sql.eq("name", name);
        sql.eq("label", label);
        sql.eq("industry", industry);
        sql.eqBooleanToChar("enable", enable);

        // 只查询当前用户拥有的公司
        Long userId = JBoltUserKit.getUserId();
        Sql subSql = Sql.me(this.dbType)
                .select("1")
                .from("user_company", "uc");
        subSql.eqInnerColumn("uc.company_id", "company.id")
                .eq("uc.account_id", userId)
                .eq("uc.enable", "1");
        sql.exists(subSql);

        //关键词模糊查询
        sql.likeMulti(keywords, "name", "nick_name", "domain", "official_website", "country", "label", "industry", "title_website", "site_description", "related_technologies", "keyword", "company_address", "remark");
        //排序
        sql.orderBy(sortColumn, sortType);
        LogKit.info("SQL查询条件: {}", sql.toString());
        Page<Company> page = paginate(sql);
        LogKit.info("查询结果总数: {}, 当前页数据量: {}", page.getTotalRow(), page.getList().size());

        return page;
    }

    /**
     * 保存
     *
     * @param company
     * @return
     */
    public Ret save(Company company) {
        if (company == null || isOk(company.getId())) {
            return fail(JBoltMsg.PARAM_ERROR);
        }
        company.setSortRank(getNextSortRank());
        boolean success = company.save();
        if (success) {
            //添加日志
            //addSaveSystemLog(company.getId(), JBoltUserKit.getUserId(), company.getName());
        }
        return ret(success);
    }

    /**
     * 更新
     *
     * @param company
     * @return
     */
    public Ret update(Company company) {
        if (company == null || notOk(company.getId())) {
            return fail(JBoltMsg.PARAM_ERROR);
        }
        //更新时需要判断数据存在
        Company dbCompany = findById(company.getId());
        if (dbCompany == null) {
            return fail(JBoltMsg.DATA_NOT_EXIST);
        }
        boolean success = company.update();
        if (success) {
            //添加日志
            //addUpdateSystemLog(company.getId(), JBoltUserKit.getUserId(), company.getName());
        }
        return ret(success);
    }

    /**
     * 删除数据后执行的回调
     *
     * @param company 要删除的model
     * @param kv      携带额外参数一般用不上
     * @return
     */
    @Override
    protected String afterDelete(Company company, Kv kv) {
        //addDeleteSystemLog(company.getId(), JBoltUserKit.getUserId(),company.getName());
        return null;
    }

    /**
     * 检测是否可以删除
     *
     * @param company model
     * @param kv      携带额外参数一般用不上
     * @return
     */
    @Override
    public String checkInUse(Company company, Kv kv) {
        //这里用来覆盖 检测是否被其它表引用
        return null;
    }

    /**
     * 上移
     *
     * @param id
     * @return
     */
    public Ret up(Integer id) {
        Company company = findById(id);
        if (company == null) {
            return fail("数据不存在或已被删除");
        }
        Integer rank = company.getSortRank();
        if (rank == null || rank <= 0) {
            return fail("顺序需要初始化");
        }
        if (rank == 1) {
            return fail("已经是第一个");
        }
        Company upCompany = findFirst(Okv.by("sort_rank", rank - 1));
        if (upCompany == null) {
            return fail("顺序需要初始化");
        }
        upCompany.setSortRank(rank);
        company.setSortRank(rank - 1);

        upCompany.update();
        company.update();
        return SUCCESS;
    }

    /**
     * 下移
     *
     * @param id
     * @return
     */
    public Ret down(Integer id) {
        Company company = findById(id);
        if (company == null) {
            return fail("数据不存在或已被删除");
        }
        Integer rank = company.getSortRank();
        if (rank == null || rank <= 0) {
            return fail("顺序需要初始化");
        }
        int max = getCount();
        if (rank == max) {
            return fail("已经是最后已一个");
        }
        Company upCompany = findFirst(Okv.by("sort_rank", rank + 1));
        if (upCompany == null) {
            return fail("顺序需要初始化");
        }
        upCompany.setSortRank(rank);
        company.setSortRank(rank + 1);

        upCompany.update();
        company.update();
        return SUCCESS;
    }

    /**
     * 初始化排序
     */
    public Ret initSortRank() {
        List<Company> allList = findAll();
        if (!allList.isEmpty()) {
            for (int i = 0; i < allList.size(); i++) {
                allList.get(i).setSortRank(i + 1);
            }
            batchUpdate(allList);
        }
        //添加日志
        //addUpdateSystemLog(null, JBoltUserKit.getUserId(), "所有数据", "的顺序:初始化所有");
        return SUCCESS;
    }

    /**
     * 生成excel导入使用的模板
     *
     * @return
     */
    public JBoltExcel getImportExcelTpl() {
        return JBoltExcel
                //创建
                .create()
                .setSheets(
                        JBoltExcelSheet.create()
                                //设置列映射 顺序 标题名称 不处理别名
                                .setHeaders(1, false,
                                        JBoltExcelHeader.create("姓氏", 15),
                                        JBoltExcelHeader.create("公司名称", 15),
                                        JBoltExcelHeader.create("域名", 15),
                                        JBoltExcelHeader.create("官网", 15),
                                        JBoltExcelHeader.create("国家", 15),
                                        JBoltExcelHeader.create("标签", 15),
                                        JBoltExcelHeader.create("员工数", 15),
                                        JBoltExcelHeader.create("行业", 15),
                                        JBoltExcelHeader.create("电话", 15),
                                        JBoltExcelHeader.create("其他社交", 15),
                                        JBoltExcelHeader.create("创立时间", 15),
                                        JBoltExcelHeader.create("公司介绍", 15),
                                        JBoltExcelHeader.create("谷歌搜录", 15),
                                        JBoltExcelHeader.create("网站标题", 15),
                                        JBoltExcelHeader.create("网站描述", 15),
                                        JBoltExcelHeader.create("相关技术", 15),
                                        JBoltExcelHeader.create("关键词", 15),
                                        JBoltExcelHeader.create("公司地址", 15),
                                        JBoltExcelHeader.create("备注信息", 15)
                                )
                );
    }

    /**
     * 读取excel文件
     *
     * @param file
     * @return
     */
    public Ret importExcel(File file) {
        StringBuilder errorMsg = new StringBuilder();
        JBoltExcel jBoltExcel = JBoltExcel
                //从excel文件创建JBoltExcel实例
                .from(file)
                //设置工作表信息
                .setSheets(
                        JBoltExcelSheet.create()
                                //设置列映射 顺序 标题名称
                                .setHeaders(1,
                                        JBoltExcelHeader.create("name", "公司名称"),
                                        JBoltExcelHeader.create("domain", "域名"),
                                        JBoltExcelHeader.create("official_website", "官网"),
                                        JBoltExcelHeader.create("country", "国家"),
                                        JBoltExcelHeader.create("label", "标签"),
                                        JBoltExcelHeader.create("number_of_employees", "员工数"),
                                        JBoltExcelHeader.create("industry", "行业"),
                                        JBoltExcelHeader.create("phone", "电话"),
                                        JBoltExcelHeader.create("other_social", "其他社交"),
                                        JBoltExcelHeader.create("founded", "创立时间"),
                                        JBoltExcelHeader.create("company_introduction", "公司介绍"),
                                        JBoltExcelHeader.create("google_search", "谷歌搜录"),
                                        JBoltExcelHeader.create("title_website", "网站标题"),
                                        JBoltExcelHeader.create("site_description", "网站描述"),
                                        JBoltExcelHeader.create("related_technologies", "相关技术"),
                                        JBoltExcelHeader.create("keyword", "关键词"),
                                        JBoltExcelHeader.create("company_address", "公司地址"),
                                        JBoltExcelHeader.create("remark", "备注信息")
                                )
                                //从第三行开始读取
                                .setDataStartRow(2)
                );
        //从指定的sheet工作表里读取数据
        List<Company> companys = JBoltExcelUtil.readModels(jBoltExcel, 1, Company.class, errorMsg);
        if (notOk(companys)) {
            if (errorMsg.length() > 0) {
                return fail(errorMsg.toString());
            } else {
                return fail(JBoltMsg.DATA_IMPORT_FAIL_EMPTY);
            }
        }
        //执行批量操作
        boolean success = tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                batchSave(companys);
                return true;
            }
        });

        if (!success) {
            return fail(JBoltMsg.DATA_IMPORT_FAIL);
        }
        return SUCCESS;
    }

    /**
     * 生成要导出的Excel
     *
     * @return
     */
    public JBoltExcel exportExcel(List<Company> datas) {
        return JBoltExcel
                //创建
                .create()
                //设置工作表
                .setSheets(
                        //设置工作表 列映射 顺序 标题名称
                        JBoltExcelSheet
                                .create()
                                //表头映射关系
                                .setHeaders(1,
                                        JBoltExcelHeader.create("id", "ID", 15),
                                        JBoltExcelHeader.create("surname", "姓氏", 15),
                                        JBoltExcelHeader.create("name", "公司名称", 15),
                                        JBoltExcelHeader.create("domain", "域名", 15),
                                        JBoltExcelHeader.create("official_website", "官网", 15),
                                        JBoltExcelHeader.create("country", "国家", 15),
                                        JBoltExcelHeader.create("label", "标签", 15),
                                        JBoltExcelHeader.create("number_of_employees", "员工数", 15),
                                        JBoltExcelHeader.create("industry", "行业", 15),
                                        JBoltExcelHeader.create("phone", "电话", 15),
                                        JBoltExcelHeader.create("other_social", "其他社交", 15),
                                        JBoltExcelHeader.create("founded", "创立时间", 15),
                                        JBoltExcelHeader.create("company_introduction", "公司介绍", 15),
                                        JBoltExcelHeader.create("google_search", "谷歌搜录", 15),
                                        JBoltExcelHeader.create("title_website", "网站标题", 15),
                                        JBoltExcelHeader.create("site_description", "网站描述", 15),
                                        JBoltExcelHeader.create("related_technologies", "相关技术", 15),
                                        JBoltExcelHeader.create("keyword", "关键词", 15),
                                        JBoltExcelHeader.create("company_address", "公司地址", 15),
                                        JBoltExcelHeader.create("remark", "备注信息", 15),
                                        JBoltExcelHeader.create("create_time", "创建时间", 15),
                                        JBoltExcelHeader.create("create_user_id", "创建人", 15)
                                )
                                //设置导出的数据源 来自于数据库查询出来的Model List
                                .setModelDatas(2, datas)
                );
    }

    /**
     * toggle操作执行后的回调处理
     */
    @Override
    protected String afterToggleBoolean(Company company, String column, Kv kv) {
        //addUpdateSystemLog(company.getId(), JBoltUserKit.getUserId(), company.getName(),"的字段["+column+"]值:"+company.get(column));
        /**
         switch(column){
         case "enable":
         break;
         }
         */
        return null;
    }

    public List<Company> countyOptions(String q) {
        return dao.find("select distinct country country from company where country like ? order by country", "%" + q + "%");
    }

    public List<Company> companyOptions(String q) {
        return dao.find("select id,name from company where name like ? order by name", "%" + q + "%");
    }

    public Page<Record> getEmailsFromEmailSet(Integer pageNumber, Integer pageSize, String q, Set<String> emailSet) {
        Sql emailIdSql = Sql.me(this.dbType).select("email_id").from("emails_email").in("email", emailSet);
        Sql getEmailsFromEmailSetSql = Sql.me(this.dbType).page(pageNumber, pageSize).select("*").from("emails").inSql("id", emailIdSql).orderBy("send_date", true);
        return paginateRecord(getEmailsFromEmailSetSql);
    }

    public Ret syncCompany() {
        List<Record> records = Db.use("osclub").find("select id, " +
                "       jc, " +
                "       mc, " +
                "       gsmc, " +
                "       gswz, " +
                "       dh, " +
                "       dz, " +
                "       gb, " +
                "       concat(if(bz1 is null, '', concat(bz1, '\\n')), " +
                "              if(bz2 is null, '', concat(bz2, '\\n')), " +
                "              if(bz3 is null, '', concat(bz3, '\\n')), " +
                "              if(bz4 is null, '', concat(bz4, '\\n')), " +
                "              if(bz5 is null, '', concat(bz5, '\\n')), " +
                "              if(bz6 is null, '', concat(bz6, '\\n')), " +
                "              if(bz7 is null, '', concat(bz7, '\\n'))) bz " +
                "from kh;");
        List<Record> khLxrRecords = Db.use("osclub").find("select * from vw_khlxr where yx not like '%@linstone.cn%' and yx not like '%@theolympiastone.com%'");
        Map<Integer, List<Map<String, String>>> khlxrMap = Maps.newHashMap();
        // 新增：记录所有邮箱对应的公司列表，用于跨公司客户关联
        Map<String, Set<Integer>> emailToCompaniesMap = Maps.newHashMap();
        for (Record khLxrRecord : khLxrRecords) {
            Integer id = khLxrRecord.getInt("id");
            String mc = khLxrRecord.getStr("mc");
            String yx = khLxrRecord.getStr("yx");
            String fd = khLxrRecord.getStr("fd");
            if (StringUtils.isEmpty(yx)) {
                continue;
            }
            String[] yxSplit = yx.split("[,;]");
            for (String yxSingle : yxSplit) {
                yxSingle = yxSingle.trim(); // 去除空格
                if (!EmailValidator.getInstance().isValid(yxSingle)) {
                    continue;
                }
                Map<String, String> map = Maps.newHashMap();
                map.put("mc", mc);
                map.put("yx", yxSingle);
                map.put("fd", fd);
                List<Map<String, String>> mapList = khlxrMap.computeIfAbsent(id, k -> Lists.newArrayList());
                mapList.add(map);

                // 记录邮箱对应的公司ID
                emailToCompaniesMap.computeIfAbsent(yxSingle, k -> new HashSet<>()).add(id);
            }
        }
        List<Company> companies = dao.findAll();
        Map<Integer, Company> idCompanyMap = companies.stream().collect(Collectors.toMap(Company::getOsclubId, Function.identity()));
        Company company;
        for (Record record : records) {
            Integer id = record.getInt("id");
            if (idCompanyMap.containsKey(id)) {
                company = idCompanyMap.get(id);
                company.setNickName(record.getStr("jc"));
                company.setName(record.getStr("gsmc"));
                String gswz = record.getStr("gswz");
                String domain = getDomain(gswz);
                company.setDomain(domain);
                company.setOfficialWebsite(gswz);
                company.setCountry(record.getStr("gb"));
                company.setRemark(record.getStr("bz"));
                company.setPhone(record.getStr("dh"));
                company.setCompanyAddress(record.getStr("dz"));
                company.setOsclubId(id);
                company.update();
            } else {
                company = new Company();
                company.setNickName(record.getStr("jc"));
                company.setName(record.getStr("gsmc"));
                String gswz = record.getStr("gswz");
                String domain = getDomain(gswz);
                company.setDomain(domain);
                company.setOfficialWebsite(gswz);
                company.setCountry(record.getStr("gb"));
                company.setRemark(record.getStr("bz"));
                company.setPhone(record.getStr("dh"));
                company.setCompanyAddress(record.getStr("dz"));
                company.setOsclubId(id);
                company.save();
            }
            Integer companyId = company.getId();
            Integer osclubId = company.getOsclubId();
            
            // 处理客户信息的智能同步逻辑
            if (khlxrMap.containsKey(osclubId)) {
                List<Map<String, String>> mapList = khlxrMap.get(osclubId);
                
                // 获取当前公司关联的所有客户邮箱
                List<String> existingEmails = Db.query("select email from client where id in (select client_id from company_client where company_id = ?)", companyId);
                Set<String> existingEmailSet = new HashSet<>(existingEmails);
                Set<String> processedEmails = new HashSet<>();
                
                for (Map<String, String> map : mapList) {
                    String mc = map.get("mc");
                    String yx = map.get("yx");
                    String fd = map.get("fd");
                    
                    if (StringUtils.isEmpty(yx)) {
                        continue;
                    }
                    
                    processedEmails.add(yx);
                    
                    // 查找本地是否已存在该邮箱的客户
                    Client existingClient = new Client().dao().findFirst("select * from client where email = ?", yx);
                    
                    if (existingClient != null) {
                        // 客户已存在，检查并补齐空字段
                        boolean needUpdate = false;
                        if (StringUtils.isEmpty(existingClient.getName()) && StringUtils.isNotEmpty(mc)) {
                            existingClient.setName(mc);
                            needUpdate = true;
                        }
                        if (StringUtils.isEmpty(existingClient.getNickname()) && StringUtils.isNotEmpty(fd)) {
                            existingClient.setNickname(fd);
                            needUpdate = true;
                        }
                        if (StringUtils.isEmpty(existingClient.getCatalogNumber()) && StringUtils.isNotEmpty(fd)) {
                            existingClient.setCatalogNumber(fd);
                            needUpdate = true;
                        }
                        if (StringUtils.isEmpty(existingClient.getCountry()) && StringUtils.isNotEmpty(company.getCountry())) {
                            existingClient.setCountry(company.getCountry());
                            needUpdate = true;
                        }
                        if (StringUtils.isEmpty(existingClient.getRemark()) && StringUtils.isNotEmpty(company.getName())) {
                            existingClient.setRemark(company.getName());
                            needUpdate = true;
                        }
                        
                        if (needUpdate) {
                            existingClient.update();
                        }
                        
                        // 确保公司客户关联关系存在
                        CompanyClient existingRelation = new CompanyClient().dao().findFirst(
                            "select * from company_client where company_id = ? and client_id = ?", 
                            companyId, existingClient.getId()
                        );
                        if (existingRelation == null) {
                            new CompanyClient().setCompanyId(companyId).setClientId(existingClient.getId()).save();
                        }
                    } else {
                        // 客户不存在，新建客户
                        Client newClient = new Client()
                            .setName(mc)
                            .setEmail(yx)
                            .setCountry(company.getCountry())
                            .setRemark(company.getName())
                            .setNickname(fd)
                            .setCatalogNumber(fd);
                        newClient.save();
                        new CompanyClient().setCompanyId(companyId).setClientId(newClient.getId()).save();
                    }
                }
                
                // 移除不再关联的客户关系（但不删除客户本身）
                for (String existingEmail : existingEmailSet) {
                    if (!processedEmails.contains(existingEmail)) {
                        Client clientToRemove = new Client().dao().findFirst("select * from client where email = ?", existingEmail);
                        if (clientToRemove != null) {
                            Db.delete("delete from company_client where company_id = ? and client_id = ?", companyId, clientToRemove.getId());
                        }
                    }
                }
            } else {
                // 如果khlxrMap中没有该公司的数据，移除所有关联关系（但不删除客户）
                Db.delete("delete from company_client where company_id = ?", companyId);
            }
            UserCompanyService.refreshUserCompanyEmail(companyId);
        }

        // 处理跨公司客户关联：确保同一邮箱的客户在所有相关公司中都有关联
        processEmailCrossCompanyAssociation(emailToCompaniesMap, idCompanyMap);

        Aop.get(EmailNameCache.class).refreshCache();
        return Ret.ok();
    }

    /**
     * 处理跨公司客户关联：确保同一邮箱的客户在所有相关公司中都有关联
     *
     * @param emailToCompaniesMap 邮箱到公司ID集合的映射
     * @param idCompanyMap 公司ID到公司对象的映射
     */
    private void processEmailCrossCompanyAssociation(Map<String, Set<Integer>> emailToCompaniesMap,
                                                   Map<Integer, Company> idCompanyMap) {
        for (Map.Entry<String, Set<Integer>> entry : emailToCompaniesMap.entrySet()) {
            String email = entry.getKey();
            Set<Integer> companyOsclubIds = entry.getValue();

            // 如果该邮箱只关联一个公司，跳过
            if (companyOsclubIds.size() <= 1) {
                continue;
            }

            // 查找该邮箱对应的客户
            Client existingClient = new Client().dao().findFirst("select * from client where email = ?", email);
            if (existingClient == null) {
                continue; // 客户不存在，跳过
            }

            // 为该客户在所有相关公司中建立关联关系
            for (Integer osclubId : companyOsclubIds) {
                Company company = idCompanyMap.get(osclubId);
                if (company == null) {
                    continue; // 公司不存在，跳过
                }

                // 检查是否已存在关联关系
                CompanyClient existingRelation = new CompanyClient().dao().findFirst(
                    "select * from company_client where company_id = ? and client_id = ?",
                    company.getId(), existingClient.getId()
                );

                // 如果关联关系不存在，则创建
                if (existingRelation == null) {
                    new CompanyClient()
                        .setCompanyId(company.getId())
                        .setClientId(existingClient.getId())
                        .save();
                    System.out.println("为客户 " + email + " 在公司 " + company.getName() + "(ID:" + company.getId() + ") 中建立关联关系");
                }
            }
        }
    }

    public Ret syncUserCompany() {
        try {
            // 1. 查询osclub数据库的kh表，获取客户信息（包含ywy字段）
            List<Record> khRecords = Db.use("osclub").find(
                "select id, jc, gsmc, ywy from kh where ywy is not null and ywy <> ''"
            );

            // 2. 查询本地jbolt_user表，获取用户邮箱信息
            List<Record> userRecords = Db.find(
                "select id, username, name, email from jb_user where email is not null and email <> '' and enable = '1'"
            );

            // 3. 获取本地公司信息，建立osclub_id到company_id的映射
            Map<Integer, Integer> osclubIdToCompanyIdMap = new HashMap<>();
            List<Record> companyRecords = Db.find("select id, osclub_id from company where osclub_id is not null");
            for (Record company : companyRecords) {
                Integer osclubId = company.getInt("osclub_id");
                Integer companyId = company.getInt("id");
                if (osclubId != null && companyId != null) {
                    osclubIdToCompanyIdMap.put(osclubId, companyId);
                }
            }

            int matchCount = 0;
            int newRelationCount = 0;

            // 4. 处理邮箱匹配逻辑
            for (Record khRecord : khRecords) {
                Integer khId = khRecord.getInt("id");
                String ywy = khRecord.getStr("ywy");
                
                // 查找对应的公司ID
                Integer companyId = osclubIdToCompanyIdMap.get(khId);
                if (companyId == null) {
                    continue; // 如果没有对应的公司，跳过
                }

                // 分割kh表的ywy字段（可能包含多个邮箱）
                if (StringUtils.isEmpty(ywy)) {
                    continue;
                }
                String[] khEmails = ywy.split("[,;]");
                Set<String> khEmailSet = new HashSet<>();
                for (String email : khEmails) {
                    email = email.trim();
                    if (StringUtils.isNotEmpty(email) && EmailValidator.getInstance().isValid(email)) {
                        khEmailSet.add(email.toLowerCase());
                    }
                }

                if (khEmailSet.isEmpty()) {
                    continue;
                }

                // 与用户邮箱进行匹配
                for (Record userRecord : userRecords) {
                    Long userId = userRecord.getLong("id");
                    String userEmail = userRecord.getStr("email");
                    
                    if (StringUtils.isEmpty(userEmail)) {
                        continue;
                    }

                    // 分割用户邮箱字段（可能包含多个邮箱）
                    String[] userEmails = userEmail.split("[,;]");
                    Set<String> userEmailSet = new HashSet<>();
                    for (String email : userEmails) {
                        email = email.trim();
                        if (StringUtils.isNotEmpty(email) && EmailValidator.getInstance().isValid(email)) {
                            userEmailSet.add(email.toLowerCase());
                        }
                    }

                    // 检查两个邮箱集合是否有交集
                    boolean hasIntersection = false;
                    for (String khEmail : khEmailSet) {
                        if (userEmailSet.contains(khEmail)) {
                            hasIntersection = true;
                            break;
                        }
                    }

                    if (hasIntersection) {
                        matchCount++;
                        
                        // 检查是否已存在用户公司关联关系
                        Record existingRelation = Db.findFirst(
                            "select * from user_company where account_id = ? and company_id = ?", 
                            userId, companyId
                        );

                        if (existingRelation == null) {
                            // 创建新的用户公司关联关系
                            UserCompany userCompany = new UserCompany();
                            userCompany.setAccountId(userId);
                            userCompany.setCompanyId(companyId);
                            userCompany.setEnable(true);
                            userCompany.setSortRank(1);
                            userCompany.setRemark("系统自动同步创建 - " + khRecord.getStr("jc"));
                            userCompany.save();
                            newRelationCount++;
                        } else if (!existingRelation.getBoolean("enable")) {
                            // 如果关联关系存在但被禁用，重新启用
                            Db.update(
                                "update user_company set enable = '1', update_time = now(), remark = ? where id = ?",
                                "系统自动同步启用 - " + khRecord.getStr("jc"),
                                existingRelation.getInt("id")
                            );
                            newRelationCount++;
                        }
                        
                        // 刷新用户关联邮箱
                        UserCompanyService.refreshUserCompanyEmail(companyId);
                    }
                }
            }

            return Ret.ok("msg", "同步完成")
                .set("totalMatches", matchCount)
                .set("newRelations", newRelationCount);
                
        } catch (Exception e) {
            LogKit.error("同步用户公司关联失败", e);
            return Ret.fail("同步失败：" + e.getMessage());
        }
    }

    public Integer findIdFromEmail(String email) {
        return Db.queryInt("select company_id as id from company_client where client_id in (select id from client where email=?) limit 1", email);
    }

    public List<String> getUserRelatedEmails(Long userId) {
        return Db.query("select email from client where id in (select client_id from company_client where company_id in (select company_id from user_company where account_id=? and enable='1'))", userId);
    }

    /**
     * 获取公司/客户树结构
     *
     * @param userId 用户ID
     * @return 公司/客户树结构
     */
    public List<Record> getCompanyClientTree(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }

        // 获取公司列表
        List<Record> companies = Db.find("SELECT id, name, 'company' type FROM company WHERE id in (select company_id from user_company where account_id = ? and enable='1') ORDER BY name", userId);
        
        // 获取客户列表
        List<Record> clients = Db.find("SELECT c.id, c.name, cc.company_id FROM client c left join company_client cc on c.id=cc.client_id WHERE cc.company_id in (select company_id from user_company where account_id = ? and enable='1') ", userId);
        
        List<Record> result = new ArrayList<>();
        
        // 创建根节点
        Record root = new Record();
        root.set("id", "root");
        root.set("text", "所有公司/客户");
        root.set("icon", "fa fa-building");
        
        Map<String, Object> state = new HashMap<>();
        state.put("opened", true);
        root.set("state", state);
        
        Map<String, Object> data = new HashMap<>();
        data.put("type", "root");
        root.set("data", data);
        
        // 添加公司节点
        List<Record> children = new ArrayList<>();
        for (Record company : companies) {
            Record node = new Record();
            node.set("id", "company_" + company.getInt("id"));
            node.set("text", company.getStr("name"));
            node.set("icon", "fa fa-building-o");
            
            Map<String, Object> nodeData = new HashMap<>();
            nodeData.put("type", "company");
            node.set("data", nodeData);
            
            // 添加属于该公司的客户
            List<Record> companyChildren = new ArrayList<>();
            for (Record client : clients) {
                if (client.getInt("company_id") != null && 
                    client.getInt("company_id").equals(company.getInt("id"))) {
                    Record clientNode = new Record();
                    clientNode.set("id", "client_" + client.getInt("id"));
                    clientNode.set("text", client.getStr("name"));
                    clientNode.set("icon", "fa fa-user");
                    
                    Map<String, Object> clientData = new HashMap<>();
                    clientData.put("type", "client");
                    clientNode.set("data", clientData);
                    
                    companyChildren.add(clientNode);
                }
            }
            
            if (!companyChildren.isEmpty()) {
                node.set("children", companyChildren);
            }
            
            children.add(node);
        }
        
        // 添加未分配公司的客户
        for (Record client : clients) {
            if (client.getInt("company_id") == null) {
                Record node = new Record();
                node.set("id", "client_" + client.getInt("id"));
                node.set("text", client.getStr("name"));
                node.set("icon", "fa fa-user");
                
                Map<String, Object> nodeData = new HashMap<>();
                nodeData.put("type", "client");
                node.set("data", nodeData);
                
                children.add(node);
            }
        }
        
        root.set("children", children);
        result.add(root);
        
        return result;
    }
}