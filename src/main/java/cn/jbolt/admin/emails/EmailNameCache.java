package cn.jbolt.admin.emails;

import cn.jbolt.admin.client.ClientService;
import cn.jbolt.admin.emailaccount.EmailAccountService;
import cn.jbolt.common.model.Client;
import cn.jbolt.common.model.EmailAccount;
import cn.jbolt.core.cache.JBoltCache;
import com.google.common.collect.Maps;
import com.jfinal.aop.Aop;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static cn.jbolt.common.util.StringKit.noNull;

/**
 * 邮件地址显示名称缓存
 */
public class EmailNameCache extends JBoltCache {
    public static final EmailNameCache me = new EmailNameCache();
    private static final String TYPE_NAME = "emailnamecache";
    EmailAccountService emailAccountService = Aop.get(EmailAccountService.class);
    ClientService clientService = Aop.get(ClientService.class);

    private static final ConcurrentHashMap<String, String> nameCache = new ConcurrentHashMap<>();

    /**
     * 获取邮件地址的显示名称
     *
     * @param emailAddress 邮件地址
     * @return 显示名称
     */
    public String getDisplayName(String emailAddress) {
        if (StrKit.isBlank(emailAddress)) {
            return "";
        }

        String[] emailAddressSplit = emailAddress.split("[,;]");
        List<String> emailNameList = Lists.newArrayList();
        for (String emailAddressSplitOne : emailAddressSplit) {
            emailAddressSplitOne = emailAddressSplitOne.trim().toLowerCase();
            emailNameList.add(nameCache.getOrDefault(emailAddressSplitOne, emailAddressSplitOne));
        }
        return StringUtils.join(emailNameList, ",");
    }

    /**
     * 初始化或刷新缓存
     */
    public void refreshCache() {
        try {
            Map<String, String> emailNameMap = Maps.newHashMap();

            List<Record> emailAliasList = Db.find("select * from email_alias order by email, label");
            for (Record emailAlias : emailAliasList) {
                String email = emailAlias.getStr("email").trim().toLowerCase();
                emailNameMap.put(email, emailAlias.getStr("label"));
            }

            // 获取所有EmailAccount数据
            List<EmailAccount> emailAccounts = emailAccountService.findAll();
            for (EmailAccount account : emailAccounts) {
                if (account.getUsername() != null) {
                    String nickname = account.getNickname();
                    nickname = StringUtils.isEmpty(nickname) ? StringUtils.EMPTY : nickname;
                    emailNameMap.put(account.getUsername().toLowerCase(), nickname);
                }
            }

            // 获取所有Client数据
            List<Client> clients = clientService.getAllClientNotInAccount();
            for (Client client : clients) {
                if (client.getEmail() != null) {
                    String[] emails = client.getEmail().split("[,;]");
                    for (String email : emails) {
                        email = email.trim().toLowerCase();
                        if (!email.isEmpty()) {
                            String nickname = noNull(client.getNickname(), email.split("@")[0]);
                            String companyNickname = noNull(client.getStr("nick_name"));
                            emailNameMap.put(email, companyNickname + "•" + nickname);
                        }
                    }
                }
            }

            // 更新缓存
            nameCache.clear();
            nameCache.putAll(emailNameMap);

            LogKit.info("Email name cache refreshed successfully. Total entries: " + emailNameMap.size());
        } catch (Exception e) {
            LogKit.error("Failed to refresh email name cache", e);
        }
    }

    /**
     * 清除缓存
     */
    public void clearCache() {
        nameCache.clear();
    }

    /**
     * 更新缓存中的显示名称
     *
     * @param emailAddress 邮件地址
     * @param displayName  显示名称
     */
    public void updateDisplayName(String emailAddress, String displayName) {
        if (StrKit.notBlank(emailAddress) && StrKit.notBlank(displayName)) {
            nameCache.put(emailAddress, displayName);
        }
    }

    public void removeDisplayName(String emailAddress) {
        if (StrKit.notBlank(emailAddress)) {
            nameCache.remove(emailAddress);
        }
    }

    @Override
    public String getCacheTypeName() {
        return TYPE_NAME;
    }
}