package cn.jbolt.admin.llm;

import cn.jbolt.common.config.JBoltUploadFolder;
import cn.jbolt.common.model.LlmModel;
import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.core.controller.base.JBoltBaseController;
import cn.jbolt.core.permission.CheckPermission;
import cn.jbolt.core.permission.UnCheckIfSystemAdmin;
import cn.jbolt.llm.service.LlmService;
import com.jfinal.core.Path;
import com.jfinal.upload.UploadFile;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * LLM演示控制器
 * 用于演示如何使用LLM API，包括图片处理功能
 */
@CheckPermission("admin.llm")
@UnCheckIfSystemAdmin
@Path(value = "/admin/llm/demo", viewPath = "/_view/admin/llm/demo")
public class LlmDemoController extends JBoltBaseController {

    /**
     * 首页
     */
    public void index() {
        render("index.html");
    }

    /**
     * 使用文本调用LLM
     */
    public void callWithText() {
        Long providerId = getParaToLong("providerId");
        String model = getPara("model");
        String prompt = getPara("prompt");

        if (providerId == null || StringUtils.isEmpty(model)) {
            renderJsonFail("没有选择大模型");
            return;
        }

//        Record promptRecord = Db.findFirst("select * from ai_prompt where `key`='email_monument_translate'");
//        if (StringUtils.isEmpty(prompt)) {
//            String emailTranslatePrompt = Db.queryStr("select ifnull(user_content, ifnull(system_content, '')) from ai_prompt where `key`='email_monument_translate' limit 1");
//        }

        // 调用LLM服务
        String response = LlmService.me().callLlm(providerId, model, prompt);

        if (response != null) {
            // 添加日志
            System.out.println("LLM响应：" + response);
            renderJsonData(response);
        } else {
            renderJsonFail("调用LLM失败");
        }
    }

    /**
     * 上传图片并调用LLM
     */
    public void callWithImage() {
        // 上传图片
        String uploadPath = JBoltUploadFolder.todayFolder(JBoltUploadFolder.TEMP_FOLDER);
        UploadFile uploadFile = getFile("image", uploadPath);
        if (uploadFile == null) {
            renderJsonFail("请上传图片");
            return;
        }
        File file = uploadFile.getFile();
        if (!file.exists()) {
            renderJsonFail("图片上传失败");
            return;
        }
        Long providerId = getParaToLong("providerId");
        String model = getPara("model");
        String prompt = getPara("prompt");

        if (providerId == null || prompt == null || prompt.isEmpty()) {
            renderJsonFail("参数错误");
            return;
        }

        // 创建图片路径列表
        List<String> imagePaths = new ArrayList<>();
        imagePaths.add(file.getAbsolutePath());

        // 调用LLM服务
        String response = LlmService.me().callLlmWithImages(providerId, model, prompt, imagePaths);

        if (response != null) {
            // 添加日志
            System.out.println("LLM图片响应：" + response);
            renderJsonData(response);
        } else {
            renderJsonFail("调用LLM失败");
        }
    }

    /**
     * 使用Markdown格式的图片引用调用LLM
     */
    public void callWithMarkdown() {
        Long providerId = getParaToLong("providerId");
        String model = getPara("model");
        String markdown = getPara("markdown");

        if (providerId == null || markdown == null || markdown.isEmpty()) {
            renderJsonFail("参数错误");
            return;
        }

        // 调用LLM服务
        String response = LlmService.me().callLlmWithMarkdownImages(providerId, model, markdown);

        if (response != null) {
            // 添加日志
            System.out.println("LLM Markdown响应：" + response);
            renderJsonData(response);
        } else {
            renderJsonFail("调用LLM失败");
        }
    }

    /**
     * 获取LLM提供商列表
     */
    public void getProviders() {
        renderJsonData(new LlmProvider().dao().find("select * from llm_provider where status=1 order by priority"));
    }

    /**
     * 获取指定提供商的模型列表
     */
    public void getModels() {
        Long providerId = getParaToLong("providerId");
        if (providerId == null) {
            renderJsonFail("参数错误");
            return;
        }

        // 获取指定提供商的模型列表
        renderJsonData(new LlmModel().dao().find(
                "select * from llm_model where provider_id=? and status=1 order by model_identifier", providerId));
    }
}
