package cn.jbolt.admin.sso;

import com.jfinal.kit.HashKit;
import com.jfinal.kit.StrKit;

import java.net.URLEncoder;

/**
 * 外部系统集成工具类
 * 用于系统B生成跳转到StoneCRM的自动登录URL
 * 
 * <AUTHOR>
 * @date 2024
 */
public class ExternalSystemUtil {
    
    // 与StoneCRM系统保持一致的共享密钥
    private static final String SHARED_SECRET = "your-shared-secret-key-2024";
    
    private String stoneCrmBaseUrl;
    
    public ExternalSystemUtil(String stoneCrmBaseUrl) {
        this.stoneCrmBaseUrl = stoneCrmBaseUrl;
        if (!this.stoneCrmBaseUrl.endsWith("/")) {
            this.stoneCrmBaseUrl += "/";
        }
    }
    
    /**
     * 生成自动登录URL
     * 
     * @param username 用户名（必须在StoneCRM系统中存在）
     * @param targetPage 登录成功后跳转的目标页面（可选）
     * @return 自动登录URL
     */
    public String generateAutoLoginUrl(String username, String targetPage) {
        if (StrKit.isBlank(username)) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        
        try {
            long timestamp = System.currentTimeMillis() / 1000;
            String sign = generateSign(username, timestamp);
            
            StringBuilder url = new StringBuilder();
            url.append(stoneCrmBaseUrl);
            url.append("autoLogin");
            url.append("?username=").append(URLEncoder.encode(username, "UTF-8"));
            url.append("&timestamp=").append(timestamp);
            url.append("&sign=").append(sign);
            
            if (StrKit.notBlank(targetPage)) {
                url.append("&target=").append(URLEncoder.encode(targetPage, "UTF-8"));
            }
            
            return url.toString();
            
        } catch (Exception e) {
            throw new RuntimeException("生成自动登录URL失败", e);
        }
    }
    
    /**
     * 生成签名
     * 签名算法：MD5(username + timestamp + secret)
     * 
     * @param username 用户名
     * @param timestamp 时间戳
     * @return 签名
     */
    private String generateSign(String username, long timestamp) {
        String data = username + timestamp + SHARED_SECRET;
        return HashKit.md5(data);
    }
    
    /**
     * 验证签名（用于测试）
     * 
     * @param username 用户名
     * @param timestamp 时间戳
     * @param sign 签名
     * @return 验证结果
     */
    public boolean verifySign(String username, long timestamp, String sign) {
        String expectedSign = generateSign(username, timestamp);
        return expectedSign.equalsIgnoreCase(sign);
    }
    
    // Getter and Setter
    public String getStoneCrmBaseUrl() {
        return stoneCrmBaseUrl;
    }
    
    public void setStoneCrmBaseUrl(String stoneCrmBaseUrl) {
        this.stoneCrmBaseUrl = stoneCrmBaseUrl;
        if (!this.stoneCrmBaseUrl.endsWith("/")) {
            this.stoneCrmBaseUrl += "/";
        }
    }
} 