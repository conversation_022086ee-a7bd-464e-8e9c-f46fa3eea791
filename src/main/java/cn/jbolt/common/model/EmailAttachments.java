package cn.jbolt.common.model;

import cn.jbolt.common.model.base.BaseEmailAttachments;
import cn.jbolt.core.annotation.TableBind;
import cn.jbolt.core.base.JBoltIDGenMode;

/**
 * Generated by JBolt.
 */
@SuppressWarnings("serial")
@TableBind(dataSource = "main", table = "email_attachments", primaryKey = "id", idGenMode = JBoltIDGenMode.SNOWFLAKE)
public class EmailAttachments extends BaseEmailAttachments<EmailAttachments> {
    public static final int STATUS_DOWNLOADING = 1;
    public static final int STATUS_COMPLETED = 2;
    public static final int STATUS_FAILED = 3;
    public static final int STATUS_PENDING = 5;

    public void setFileIntSize(Integer size) {
        setFileSize(size == null ? 0L : size.longValue());
    }
}

