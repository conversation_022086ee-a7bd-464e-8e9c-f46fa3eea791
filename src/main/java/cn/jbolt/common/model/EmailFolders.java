package cn.jbolt.common.model;

import cn.jbolt.common.model.base.BaseEmailFolders;
import cn.jbolt.core.annotation.TableBind;
import cn.jbolt.core.base.JBoltIDGenMode;

/**
 * 邮箱文件夹管理表
 * Generated by <PERSON><PERSON><PERSON>.
 */
@SuppressWarnings("serial")
@TableBind(dataSource = "main" , table = "email_folders" , primaryKey = "id" , idGenMode = JBoltIDGenMode.SNOWFLAKE)
public class EmailFolders extends BaseEmailFolders<EmailFolders> {
    public static final String FOLDER_INBOX = "inbox";
    public static final Integer PRIORITY_HIGHEST = 999;
    public static final Integer PRIORITY_NORMAL = 1;
    public static final Integer PRIORITY_LOW = 0;
    public Boolean isIgnored(){
        // todo: add a colum ignored
        return false;
    }
}

