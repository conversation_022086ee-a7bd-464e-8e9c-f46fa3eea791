package cn.jbolt.common.model;

import cn.jbolt.common.model.base.BaseEmailsEmail;
import cn.jbolt.core.annotation.TableBind;
import cn.jbolt.core.base.JBoltIDGenMode;

/**
 * 信息邮箱
 * Generated by JBolt.
 */
@SuppressWarnings("serial")
@TableBind(dataSource = "main", table = "emails_email", primaryKey = "id", idGenMode = JBoltIDGenMode.AUTO)
public class EmailsEmail extends BaseEmailsEmail<EmailsEmail> {
}

