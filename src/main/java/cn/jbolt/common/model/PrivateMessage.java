package cn.jbolt.common.model;

import cn.jbolt.common.model.base.BasePrivateMessage;
import cn.jbolt.core.annotation.TableBind;
import cn.jbolt.core.base.JBoltIDGenMode;

/**
 * 内部私信
 * Generated by JBolt.
 */
@SuppressWarnings("serial")
@TableBind(dataSource = "main", table = "jb_private_message", primaryKey = "id", idGenMode = JBoltIDGenMode.SNOWFLAKE)
public class PrivateMessage extends BasePrivateMessage<PrivateMessage> {

}
