package cn.jbolt.common.model.base;
import cn.jbolt.core.model.base.JBoltBaseModel;
import cn.jbolt.core.gen.JBoltField;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * 邮箱签名
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseEmailSignature<M extends BaseEmailSignature<M>> extends JBoltBaseModel<M>{
    public static final String DATASOURCE_CONFIG_NAME = "main";
    /**ID*/
    public static final String ID = "id";
    /**名称*/
    public static final String NAME = "name";
    /**内容*/
    public static final String CONTENT = "content";
    /**顺序*/
    public static final String SORT_RANK = "sort_rank";
    /**备注信息*/
    public static final String REMARK = "remark";
    /**启用/禁用*/
    public static final String ENABLE = "enable";
    /**创建时间*/
    public static final String CREATE_TIME = "create_time";
    /**创建人*/
    public static final String CREATE_USER_ID = "create_user_id";
    /**更新时间*/
    public static final String UPDATE_TIME = "update_time";
    /**更新人id*/
    public static final String UPDATE_USER_ID = "update_user_id";
    /**是否全局可用*/
    public static final String IS_GLOBAL = "is_global";
	/**
	 * ID
	 */
	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	/**
	 * ID
	 */
	@JBoltField(name="id" ,columnName="id",type="Integer", remark="ID", required=true, maxLength=10, fixed=0, order=1)
	public java.lang.Integer getId() {
		return getInt("id");
	}

	/**
	 * 名称
	 */
	public M setName(java.lang.String name) {
		set("name", name);
		return (M)this;
	}
	
	/**
	 * 名称
	 */
	@JBoltField(name="name" ,columnName="name",type="String", remark="名称", required=false, maxLength=255, fixed=0, order=2)
	public java.lang.String getName() {
		return getStr("name");
	}

	/**
	 * 内容
	 */
	public M setContent(java.lang.String content) {
		set("content", content);
		return (M)this;
	}
	
	/**
	 * 内容
	 */
	@JBoltField(name="content" ,columnName="content",type="String", remark="内容", required=false, maxLength=2147483647, fixed=0, order=3)
	public java.lang.String getContent() {
		return getStr("content");
	}

	/**
	 * 顺序
	 */
	public M setSortRank(java.lang.Integer sortRank) {
		set("sort_rank", sortRank);
		return (M)this;
	}
	
	/**
	 * 顺序
	 */
	@JBoltField(name="sortRank" ,columnName="sort_rank",type="Integer", remark="顺序", required=false, maxLength=10, fixed=0, order=4)
	public java.lang.Integer getSortRank() {
		return getInt("sort_rank");
	}

	/**
	 * 备注信息
	 */
	public M setRemark(java.lang.String remark) {
		set("remark", remark);
		return (M)this;
	}
	
	/**
	 * 备注信息
	 */
	@JBoltField(name="remark" ,columnName="remark",type="String", remark="备注信息", required=false, maxLength=255, fixed=0, order=5)
	public java.lang.String getRemark() {
		return getStr("remark");
	}

	/**
	 * 启用/禁用
	 */
	public M setEnable(java.lang.Boolean enable) {
		set("enable", enable);
		return (M)this;
	}
	
	/**
	 * 启用/禁用
	 */
	@JBoltField(name="enable" ,columnName="enable",type="Boolean", remark="启用/禁用", required=false, maxLength=1, fixed=0, order=6)
	public java.lang.Boolean getEnable() {
		return getBoolean("enable");
	}

	/**
	 * 创建时间
	 */
	public M setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
		return (M)this;
	}
	
	/**
	 * 创建时间
	 */
	@JBoltField(name="createTime" ,columnName="create_time",type="Date", remark="创建时间", required=false, maxLength=19, fixed=0, order=7)
	public java.util.Date getCreateTime() {
		return getDate("create_time");
	}

	/**
	 * 创建人
	 */
	public M setCreateUserId(java.lang.Long createUserId) {
		set("create_user_id", createUserId);
		return (M)this;
	}
	
	/**
	 * 创建人
	 */
	@JBoltField(name="createUserId" ,columnName="create_user_id",type="Long", remark="创建人", required=false, maxLength=19, fixed=0, order=8)
	@JSONField(serializeUsing= ToStringSerializer.class)
	public java.lang.Long getCreateUserId() {
		return getLong("create_user_id");
	}

	/**
	 * 更新时间
	 */
	public M setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
		return (M)this;
	}
	
	/**
	 * 更新时间
	 */
	@JBoltField(name="updateTime" ,columnName="update_time",type="Date", remark="更新时间", required=false, maxLength=19, fixed=0, order=9)
	public java.util.Date getUpdateTime() {
		return getDate("update_time");
	}

	/**
	 * 更新人id
	 */
	public M setUpdateUserId(java.lang.Long updateUserId) {
		set("update_user_id", updateUserId);
		return (M)this;
	}
	
	/**
	 * 更新人id
	 */
	@JBoltField(name="updateUserId" ,columnName="update_user_id",type="Long", remark="更新人id", required=false, maxLength=19, fixed=0, order=10)
	@JSONField(serializeUsing= ToStringSerializer.class)
	public java.lang.Long getUpdateUserId() {
		return getLong("update_user_id");
	}

	/**
	 * 是否全局可用
	 */
	public M setIsGlobal(java.lang.Boolean isGlobal) {
		set("is_global", isGlobal);
		return (M)this;
	}
	
	/**
	 * 是否全局可用
	 */
	@JBoltField(name="isGlobal" ,columnName="is_global",type="Boolean", remark="是否全局可用", required=false, maxLength=1, fixed=0, order=11)
	public java.lang.Boolean getIsGlobal() {
		return getBoolean("is_global");
	}

}

