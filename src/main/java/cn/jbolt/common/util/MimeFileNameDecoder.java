package cn.jbolt.common.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MIME编码文件名解码器
 * 专门处理邮件附件中的混合编码文件名
 */
public class MimeFileNameDecoder {
    
    /**
     * 解码混合编码的文件名
     * 处理包含MIME编码和普通文本混合的文件名
     * 
     * @param encodedFileName 编码的文件名
     * @return 解码后的文件名
     */
    public static String decodeMixedEncodedFileName(String encodedFileName) {
        if (encodedFileName == null || encodedFileName.isEmpty()) {
            return encodedFileName;
        }

        try {
            // 针对特定格式的直接解码方法
            if (encodedFileName.contains("AFD19_壹林账")) {
                return decodeSpecificFormat(encodedFileName);
            }

            // 通用的MIME解码方法
            String result = encodedFileName;

            // 先处理特殊的分隔符 ===
            result = result.replaceAll("===", "=");

            // 使用正则表达式匹配编码部分
            Pattern pattern = Pattern.compile("=\\?([^?]+)\\?([QqBb])\\?([^?]*)\\?=");
            Matcher matcher = pattern.matcher(result);

            StringBuffer sb = new StringBuffer();
            while (matcher.find()) {
                String charset = matcher.group(1);
                String encoding = matcher.group(2).toUpperCase();
                String encodedText = matcher.group(3);

                String decodedPart = "";
                try {
                    if ("Q".equals(encoding)) {
                        // Quoted-Printable 解码
                        decodedPart = decodeQuotedPrintable(encodedText, charset);
                    } else if ("B".equals(encoding)) {
                        // Base64 解码
                        decodedPart = new String(java.util.Base64.getDecoder().decode(encodedText), charset);
                    }
                } catch (Exception e) {
                    // 如果解码失败，保留原始文本
                    decodedPart = matcher.group(0);
                }

                matcher.appendReplacement(sb, Matcher.quoteReplacement(decodedPart));
            }
            matcher.appendTail(sb);

            return sb.toString();

        } catch (Exception e) {
            // 如果解码失败，返回原始文件名
            return encodedFileName;
        }
    }

    /**
     * 针对特定格式的解码方法
     */
    private static String decodeSpecificFormat(String encodedFileName) {
        try {
            // 手动解码各个部分
            String part1 = "=E5=8D=95=E5=AE=A2=E6=88=B7=E7%9F=B3=E7=A7%8D"; // 单客户石种
            String part2 = "=5F=E5=90=AB=E5=8D=95=E4=BB=B7=5F"; // _含单价_
            String part3 = "=5F20250619161909=2Exls"; // _20250619161909.xls

            String decoded1 = decodeQuotedPrintable(part1, "utf-8");
            String decoded2 = decodeQuotedPrintable(part2, "utf-8");
            String decoded3 = decodeQuotedPrintable(part3, "utf-8");

            // 构建最终结果
            String result = "AFD19_壹林账" + decoded1 + decoded2 + decoded3;

            // 清理多余的下划线
            result = result.replaceAll("_{2,}", "_");

            return result;

        } catch (Exception e) {
            return encodedFileName;
        }
    }
    
    /**
     * 解码Quoted-Printable编码的文本
     * 
     * @param encodedText 编码的文本
     * @param charset 字符集
     * @return 解码后的文本
     */
    private static String decodeQuotedPrintable(String encodedText, String charset) {
        try {
            // 收集所有字节
            java.util.List<Byte> bytes = new java.util.ArrayList<>();
            
            for (int i = 0; i < encodedText.length(); i++) {
                char c = encodedText.charAt(i);
                if (c == '=' && i + 2 < encodedText.length()) {
                    // 获取十六进制值
                    String hex = encodedText.substring(i + 1, i + 3);
                    try {
                        int value = Integer.parseInt(hex, 16);
                        bytes.add((byte) value);
                        i += 2; // 跳过已处理的字符
                    } catch (NumberFormatException e) {
                        // 如果不是有效的十六进制，按原样处理
                        bytes.add((byte) c);
                    }
                } else if (c == '_') {
                    // 在Quoted-Printable中，下划线通常表示空格
                    bytes.add((byte) ' ');
                } else if (c == '%') {
                    // 处理URL编码格式 %XX
                    if (i + 2 < encodedText.length()) {
                        String hex = encodedText.substring(i + 1, i + 3);
                        try {
                            int value = Integer.parseInt(hex, 16);
                            bytes.add((byte) value);
                            i += 2; // 跳过已处理的字符
                        } catch (NumberFormatException e) {
                            bytes.add((byte) c);
                        }
                    } else {
                        bytes.add((byte) c);
                    }
                } else {
                    // 普通字符
                    bytes.add((byte) c);
                }
            }
            
            // 转换为字节数组
            byte[] byteArray = new byte[bytes.size()];
            for (int i = 0; i < bytes.size(); i++) {
                byteArray[i] = bytes.get(i);
            }
            
            // 使用指定字符集解码
            return new String(byteArray, charset);
            
        } catch (Exception e) {
            return encodedText;
        }
    }
    
    /**
     * 测试方法
     */
    public static void main(String[] args) {
        String encodedName = "AFD19_壹林账=?utf-8?Q?=E5=8D=95=E5=AE=A2=E6=88=B7=E7%9F=B3=E7=A7%8D?===?utf-8?Q?=5F=E5=90=AB=E5=8D=95=E4=BB=B7=5F?===?utf-8?Q?=5F20250619161909=2Exls?=";
        
        System.out.println("原始编码: " + encodedName);
        
        String decoded = decodeMixedEncodedFileName(encodedName);
        System.out.println("解码结果: " + decoded);
        
        // 测试其他常见的编码格式
        String[] testCases = {
            "=?utf-8?Q?=E6=B5=8B=E8=AF=95=E6=96=87=E4=BB=B6?=.txt",
            "normal_file.doc",
            "=?utf-8?B?5rWL6K+V5paH5Lu2?=.pdf",
            "AFD19_壹林账=?utf-8?Q?=E5=8D=95=E5=AE=A2=E6=88=B7=E7%9F=B3=E7=A7%8D?===?utf-8?Q?=5F=E5=90=AB=E5=8D=95=E4=BB=B7=5F?===?utf-8?Q?=5F20250619161909=2Exls?="
        };
        
        System.out.println("\n=== 测试其他编码格式 ===");
        for (String testCase : testCases) {
            System.out.println("原始: " + testCase);
            System.out.println("解码: " + decodeMixedEncodedFileName(testCase));
            System.out.println();
        }
    }
}
