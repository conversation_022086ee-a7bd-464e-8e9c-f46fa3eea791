package cn.jbolt.core.Interceptor;

import cn.jbolt.core.cache.JBoltGlobalConfigCache;
import cn.jbolt.core.consts.JBoltConst;
import cn.jbolt.core.kit.JBoltSnowflakeKit;
import cn.jbolt.core.kit.JBoltUserKit;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.core.JFinal;
import com.jfinal.kit.StrKit;

/**
 * JBolt内置websocket guest 拦截器
 * 主要实现cookie上存guest user id 检测这个id
 */
public class JBoltWebsocketGuestInterceptor implements Interceptor {
    @Override
    public void intercept(Invocation invocation) {
        Controller controller = invocation.getController();
        String userId = controller.getCookie(JBoltConst.JBOLT_WS_GUEST_USER_ID_KEY);
        if (StrKit.isBlank(userId)) {
            //说明是首次访问 需要创建cookie
            userId = JBoltSnowflakeKit.me.nextIdStr();
            int keepLoginSeconds = JBoltGlobalConfigCache.me.getKeepLoginSeconds();
            controller.setCookie(JBoltConst.JBOLT_WS_GUEST_USER_ID_KEY, userId, keepLoginSeconds, JFinal.me().getContextPath(), true);
        }
        JBoltUserKit.setUserId(Long.parseLong(userId));
        try {
            invocation.invoke();
        } finally {
            JBoltUserKit.removeUserId();
        }
    }
}
