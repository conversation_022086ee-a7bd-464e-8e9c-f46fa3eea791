package cn.jbolt.core.cache;

import cn.jbolt.core.base.config.JBoltConfig;
import cn.jbolt.core.consts.JBoltConst;
import cn.jbolt.core.kit.JBoltSaasTenantKit;
import cn.jbolt.core.model.base.JBoltModelConfig;
import cn.jbolt.core.util.JBoltArrayUtil;
import com.jfinal.plugin.activerecord.Model;

public abstract class JBoltCache extends JBoltCacheParaValidator {
    public abstract String getCacheTypeName();

    /**
     * 关联Model对应的key 构建
     *
     * @param modelClass
     * @param pre
     * @param value
     * @return
     */
    protected String buildCacheKey(Class<? extends Model<?>> modelClass, String pre, Object... values) {
        String type = getCacheTypeName();
        if (notOk(type)) {
            type = getClass().getSimpleName();
        }
        String key = JBoltConst.JBOLT_CACHE_DEFAULT_PREFIX + type + "_" + pre;
        if (values != null && values.length > 0) {
            if (values.length == 1) {
                key = key + values[0].toString();
            } else {
                key = key + JBoltArrayUtil.join(values, JBoltArrayUtil.COMMA);
            }

        }

        if (!JBoltConfig.SAAS_ENABLE || !JBoltSaasTenantKit.me.isOk() || !JBoltModelConfig.me.isSeparate(modelClass)) {
            return key;
        }
        return JBoltSaasTenantKit.me.getSn() + "_" + key;
    }
}
