package cn.jbolt.core.cache;

import cn.jbolt.core.para.IJBoltParaValidator;
import cn.jbolt.core.para.JBoltParaValidator;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;

/**
 * cache层使用的参数校验器
 *
 * @ClassName: CacheParaValidator
 * @author: JFinal学院-小木 QQ：909854136
 * @date: 2019年12月5日
 */
public class JBoltCacheParaValidator implements IJBoltParaValidator {
    /**
     * 判断Object参数有效性
     *
     * @param param
     */
    @Override
    public boolean isOk(Object param) {
        return JBoltParaValidator.isOk(param);
    }

    /**
     * 判断Object参数为无效参数
     *
     * @param param
     */
    @Override
    public boolean notOk(Object param) {
        return JBoltParaValidator.notOk(param);
    }

    /**
     * 判断record参数有效性
     *
     * @param record
     */
    @Override
    public boolean isOk(Record record) {
        return JBoltParaValidator.isOk(record);
    }

    /**
     * 判断record参数为无效参数
     *
     * @param record
     */
    @Override
    public boolean notOk(Record record) {
        return JBoltParaValidator.notOk(record);
    }

    /**
     * 判断model参数有效性
     *
     * @param model
     */
    @Override
    public boolean isOk(Model<? extends Model<?>> model) {
        return JBoltParaValidator.isOk(model);
    }

    /**
     * 判断model参数为无效参数
     *
     * @param model
     */
    @Override
    public boolean notOk(Model<? extends Model<?>> model) {
        return JBoltParaValidator.notOk(model);
    }

    /**
     * 判断Object参数为Null
     *
     * @param param
     */
    public boolean isNull(Object param) {
        return JBoltParaValidator.isNull(param);
    }

    /**
     * 判断Object参数不为Null
     */
    public boolean notNull(Object param) {
        return JBoltParaValidator.notNull(param);
    }

    @Override
    public boolean isOk(Object[] param) {
        return JBoltParaValidator.isOk(param);
    }

    @Override
    public boolean notOk(Object[] param) {
        return JBoltParaValidator.notOk(param);
    }

    @Override
    public boolean notImage(UploadFile file) {
        return false;
    }

    @Override
    public boolean isImage(UploadFile file) {
        return false;
    }

    @Override
    public boolean isImage(String contentType) {
        return false;
    }

    @Override
    public boolean notImage(String contentType) {
        return false;
    }

    @Override
    public boolean notExcel(UploadFile file) {
        return false;
    }

    @Override
    public boolean isExcel(UploadFile file) {
        return false;
    }

    @Override
    public boolean hasOk(Object... params) {
        return JBoltParaValidator.hasOk(params);
    }

    @Override
    public boolean hasNotOk(Object... params) {
        return JBoltParaValidator.hasNotOk(params);
    }

}
