package cn.jbolt.core.cache;

import cn.jbolt.core.base.config.JBoltConfig;
import cn.jbolt.core.model.DictionaryType;
import cn.jbolt.core.service.JBoltDictionaryTypeService;
import com.jfinal.aop.Aop;

import java.util.List;

public class JBoltDictionaryTypeCache extends JB<PERSON><PERSON>ache {
    public static final JBoltDictionaryTypeCache me = new JBoltDictionaryTypeCache();
    private static final String TYPE_NAME = "dic_type";
    JBoltDictionaryTypeService service = Aop.get(JBoltDictionaryTypeService.class);

    /**
     * 通过ID获得字典数据类型
     *
     * @return
     */
    public DictionaryType get(Long id) {
        return service.findById(id);
    }

    /**
     * 通过typeKey获得字典数据类型
     *
     * @return
     */
    public DictionaryType getByKey(String typeKey) {
        return service.getCacheByKey(typeKey);
    }

    /**
     * 获得字典数据类型的标识name
     *
     * @param id
     * @return
     */
    public String getName(Long id) {
        DictionaryType dictionaryType = get(id);
        return dictionaryType == null ? "" : dictionaryType.getName();
    }

    /**
     * 获得字典数据类型是否是内置
     *
     * @param id
     * @return
     */
    public boolean isBuildIn(Long id) {
        DictionaryType dictionaryType = get(id);
        return dictionaryType != null && (dictionaryType.getIsBuildIn() != null && dictionaryType.getIsBuildIn());
    }

    /**
     * 根据字典类型key 获得字典数据类型的name
     *
     * @param typeKey
     * @return
     */
    public String getNameByKey(String typeKey) {
        DictionaryType dictionaryType = getByKey(typeKey);
        return dictionaryType == null ? "" : dictionaryType.getName();
    }

    /**
     * 获得字典数据类型的标识Key
     *
     * @param id
     * @return
     */
    public String getKey(Long id) {
        DictionaryType dictionaryType = get(id);
        return dictionaryType == null ? "" : dictionaryType.getTypeKey();
    }

    /**
     * 获得字典数据类型的ModelLevel 层级模式
     *
     * @param id
     * @return
     */
    public int getModeLevel(Long id) {
        DictionaryType dictionaryType = get(id);
        return dictionaryType == null ? 0 : dictionaryType.getModeLevel();
    }

    /**
     * 获取所有字典类型
     *
     * @return
     */
    public List<DictionaryType> getTypes() {
        return JBoltCacheKit.get(JBoltConfig.JBOLT_CACHE_NAME, "jbolt_dic_types", () -> service.findAll());
    }

    /**
     * 删除所有types的cache
     */
    public void removeTypes() {
        JBoltCacheKit.remove(JBoltConfig.JBOLT_CACHE_NAME, "jbolt_dic_types");
    }

    @Override
    public String getCacheTypeName() {
        return TYPE_NAME;
    }


}
