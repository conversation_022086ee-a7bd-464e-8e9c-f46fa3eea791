package cn.jbolt.core.converter.htmltojson;

import org.jsoup.nodes.Element;

import java.util.function.Predicate;

public class JBoltHtmlToJsonParams {
    public static final String TYPE_HTML = "html";
    public static final String TYPE_MD = "md";
    public static final String TYPE_MARKDOWN = "markdown";
    private String type;//类型 html markdown md
    private Boolean highlight;//是否开启pre代码高亮
    private Boolean linenums;//是否开启显示pre代码行号
    private String baseUri;//超链接或者图片的根URL
    private boolean keepId;//所有标签有id的就保留 默认false
    private boolean keepClass;//所有标签有class的就保留 默认false
    private Predicate<Element> nodeFilter;//节点筛选
    private JBoltHtmlToJsonNodeProcessor nodeProcessor;//节点处理

    private boolean skipEmptyTextNode;//是否跳过空字符节点

    public boolean isSkipEmptyTextNode() {
        return skipEmptyTextNode;
    }

    public void setSkipEmptyTextNode(boolean skipEmptyTextNode) {
        this.skipEmptyTextNode = skipEmptyTextNode;
    }

    public boolean isKeepId() {
        return keepId;
    }

    public void setKeepId(boolean keepId) {
        this.keepId = keepId;
    }

    public boolean isKeepClass() {
        return keepClass;
    }

    public void setKeepClass(boolean keepClass) {
        this.keepClass = keepClass;
    }

    public Predicate<Element> getNodeFilter() {
        return nodeFilter;
    }

    public void setNodeFilter(Predicate<Element> nodeFilter) {
        this.nodeFilter = nodeFilter;
    }

    public JBoltHtmlToJsonNodeProcessor getNodeProcessor() {
        return nodeProcessor;
    }

    public void setNodeProcessor(JBoltHtmlToJsonNodeProcessor nodeProcessor) {
        this.nodeProcessor = nodeProcessor;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getHighlight() {
        return highlight;
    }

    public void setHighlight(Boolean highlight) {
        this.highlight = highlight;
    }

    public Boolean getLinenums() {
        return linenums;
    }

    public void setLinenums(Boolean linenums) {
        this.linenums = linenums;
    }

    public String getBaseUri() {
        return baseUri;
    }

    public void setBaseUri(String baseUri) {
        this.baseUri = baseUri;
    }
}
