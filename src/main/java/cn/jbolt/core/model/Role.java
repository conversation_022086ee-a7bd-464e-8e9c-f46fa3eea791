package cn.jbolt.core.model;

import cn.jbolt.core.annotation.JBoltAutoCache;
import cn.jbolt.core.annotation.TableBind;
import cn.jbolt.core.base.JBoltIDGenMode;
import cn.jbolt.core.model.base.BaseRole;

/**
 * Generated by JFinal.
 */
@SuppressWarnings("serial")
@JBoltAutoCache(keyCache = true, column = "sn")
@TableBind(dataSource = "main", table = "jb_role", primaryKey = "id", idGenMode = JBoltIDGenMode.SNOWFLAKE)
public class Role extends BaseRole<Role> {
}
