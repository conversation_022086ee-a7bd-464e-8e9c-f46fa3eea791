package cn.jbolt.core.model;

import cn.jbolt.core.annotation.TableBind;
import cn.jbolt.core.base.JBoltIDGenMode;
import cn.jbolt.core.model.base.BaseWechatConfig;

/**
 * Generated by JFinal.
 */
@SuppressWarnings("serial")
@TableBind(dataSource = "main", table = "jb_wechat_config", primaryKey = "id", idGenMode = JBoltIDGenMode.SNOWFLAKE)
public class WechatConfig extends BaseWechatConfig<WechatConfig> {
    /**
     * 配置类型-基础配置
     */
    public final static int TYPE_BASE = 1;
    /**
     * 配置类型-支付配置
     */
    public final static int TYPE_PAY = 2;
    /**
     * 配置类型-额外配置
     */
    public final static int TYPE_EXTRA = 3;
}
