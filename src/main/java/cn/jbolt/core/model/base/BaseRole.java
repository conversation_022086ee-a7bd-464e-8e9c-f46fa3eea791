package cn.jbolt.core.model.base;

import cn.jbolt.core.gen.J<PERSON>oltField;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * 角色表 Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseRole<M extends BaseRole<M>> extends JBoltBaseModel<M> {

    /**
     * 主键ID
     */
    @JBoltField(name = "id", columnName = "id", type = "Long", remark = "主键ID", required = true, maxLength = 19, fixed = 0, order = 1)
    @JSONField(serializeUsing = ToStringSerializer.class)
    public Long getId() {
        return getLong("id");
    }

    /**
     * 主键ID
     */
    public M setId(Long id) {
        set("id", id);
        return (M) this;
    }

    /**
     * 名称
     */
    @JBoltField(name = "name", columnName = "name", type = "String", remark = "名称", required = true, maxLength = 20, fixed = 0, order = 2)
    public String getName() {
        return getStr("name");
    }

    /**
     * 名称
     */
    public M setName(String name) {
        set("name", name);
        return (M) this;
    }

    /**
     * 编码
     */
    @JBoltField(name = "sn", columnName = "sn", type = "String", remark = "编码", required = false, maxLength = 40, fixed = 0, order = 3)
    public String getSn() {
        return getStr("sn");
    }

    /**
     * 编码
     */
    public M setSn(String sn) {
        set("sn", sn);
        return (M) this;
    }

    /**
     * 父级角色ID
     */
    @JBoltField(name = "pid", columnName = "pid", type = "Long", remark = "父级角色ID", required = false, maxLength = 19, fixed = 0, order = 4)
    @JSONField(serializeUsing = ToStringSerializer.class)
    public Long getPid() {
        return getLong("pid");
    }

    /**
     * 父级角色ID
     */
    public M setPid(Long pid) {
        set("pid", pid);
        return (M) this;
    }

}
