package cn.jbolt.core.model.base;

import cn.jbolt.core.gen.JBoltField;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * 顶部导航 Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseTopnav<M extends BaseTopnav<M>> extends JBoltBaseModel<M> {

    /**
     * 主键ID
     */
    @JBoltField(name = "id", columnName = "id", type = "Long", remark = "主键ID", required = true, maxLength = 19, fixed = 0, order = 1)
    @JSONField(serializeUsing = ToStringSerializer.class)
    public Long getId() {
        return getLong("id");
    }

    /**
     * 主键ID
     */
    public M setId(Long id) {
        set("id", id);
        return (M) this;
    }

    /**
     * 名称
     */
    @JBoltField(name = "name", columnName = "name", type = "String", remark = "名称", required = false, maxLength = 40, fixed = 0, order = 2)
    public String getName() {
        return getStr("name");
    }

    /**
     * 名称
     */
    public M setName(String name) {
        set("name", name);
        return (M) this;
    }

    /**
     * 图标
     */
    @JBoltField(name = "icon", columnName = "icon", type = "String", remark = "图标", required = false, maxLength = 40, fixed = 0, order = 3)
    public String getIcon() {
        return getStr("icon");
    }

    /**
     * 图标
     */
    public M setIcon(String icon) {
        set("icon", icon);
        return (M) this;
    }

    /**
     * 是否启用
     */
    @JBoltField(name = "enable", columnName = "enable", type = "Boolean", remark = "是否启用", required = false, maxLength = 1, fixed = 0, order = 4)
    public Boolean getEnable() {
        return getBoolean("enable");
    }

    /**
     * 是否启用
     */
    public M setEnable(Boolean enable) {
        set("enable", enable);
        return (M) this;
    }

    /**
     * 排序
     */
    @JBoltField(name = "sortRank", columnName = "sort_rank", type = "Integer", remark = "排序", required = false, maxLength = 10, fixed = 0, order = 5)
    public Integer getSortRank() {
        return getInt("sort_rank");
    }

    /**
     * 排序
     */
    public M setSortRank(Integer sortRank) {
        set("sort_rank", sortRank);
        return (M) this;
    }

}
