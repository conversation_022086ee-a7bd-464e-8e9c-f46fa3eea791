package cn.jbolt.core.model.base;

import cn.jbolt.core.gen.JBoltField;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * 用户系统样式自定义设置表
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseUserConfig<M extends BaseUserConfig<M>> extends JBoltBaseModel<M> {

    /**
     * 主键ID
     */
    @JBoltField(name = "id", columnName = "id", type = "Long", remark = "主键ID", required = true, maxLength = 19, fixed = 0, order = 1)
    @JSONField(serializeUsing = ToStringSerializer.class)
    public Long getId() {
        return getLong("id");
    }

    /**
     * 主键ID
     */
    public M setId(Long id) {
        set("id", id);
        return (M) this;
    }

    /**
     * 配置名
     */
    @JBoltField(name = "name", columnName = "name", type = "String", remark = "配置名", required = true, maxLength = 255, fixed = 0, order = 2)
    public String getName() {
        return getStr("name");
    }

    /**
     * 配置名
     */
    public M setName(String name) {
        set("name", name);
        return (M) this;
    }

    /**
     * 配置KEY
     */
    @JBoltField(name = "configKey", columnName = "config_key", type = "String", remark = "配置KEY", required = true, maxLength = 255, fixed = 0, order = 3)
    public String getConfigKey() {
        return getStr("config_key");
    }

    /**
     * 配置KEY
     */
    public M setConfigKey(String configKey) {
        set("config_key", configKey);
        return (M) this;
    }

    /**
     * 配置值
     */
    @JBoltField(name = "configValue", columnName = "config_value", type = "String", remark = "配置值", required = true, maxLength = 255, fixed = 0, order = 4)
    public String getConfigValue() {
        return getStr("config_value");
    }

    /**
     * 配置值
     */
    public M setConfigValue(String configValue) {
        set("config_value", configValue);
        return (M) this;
    }

    /**
     * 用户ID
     */
    @JBoltField(name = "userId", columnName = "user_id", type = "Long", remark = "用户ID", required = true, maxLength = 19, fixed = 0, order = 5)
    @JSONField(serializeUsing = ToStringSerializer.class)
    public Long getUserId() {
        return getLong("user_id");
    }

    /**
     * 用户ID
     */
    public M setUserId(Long userId) {
        set("user_id", userId);
        return (M) this;
    }

    /**
     * 创建时间
     */
    @JBoltField(name = "createTime", columnName = "create_time", type = "Date", remark = "创建时间", required = false, maxLength = 19, fixed = 0, order = 6)
    public java.util.Date getCreateTime() {
        return getDate("create_time");
    }

    /**
     * 创建时间
     */
    public M setCreateTime(java.util.Date createTime) {
        set("create_time", createTime);
        return (M) this;
    }

    /**
     * 更新时间
     */
    @JBoltField(name = "updateTime", columnName = "update_time", type = "Date", remark = "更新时间", required = false, maxLength = 19, fixed = 0, order = 7)
    public java.util.Date getUpdateTime() {
        return getDate("update_time");
    }

    /**
     * 更新时间
     */
    public M setUpdateTime(java.util.Date updateTime) {
        set("update_time", updateTime);
        return (M) this;
    }

    /**
     * 取值类型
     */
    @JBoltField(name = "valueType", columnName = "value_type", type = "String", remark = "取值类型", required = false, maxLength = 40, fixed = 0, order = 8)
    public String getValueType() {
        return getStr("value_type");
    }

    /**
     * 取值类型
     */
    public M setValueType(String valueType) {
        set("value_type", valueType);
        return (M) this;
    }

}

