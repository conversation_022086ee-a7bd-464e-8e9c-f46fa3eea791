package cn.jbolt.index;

import cn.jbolt.admin.emailtracking.EmailTrackingController;
import com.jfinal.config.Routes;

/**
 * 邮件跟踪路由配置
 * 
 * <AUTHOR>
 */
public class EmailTrackingRoutes extends Routes {

    @Override
    public void config() {
        // 跟踪像素请求不需要认证，因为是从邮件客户端发起的
        this.add("/track", EmailTrackingController.class);
        
        // 管理员接口需要认证，但这里先不加拦截器，在具体的controller中处理
        // 如果需要统一认证，可以添加：
        // this.addInterceptor(new JBoltAdminAuthInterceptor());
    }
}
