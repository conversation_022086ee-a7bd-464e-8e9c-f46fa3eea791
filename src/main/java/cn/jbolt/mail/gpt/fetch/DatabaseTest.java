package cn.jbolt.mail.gpt.fetch;

import cn.jbolt.mail.gpt.InitEnv;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.Date;
import java.util.List;

/**
 * 数据库测试类
 * 用于测试数据库连接和操作
 */
public class DatabaseTest {
    public static void main(String[] args) {
        try {
            InitEnv.initEnvironment();
            System.out.println("=== 数据库连接测试 ===");

            // 测试1：创建测试表
            System.out.println("测试1：创建测试表");
            try {
                Db.update("CREATE TABLE IF NOT EXISTS email_test_log (" +
                        "id BIGINT NOT NULL AUTO_INCREMENT, " +
                        "account_id INT NOT NULL COMMENT '邮箱账号ID', " +
                        "message VARCHAR(255) NOT NULL COMMENT '测试消息', " +
                        "created_at DATETIME NOT NULL COMMENT '创建时间', " +
                        "PRIMARY KEY (id)" +
                        ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
                System.out.println("创建测试表成功！");
            } catch (Exception e) {
                System.out.println("创建测试表失败: " + e.getMessage());
                e.printStackTrace();
            }

            // 测试2：插入数据
            System.out.println("\n测试2：插入数据");
            try {
                Record testRecord = new Record();
                testRecord.set("account_id", 1);
                testRecord.set("message", "测试消息 " + new Date());
                testRecord.set("created_at", new Date());

                boolean result = Db.save("email_test_log", testRecord);
                if (result) {
                    System.out.println("插入数据成功！");
                } else {
                    System.out.println("插入数据失败！");
                }
            } catch (Exception e) {
                System.out.println("插入数据失败: " + e.getMessage());
                e.printStackTrace();
            }

            // 测试3：查询数据
            System.out.println("\n测试3：查询数据");
            try {
                List<Record> records = Db.find("SELECT * FROM email_test_log ORDER BY id DESC LIMIT 5");
                System.out.println("查询到 " + records.size() + " 条记录：");
                for (Record record : records) {
                    System.out.println("ID: " + record.getLong("id") +
                            ", 账号ID: " + record.getInt("account_id") +
                            ", 消息: " + record.getStr("message") +
                            ", 创建时间: " + record.getDate("created_at"));
                }
            } catch (Exception e) {
                System.out.println("查询数据失败: " + e.getMessage());
                e.printStackTrace();
            }

            // 测试4：测试邮件表
            System.out.println("\n测试4：测试邮件表");
            try {
                int count = Db.queryInt("SELECT COUNT(*) FROM email_messages");
                System.out.println("邮件表中有 " + count + " 条记录");

                if (count > 0) {
                    Record latestEmail = Db.findFirst("SELECT * FROM email_messages ORDER BY id DESC LIMIT 1");
                    System.out.println("最新邮件：");
                    System.out.println("ID: " + latestEmail.getLong("id") +
                            ", 账号ID: " + latestEmail.getInt("account_id") +
                            ", 主题: " + latestEmail.getStr("subject") +
                            ", 发件人: " + latestEmail.getStr("from_address"));
                }
            } catch (Exception e) {
                System.out.println("测试邮件表失败: " + e.getMessage());
                e.printStackTrace();
            }

            // 测试5：测试事务
            System.out.println("\n测试5：测试事务");
            try {
                boolean txResult = Db.tx(() -> {
                    Record record1 = new Record();
                    record1.set("account_id", 1);
                    record1.set("message", "事务测试1");
                    record1.set("created_at", new Date());
                    boolean save1 = Db.save("email_test_log", record1);

                    Record record2 = new Record();
                    record2.set("account_id", 1);
                    record2.set("message", "事务测试2");
                    record2.set("created_at", new Date());
                    boolean save2 = Db.save("email_test_log", record2);

                    return save1 && save2;
                });

                if (txResult) {
                    System.out.println("事务测试成功！");
                } else {
                    System.out.println("事务测试失败！");
                }
            } catch (Exception e) {
                System.out.println("事务测试失败: " + e.getMessage());
                e.printStackTrace();
            }

            System.out.println("\n=== 数据库测试完成 ===");
        } catch (Exception e) {
            System.out.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
