package cn.jbolt.mail.gpt.fetch.plugin;

import cn.jbolt.common.model.EmailAccount;
import cn.jbolt.common.model.EmailMessages;
import cn.jbolt.mail.gpt.client.EmailClient;
import cn.jbolt.mail.gpt.client.EmailClientPool;
import cn.jbolt.mail.gpt.fetch.EmailAccountMonitor;
import com.jfinal.plugin.IPlugin;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static cn.jbolt.mail.gpt.fetch.plugin.EmailFetchPluginKit.shutdownResources;

/**
 * 空闲完整邮件获取插件
 * 系统空闲时间就查数据库，把所有fetch_status为0的邮件，完整接收下来（包含附件和正文cid附件的转换到本地磁盘）
 */
public class EmailFetchPluginIdleComplete implements IPlugin, EmailAccountMonitor.EmailAccountChangeListener {
    private static final Logger LOG = LoggerFactory.getLogger(EmailFetchPluginIdleComplete.class);

    // 运行状态
    private final AtomicBoolean running = new AtomicBoolean(false);

    // 邮件客户端池
    private final EmailClientPool clientPool = EmailClientPool.getInstance();
    // 轮询间隔（秒）
    private final int pollingInterval;
    // 最大并发任务数
    private final int maxConcurrentTasks;
    // 每次处理的邮件数量
    private final int batchSize;
    // 非工作时间开始（小时）
    private final int offHourStart;
    // 非工作时间结束（小时）
    private final int offHourEnd;
    // 执行器服务
    private ScheduledExecutorService scheduledExecutor;
    private ExecutorService fetchExecutor;

    /**
     * 构造函数
     *
     * @param pollingInterval    轮询间隔（秒）
     * @param maxConcurrentTasks 最大并发任务数
     * @param batchSize          每次处理的邮件数量
     * @param offHourStart       非工作时间开始（小时）
     * @param offHourEnd         非工作时间结束（小时）
     */
    public EmailFetchPluginIdleComplete(int pollingInterval, int maxConcurrentTasks, int batchSize,
                                        int offHourStart, int offHourEnd) {
        this.pollingInterval = pollingInterval;
        this.maxConcurrentTasks = maxConcurrentTasks;
        this.batchSize = batchSize;
        this.offHourStart = offHourStart;
        this.offHourEnd = offHourEnd;
    }

    /**
     * 构造函数（使用默认值）
     */
    public EmailFetchPluginIdleComplete() {
        this(300, 2, 50, 23, 6); // 默认5分钟轮询一次，最多2个并发任务，每次处理50封邮件，非工作时间为晚上11点到早上6点
    }

    @Override
    public boolean start() {
        if (running.compareAndSet(false, true)) {
            try {
                LOG.info("启动空闲完整邮件获取插件");

                // 创建线程池
                scheduledExecutor = Executors.newScheduledThreadPool(1);
                fetchExecutor = Executors.newFixedThreadPool(maxConcurrentTasks);

                // 启动定时任务
                scheduledExecutor.scheduleWithFixedDelay(
                        this::processQuickEmails,
                        0, // 立即开始
                        pollingInterval,
                        TimeUnit.SECONDS
                );

                // 注册邮箱账号监控器
                EmailAccountMonitor.getInstance().addListener(this);
                // 启动邮箱账号监控器
                EmailAccountMonitor.getInstance().start(scheduledExecutor);

                return true;
            } catch (Exception e) {
                LOG.error("启动空闲完整邮件获取插件失败: " + e.getMessage(), e);
                running.set(false);
                return false;
            }
        } else {
            LOG.warn("空闲完整邮件获取插件已在运行中");
            return true;
        }
    }

    @Override
    public boolean stop() {
        if (running.compareAndSet(true, false)) {
            try {
                LOG.info("停止空闲完整邮件获取插件");
                return shutdownResources(this, scheduledExecutor, fetchExecutor);
            } catch (Exception e) {
                LOG.error("停止空闲完整邮件获取插件失败: " + e.getMessage(), e);
                return false;
            }
        } else {
            LOG.warn("空闲完整邮件获取插件未运行");
            return true;
        }
    }

    /**
     * 处理快速接收的邮件
     */
    private void processQuickEmails() {
        if (!running.get()) {
            return;
        }

        try {
            LOG.info("开始处理快速接收的邮件");

            // 获取所有fetch_status为0的邮件
            List<EmailMessages> messages = new EmailMessages().dao().find(
                    "SELECT * FROM email_messages WHERE fetch_status = 0 ORDER BY id ASC LIMIT ?",
                    batchSize);

            if (messages.isEmpty()) {
                LOG.info("没有需要处理的快速接收邮件");
                return;
            }

            LOG.info("找到 {} 封需要处理的快速接收邮件", messages.size());

            // 按邮箱分组处理
            messages.stream()
                    .collect(Collectors.groupingBy(EmailMessages::getAccountId))
                    .forEach((accountId, accountMessages) -> {
                        if (!running.get()) return;

                        fetchExecutor.submit(() -> {
                            try {
                                processMessagesForAccount(accountId, accountMessages);
                            } catch (Exception e) {
                                LOG.error("处理邮箱 {} 的快速接收邮件失败: {}", accountId, e.getMessage(), e);
                            }
                        });
                    });
        } catch (Exception e) {
            LOG.error("处理快速接收的邮件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理指定邮箱的邮件
     *
     * @param accountId 邮箱账号ID
     * @param messages  邮件列表
     */
    private void processMessagesForAccount(Integer accountId, List<EmailMessages> messages) {
        if (!running.get() || messages.isEmpty()) {
            return;
        }

        // 获取邮箱账号
        EmailAccount account = new EmailAccount().dao().findById(accountId);
        if (account == null || !account.isEnabled()) {
            LOG.warn("邮箱账号不存在或未启用: {}", accountId);
            return;
        }

        LOG.info("开始处理邮箱 {} 的 {} 封快速接收邮件", account.getUsername(), messages.size());

        // 获取邮件客户端
        EmailClient client = clientPool.getClient(account, EmailClient.Mode.IDLE_MODE);

        try {
            // 连接邮件服务器
            if (!client.connect()) {
                LOG.error("连接邮箱 {} 失败", account.getUsername());
                return;
            }

            // 处理每封邮件
            int successCount = 0;
            for (EmailMessages message : messages) {
                if (!running.get()) break;

                try {
                    // 获取邮件所在文件夹
                    String folderName = message.getFolderName();
                    if (folderName == null || folderName.isEmpty()) {
                        folderName = "INBOX"; // 默认文件夹
                    }

                    // 完整接收邮件
                    boolean success = client.fetchSingleEmail(folderName, message);
                    if (success) {
                        successCount++;
                    }
                } catch (Exception e) {
                    LOG.error("处理邮件 {} 失败: {}", message.getId(), e.getMessage(), e);
                }
            }

            LOG.info("完成处理邮箱 {} 的快速接收邮件，成功处理 {} 封邮件", account.getUsername(), successCount);
        } catch (Exception e) {
            LOG.error("处理邮箱 {} 的快速接收邮件失败: {}", account.getUsername(), e.getMessage(), e);
        }
    }

    /**
     * 手动触发处理快速接收的邮件
     *
     * @param count 处理的邮件数量
     */
    public void triggerProcessQuickEmails(int count) {
        if (!running.get()) {
            LOG.warn("空闲完整邮件获取插件未运行，无法触发处理");
            return;
        }

        LOG.info("手动触发处理 {} 封快速接收的邮件", count);

        try {
            // 获取所有fetch_status为0的邮件
            List<EmailMessages> messages = new EmailMessages().dao().find(
                    "SELECT * FROM email_messages WHERE fetch_status = 0 ORDER BY id ASC LIMIT ?",
                    count);

            if (messages.isEmpty()) {
                LOG.info("没有需要处理的快速接收邮件");
                return;
            }

            LOG.info("找到 {} 封需要处理的快速接收邮件", messages.size());

            // 按邮箱分组处理
            messages.stream()
                    .collect(Collectors.groupingBy(EmailMessages::getAccountId))
                    .forEach((accountId, accountMessages) -> {
                        if (!running.get()) return;

                        fetchExecutor.submit(() -> {
                            try {
                                processMessagesForAccount(accountId, accountMessages);
                            } catch (Exception e) {
                                LOG.error("处理邮箱 {} 的快速接收邮件失败: {}", accountId, e.getMessage(), e);
                            }
                        });
                    });
        } catch (Exception e) {
            LOG.error("手动触发处理快速接收的邮件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 手动触发处理指定邮箱的快速接收邮件
     *
     * @param accountId 邮箱账号ID
     * @param count     处理的邮件数量
     */
    public void triggerProcessQuickEmailsForAccount(Integer accountId, int count) {
        if (!running.get()) {
            LOG.warn("空闲完整邮件获取插件未运行，无法触发处理");
            return;
        }

        // 获取邮箱账号
        EmailAccount account = new EmailAccount().dao().findById(accountId);
        if (account == null || !account.isEnabled()) {
            LOG.warn("邮箱账号不存在或未启用: {}", accountId);
            return;
        }

        // 验证邮箱账号有效性
        if (!EmailAccountMonitor.getInstance().triggerAccountValidation(accountId)) {
            LOG.warn("邮箱账号验证失败，跳过处理: {}", account.getUsername());
            return;
        }

        LOG.info("手动触发处理邮箱 {} 的 {} 封快速接收邮件", account.getUsername(), count);

        try {
            // 获取指定邮箱的fetch_status为0的邮件
            List<EmailMessages> messages = new EmailMessages().dao().find(
                    "SELECT * FROM email_messages WHERE account_id = ? AND fetch_status = 0 ORDER BY id ASC LIMIT ?",
                    accountId, count);

            if (messages.isEmpty()) {
                LOG.info("邮箱 {} 没有需要处理的快速接收邮件", account.getUsername());
                return;
            }

            LOG.info("找到邮箱 {} 的 {} 封需要处理的快速接收邮件", account.getUsername(), messages.size());

            fetchExecutor.submit(() -> {
                try {
                    processMessagesForAccount(accountId, messages);
                } catch (Exception e) {
                    LOG.error("处理邮箱 {} 的快速接收邮件失败: {}", account.getUsername(), e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            LOG.error("手动触发处理邮箱 {} 的快速接收邮件失败: {}", account.getUsername(), e.getMessage(), e);
        }
    }

    /**
     * 处理新添加的邮箱账号
     * 实现EmailAccountChangeListener接口
     *
     * @param account 添加的邮箱账号
     */
    @Override
    public void onAccountAdded(EmailAccount account) {
        // 对于空闲完整获取插件，不需要特殊处理
        // 因为它只处理数据库中已存在的邮件
    }

    /**
     * 处理移除的邮箱账号
     * 实现EmailAccountChangeListener接口
     *
     * @param accountId 移除的邮箱账号ID
     */
    @Override
    public void onAccountRemoved(Integer accountId) {
        // 对于空闲完整获取插件，不需要特殊处理
        // 因为它只处理数据库中已存在的邮件
    }
}