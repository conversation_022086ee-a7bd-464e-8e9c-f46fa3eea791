-- 邮箱账号表
CREATE TABLE IF NOT EXISTS `email_accounts` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `email` varchar(128) NOT NULL COMMENT '邮箱地址',
  `password` varchar(255) NOT NULL COMMENT '邮箱密码/授权码',
  `display_name` varchar(128) NULL COMMENT '显示名称',
  `protocol` varchar(20) NOT NULL DEFAULT 'imap' COMMENT '协议(imap/pop3)',
  `host` varchar(128) NOT NULL COMMENT '服务器地址',
  `port` int(11) NOT NULL COMMENT '服务器端口',
  `ssl_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用SSL',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(1:启用,0:禁用)',
  `receive_mode` tinyint(1) NOT NULL DEFAULT 0 COMMENT '接收模式(0:轮询,1:IDLE监听)',
  `polling_interval` int(11) DEFAULT 60 COMMENT '轮询间隔(秒)',
  `first_sync_completed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否完成首次同步',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮箱账号表';

-- 邮箱文件夹表
CREATE TABLE IF NOT EXISTS `email_folders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) NOT NULL COMMENT '邮箱账号ID',
  `full_name` varchar(255) NOT NULL COMMENT '文件夹全名',
  `display_name` varchar(128) NOT NULL COMMENT '显示名称',
  `folder_type` varchar(50) DEFAULT NULL COMMENT '文件夹类型',
  `priority` tinyint(1) NOT NULL DEFAULT 3 COMMENT '优先级(1:最高,2:高,3:普通,4:低,0:忽略)',
  `message_count` int(11) DEFAULT 0 COMMENT '邮件总数',
  `unread_count` int(11) DEFAULT 0 COMMENT '未读邮件数',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_account_folder` (`account_id`,`full_name`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮箱文件夹表';

-- 邮件表
CREATE TABLE IF NOT EXISTS `email_messages` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `email_account` varchar(128) NOT NULL COMMENT '邮箱账号',
  `message_id` varchar(512) NOT NULL COMMENT '邮件ID',
  `message_hash` varchar(64) NOT NULL COMMENT '唯一哈希',
  `subject` text DEFAULT NULL COMMENT '主题',
  `from_display` varchar(255) NOT NULL COMMENT '发件人名称',
  `from_address` varchar(255) NOT NULL COMMENT '发件人邮箱',
  `to_address` text DEFAULT NULL COMMENT '收件人邮箱',
  `cc_address` text DEFAULT NULL COMMENT '抄送邮箱',
  `bcc_address` text DEFAULT NULL COMMENT '密送邮箱',
  `content_html` longtext DEFAULT NULL COMMENT 'HTML正文',
  `content_text` longtext DEFAULT NULL COMMENT '文本正文',
  `content_type` varchar(50) DEFAULT NULL COMMENT '内容类型',
  `sent_date` datetime DEFAULT NULL COMMENT '发送时间',
  `received_date` datetime DEFAULT NULL COMMENT '接收时间',
  `has_attachments` tinyint(1) DEFAULT 0 COMMENT '是否有附件',
  `folder_name` varchar(255) NOT NULL COMMENT '文件夹名称',
  `attachment_path` varchar(255) DEFAULT NULL COMMENT '附件路径',
  `attachment_files` text DEFAULT NULL COMMENT '附件文件(JSON)',
  `is_important` tinyint(1) DEFAULT 0 COMMENT '是否重要',
  `is_read` tinyint(1) DEFAULT 0 COMMENT '是否已读',
  `is_sent` tinyint(1) DEFAULT 0 COMMENT '是否已发送',
  `is_draft` tinyint(1) DEFAULT 0 COMMENT '是否草稿',
  `original_email_id` varchar(64) DEFAULT NULL COMMENT '原始邮件ID',
  `size` int(11) DEFAULT NULL COMMENT '邮件大小',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_message_hash` (`message_hash`),
  KEY `idx_email_account` (`email_account`),
  KEY `idx_message_id` (`message_id`(191)),
  KEY `idx_sent_date` (`sent_date`),
  KEY `idx_received_date` (`received_date`),
  KEY `idx_folder` (`folder_name`(191)),
  KEY `idx_account_date` (`email_account`, `sent_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件表';

-- 附件表
CREATE TABLE IF NOT EXISTS `email_attachments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `email_id` bigint(20) NOT NULL COMMENT '邮件ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小',
  `content_type` varchar(255) DEFAULT NULL COMMENT '内容类型',
  `disposition` varchar(20) DEFAULT NULL COMMENT '处理方式(attachment/inline)',
  `cid` varchar(255) DEFAULT NULL COMMENT '内容ID(用于内联附件)',
  `path` varchar(512) DEFAULT NULL COMMENT '本地路径',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态(0:待下载,1:下载中,2:已完成,3:下载失败)',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_email_id` (`email_id`),
  KEY `idx_status` (`status`),
  KEY `idx_cid` (`cid`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件附件表'; 