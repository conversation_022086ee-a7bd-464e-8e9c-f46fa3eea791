#define jboltLayout()
	#setLocal(JboltWithTabs=JBoltUserConfigCache.getCurrentUserJBoltAdminWithTabs())
	#setLocal(enableSystemAdminH50=JBoltGlobalConfigCache.getSystemAdminH50())
	#setLocal(jboltLayerTop=(JboltWithTabs?41:1)+(enableSystemAdminH50?51:61))
	#setLocal(jboltAutoLockScreenSeconds=JBoltGlobalConfigCache.getJBoltAutoLockScreenSeconds())
	#setLocal(jboltTabKeepAfterReload=JBoltGlobalConfigCache.getJboltTabKeepAfterReload())
	#if(_jb_rqtype_!=null)
		#switch (_jb_rqtype_)
		#case (JBoltRequestType.PJAX)
			#@pjaxLayout?()
		#case (JBoltRequestType.AJAXPORTAL)
			#@ajaxPortalLayout?()
		#case (JBoltRequestType.AJAX)
			#@ajaxLayout?()
		#case (JBoltRequestType.DIALOG)
			#@dialogLayout?()
		#case (JBoltRequestType.IFRAME)
			#@iframeLayout?()
		#default
			#@doAdminLayout?()
		#end
	#else
		#@doAdminLayout?()
	#end
#end


#--
	后台主要layout自行识别判断处理 默认都用jboltLayout 这里保留是为了兼容老旧项目
--#

#define adminLayout()
#@jboltLayout?()
#end

#define pjaxLayout()
<script>
	function isDashboardPage() {
		const pathname = window.location.pathname;
		console.log('[PJAX] 检查页面路径:', pathname);
		
		// 更准确的dashboard页面检测 - 基于实际路由配置
		const isDashboard = pathname === '/admin' || 
		                   pathname === '/admin/' ||
		                   pathname.indexOf('/admin/dashboard') !== -1 ||
		                   pathname.indexOf('/admin/index') !== -1;
		
		console.log('[PJAX] 是否为dashboard页面:', isDashboard);
		return isDashboard;
	}

	$(document).on('pjax:start', function() {
		console.log('[PJAX] 页面切换开始，当前页面:', window.location.pathname);
		if (window.dashboardPage && typeof window.dashboardPage.destroy === 'function') {
			console.log('[PJAX] 销毁dashboard页面资源');
			window.dashboardPage.destroy();
		}
	});
	
	$(document).on('pjax:end', function() {
		console.log('[PJAX] 页面切换完成，新页面:', window.location.pathname);
		
		// 检查是否为dashboard页面
		if (isDashboardPage()) {
			console.log('[PJAX] 检测到dashboard页面，准备初始化...');
			
			// 等待一小段时间确保页面完全加载
			setTimeout(function() {
				// 检查dashboardPage对象是否已定义
				if (window.dashboardPage && typeof window.dashboardPage.init === 'function') {
					console.log('[PJAX] 初始化dashboard页面');
					// 暂时注释掉，避免重复初始化导致无限循环
					// window.dashboardPage.init();
				} else {
					console.log('[PJAX] dashboardPage对象尚未定义，等待加载...');
					// 如果对象还没定义，等待更长时间后重试
					setTimeout(function() {
						if (window.dashboardPage && typeof window.dashboardPage.init === 'function') {
							console.log('[PJAX] 延迟初始化dashboard页面');
							// 暂时注释掉，避免重复初始化导致无限循环
							// window.dashboardPage.init();
						} else {
							console.warn('[PJAX] dashboard页面初始化失败，对象仍未定义');
							// 最后一次尝试：检查是否有强制初始化函数
							if (window.initDashboard && typeof window.initDashboard === 'function') {
								console.log('[PJAX] 尝试使用全局初始化函数');
								// 暂时注释掉，避免重复初始化导致无限循环
								// window.initDashboard();
							}
						}
					}, 1500);
				}
			}, 500);
		}
	});
	
	// 监听pjax:complete事件，作为备用初始化时机
	$(document).on('pjax:complete', function() {
		console.log('[PJAX] pjax:complete事件触发，检查dashboard页面...');
		if (isDashboardPage()) {
			// 延迟更长时间，确保所有资源加载完成
			setTimeout(function() {
				if (window.dashboardPage && typeof window.dashboardPage.init === 'function') {
					console.log('[PJAX] 在pjax:complete事件中初始化dashboard页面');
					// 暂时注释掉，避免重复初始化导致无限循环
					// window.dashboardPage.init();
				} else if (window.initDashboard && typeof window.initDashboard === 'function') {
					console.log('[PJAX] 在pjax:complete事件中使用全局初始化函数');
					// 暂时注释掉，避免重复初始化导致无限循环
					// window.initDashboard();
				}
			}, 800);
		}
	});
	
	// 监听pjax:success事件，作为额外的初始化时机
	$(document).on('pjax:success', function() {
		console.log('[PJAX] pjax:success事件触发');
		if (isDashboardPage()) {
			setTimeout(function() {
				if (window.dashboardPage && typeof window.dashboardPage.forceInit === 'function') {
					console.log('[PJAX] 在pjax:success事件中强制初始化dashboard页面');
					// 暂时注释掉，避免重复初始化导致无限循环
					// window.dashboardPage.forceInit();
				}
			}, 1000);
		}
	});
	
	// 添加一个全局的检查函数，确保dashboard页面在各种情况下都能初始化
	window.checkAndInitDashboard = function() {
		console.log('[Global] 检查并初始化dashboard页面...');
		
		if (!isDashboardPage()) {
			console.log('[Global] 当前不是dashboard页面，跳过初始化');
			return;
		}
		
		// 检查页面是否已经有邮件数据
		const emailCountSpan = document.getElementById('emailCountSpan');
		const hasEmailData = emailCountSpan && emailCountSpan.textContent && emailCountSpan.textContent !== '0';
		
		if (hasEmailData) {
			console.log('[Global] dashboard页面已有数据，无需重新初始化');
			return;
		}
		
		console.log('[Global] dashboard页面需要初始化，尝试初始化...');
		
		if (window.dashboardPage && typeof window.dashboardPage.forceInit === 'function') {
			// 暂时注释掉，避免重复初始化导致无限循环
			// window.dashboardPage.forceInit();
		} else if (window.initDashboard && typeof window.initDashboard === 'function') {
			// 暂时注释掉，避免重复初始化导致无限循环
			// window.initDashboard();
		} else {
			console.warn('[Global] 无法找到dashboard初始化函数');
		}
	};
	
	// 暂时注释掉定时检查，避免重复初始化
	/*
	// 每5秒检查一次dashboard页面状态（仅在dashboard页面时）
	setInterval(function() {
		if (isDashboardPage()) {
			const emailCountSpan = document.getElementById('emailCountSpan');
			if (emailCountSpan && (!emailCountSpan.textContent || emailCountSpan.textContent === '0')) {
				console.log('[Monitor] 检测到dashboard页面数据异常，尝试重新初始化...');
				window.checkAndInitDashboard();
			}
		}
	}, 5000);
	*/
</script>
#@css?()
#@main?()
#@jslib?()
#@js?()
#end

#define ajaxLayout()
#@css?()
#@main?()
#@jslib?()
#@js?()
#end

#define ajaxPortalLayout()
#@css?()
#@main?()
#@jslib?()
#@js?()
#end

#define dialogLayout()
<!DOCTYPE html>
<html>
	<head>
		<base href="#(basepath??)" />
		<meta charset="utf-8" />
		<title>#(title??)</title>
		 #if(need_always_https)
	    	<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
		 #end
		<meta name="renderer" content="webkit">
		<link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.ico" />
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
		<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
		#@fontcss?()
		#@bootstrapcss?()
		<!-- jstree树形组件css资源 如果系统不使用jstree 可以注释掉这个地方 -->
		#@jstreecss?()
		<!-- Select2组件css资源 如果系统不使用select2 可以注释掉这个地方 -->
		#@select2css?()
		<!-- Bootstrap-select组件css资源 如果系统不使用bootstrap-select 可以注释掉这个地方 -->
		#@bootstrapselectcss?()
		<!-- 系统后台管理核心封装CSS 与Bootstrap-->
		#@maincss?()
		<!-- 系统后台本项目特殊使用封装CSS -->
		#@minecss?()
		<!-- css动画 -->
		#@animatecss?()
		#@jqueryjs?()
		<script type="text/javascript">
		var needPjax=false;//默认是否pjax加载
		var autoShowRequiredLabelStar=true;//默认是否自动处理表单中的必填项的红星样式
		</script>
		<style type="text/css">
		.note-editable p{
		margin-bottom: 0px;
		}  
 		</style>
		#@css?()
		
	</head>
	<body ontouchstart>
		<span class="d-none" id="JBOLT_ASSETS_VERSION">#(JBoltConfig.ASSETS_VERSION)</span>
		<span class="d-none" id="JBOLT_BASE_UPLOAD_PATH_PRE">#(JBOLT_BASE_UPLOAD_PATH_PRE??)</span>
		<span class="d-none" id="JBOLT_AUTO_LOCK_SCREEN_SECONDS">#(jboltAutoLockScreenSeconds??)</span>
		<span class="d-none" id="JBOLT_TAB_KEEP_AFTER_RELOAD">#(jboltTabKeepAfterReload??)</span>
		<span class="d-none" id="JBOLT_GLOBAL_UPLOAD_TO">#(JBOLT_GLOBAL_UPLOAD_TO?? 'local')</span>
		#@main?()
		#include("__jbolttable_filterbox.html") 
		#@bootstrapjs?()
		#@jboltmaindialogjslib?()
		<!-- jstree树形组件js资源 如果系统不使用jstree树形组件 可以注释掉这个地方 -->
		#@jstreejs?()
		<!-- select2组件js -->
		#@select2js?()
		<!-- bootstrap-select组件js -->
		#@bootstrapselectjs?()
		#@jslib?()
		<!-- 系统后台核心JS封装库 -->
		#@mainjs?()
		<!-- 系统后台本项目特殊使用封装JS -->
		#@minejs?()
		#if(JBoltConfig.JBOLT_WEBSOCKET_ENABLE)
		<script>
			window.JBoltWS = parent.JBoltWS;
		</script>
		#end
		#@js?()
	</body>
</html>
#end


#define iframeLayout()
<!DOCTYPE html>
<html>
	<head>
		<base href="#(basepath??)" />
		<meta charset="utf-8" />
		 #if(need_always_https)
	    	<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
		 #end
		<meta name="renderer" content="webkit">
		<link rel="shortcut icon" type="image/x-icon" href="assets/img/favicon.ico" />
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
		<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
		#@fontcss?()
		#@bootstrapcss?()
		<!-- jstree树形组件css资源 如果系统不使用jstree 可以注释掉这个地方 -->
		#@jstreecss?()
		<!-- Select2组件css资源 如果系统不使用select2 可以注释掉这个地方 -->
		#@select2css?()
		<!-- Bootstrap-select组件css资源 如果系统不使用bootstrap-select 可以注释掉这个地方 -->
		#@bootstrapselectcss?()
		<!-- 系统后台管理核心封装CSS 与Bootstrap-->
		#@maincss?()
		<!-- 系统后台本项目特殊使用封装CSS -->
		#@minecss?()
		<!-- css动画 -->
		#@animatecss?()
		#@jqueryjs?()
		<script type="text/javascript">
		var needPjax=false;//默认是否pjax加载
		var autoShowRequiredLabelStar=true;//默认是否自动处理表单中的必填项的红星样式
		</script>
		
		<style type="text/css">
		.note-editable p{
		margin-bottom: 0px;
		}  
 		</style>
		#@css?()
	</head>
	<body ontouchstart>
		<span class="d-none" id="JBOLT_ASSETS_VERSION">#(JBoltConfig.ASSETS_VERSION)</span>
		<span class="d-none" id="JBOLT_BASE_UPLOAD_PATH_PRE">#(JBOLT_BASE_UPLOAD_PATH_PRE??)</span>
		<span class="d-none" id="JBOLT_AUTO_LOCKSCREEN_SECONDS">#(jboltAutoLockScreenSeconds??)</span>
		<span class="d-none" id="JBOLT_TAB_KEEP_AFTER_RELOAD">#(jboltTabKeepAfterReload??)</span>
		<span class="d-none" id="JBOLT_GLOBAL_UPLOAD_TO">#(JBOLT_GLOBAL_UPLOAD_TO?? 'local')</span>
		#@main?()
		#include("__jbolttable_filterbox.html") 
		#@bootstrapjs?()
		#@jboltmaindialogjslib?()
		<!-- jstree树形组件js资源 如果系统不使用jstree树形组件 可以注释掉这个地方 -->
		#@jstreejs?()
		<!-- select2组件js -->
		#@select2js?()
		<!-- bootstrap-select组件js -->
		#@bootstrapselectjs?()
		#@jslib?()
		<!-- 系统后台核心JS封装库 -->
		#@mainjs?()
		<!-- 系统后台本项目特殊使用封装JS -->
		#@minejs?()
		#@js?()
	</body>
</html>
#end