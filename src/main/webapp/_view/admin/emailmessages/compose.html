#@jboltLayout()
#define main()
<div class="container-fluid">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <i class="fa fa-envelope"></i> #(type == 'reply' ? '回复邮件' : '转发邮件')
            <div class="float-right">
                <button type="button" class="btn btn-sm btn-light" id="sendBtn" title="发送邮件 (Ctrl+Enter)"><i class="fa fa-paper-plane"></i> 发送</button>
                <button class="btn btn-sm btn-light ml-1" id="saveBtn"><i class="fa fa-save"></i> 保存草稿</button>
                <button class="btn btn-sm btn-light ml-1" id="cancelBtn"><i class="fa fa-times"></i> 取消</button>
            </div>
        </div>
        <div class="card-body">
            <form id="emailForm">
                <input type="hidden" name="emailId" value="#(emailId)">
                <input type="hidden" name="type" value="#(type)">
                
                <div class="form-group row">
                    <label class="col-md-1 col-form-label">发件人</label>
                    <div class="col-md-11">
                        <select name="fromAccountId" class="form-control" id="fromAccount" required>
                            <!-- 邮箱账户选项将通过AJAX加载 -->
                        </select>
                    </div>
                </div>
                
                <div class="form-group row">
                    <label class="col-md-1 col-form-label">收件人</label>
                    <div class="col-md-11">
                        <input type="text" class="form-control" name="to" id="to" placeholder="收件人，多个邮箱请用分号分隔" required>
                    </div>
                </div>
                
                <div class="form-group row">
                    <label class="col-md-1 col-form-label">抄送</label>
                    <div class="col-md-11">
                        <input type="text" class="form-control" name="cc" id="cc" placeholder="抄送，多个邮箱请用分号分隔">
                    </div>
                </div>
                
                <div class="form-group row">
                    <label class="col-md-1 col-form-label">主题</label>
                    <div class="col-md-11">
                        <input type="text" class="form-control" name="subject" id="subject" placeholder="邮件主题" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <textarea id="emailContent" name="content"></textarea>
                </div>
                
                <div class="form-group">
                    <div class="custom-file">
                        <input type="file" class="custom-file-input" id="attachments" name="attachments" multiple>
                        <label class="custom-file-label" for="attachments">选择附件</label>
                    </div>
                    <div id="attachmentList" class="mt-2"></div>
                </div>
            </form>
        </div>
    </div>
</div>
#end

#define css()
<link rel="stylesheet" href="/assets/css/tinymce-table-enhancement.css">
<style>
    #attachmentList .attachment-item {
        display: inline-block;
        padding: 5px 10px;
        margin-right: 10px;
        margin-bottom: 5px;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    #attachmentList .attachment-remove {
        cursor: pointer;
        margin-left: 5px;
        color: #dc3545;
    }
</style>
#end

#define js()
<script src="/assets/libs/tinymce/tinymce.min.js"></script>
<script src="/assets/js/tinymce-table-enhancement.js"></script>
<script type="text/javascript">
    $(function() {
        // 初始化富文本编辑器
        tinymce.init({
            selector: '#emailContent',
            height: 400,
            plugins: 'link image code table tableenhancement lists emoticons',
            toolbar: 'undo redo | bold italic | forecolor backcolor | alignleft aligncenter alignright | code | table optimizetable | numlist bullist | link image | emoticons',
            menubar: false,

            // 表格配置 - 启用表格调整功能
            table_toolbar: 'tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol',
            table_appearance_options: true,
            table_grid: true,
            table_resize_bars: true,
            table_column_resizing: true,
            table_row_resizing: true,
            table_sizing_mode: 'relative',
            table_default_attributes: {
                'border': '1',
                'cellpadding': '5',
                'cellspacing': '0',
                'style': 'border-collapse: collapse; width: 100%; table-layout: auto;'
            },
            table_default_styles: {
                'border-collapse': 'collapse',
                'width': '100%',
                'table-layout': 'auto'
            },

            // 邮件兼容的内容样式
            content_style: `
                body {
                    font-family: "Microsoft YaHei", Arial, sans-serif;
                    font-size: 12pt;
                    line-height: 1.6;
                }
                table {
                    border-collapse: collapse !important;
                    mso-table-lspace: 0pt !important;
                    mso-table-rspace: 0pt !important;
                    border-spacing: 0 !important;
                    table-layout: fixed !important;
                    margin: 0 auto !important;
                }
                table td, table th {
                    border: 1px solid #ddd !important;
                    padding: 8px !important;
                    vertical-align: top !important;
                    word-wrap: break-word !important;
                    word-break: break-all !important;
                }
                table th {
                    background-color: #f5f5f5 !important;
                    font-weight: bold !important;
                    text-align: center !important;
                }
                table[style*="width"] {
                    width: auto !important;
                    max-width: 100% !important;
                }
            `,
            setup: function(editor) {
                // 添加键盘快捷键支持
                editor.on('keydown', function(e) {
                    // Ctrl+Enter 或 Cmd+Enter 发送邮件
                    if ((e.ctrlKey || e.metaKey) && e.keyCode === 13) {
                        e.preventDefault();
                        $('#sendBtn').click();
                        return false;
                    }
                });
            },
            // 颜色配置
            color_map: [
                "000000", "黑色",
                "993300", "深红色",
                "333300", "深黄色",
                "003300", "深绿色",
                "003366", "深青色",
                "000080", "深蓝色",
                "333399", "靛蓝色",
                "333333", "深灰色",
                "800000", "栗色",
                "FF6600", "橙色",
                "808000", "橄榄色",
                "008000", "绿色",
                "008080", "青色",
                "0000FF", "蓝色",
                "666699", "灰蓝色",
                "808080", "灰色",
                "FF0000", "红色",
                "FF9900", "琥珀色",
                "99CC00", "黄绿色",
                "339966", "海绿色",
                "33CCCC", "浅青色",
                "3366FF", "浅蓝色",
                "800080", "紫色",
                "999999", "中灰色",
                "FF00FF", "洋红色",
                "FFCC00", "金色",
                "FFFF00", "黄色",
                "00FF00", "酸橙色",
                "00FFFF", "水蓝色",
                "00CCFF", "天蓝色",
                "993366", "红紫色",
                "C0C0C0", "银色",
                "FF99CC", "粉红色",
                "FFCC99", "桃色",
                "FFFF99", "浅黄色",
                "CCFFCC", "浅绿色",
                "CCFFFF", "浅青色",
                "99CCFF", "浅蓝色",
                "CC99FF", "淡紫色",
                "FFFFFF", "白色"
            ]
        });
        
        // 加载邮箱账户
        $.ajax({
            url: '/admin/emailMessages/getEmailAccounts',
            dataType: 'json',
            success: function(res) {
                if (res.state === 'ok') {
                    let options = '';
                    $.each(res.data, function(index, account) {
                        options += `<option value="${account.id}">${account.name} (${account.email})</option>`;
                    });
                    $('#fromAccount').html(options);
                    
                    // 加载原始邮件数据
                    loadEmailData();
                } else {
                    JBoltAlert.error(res.msg || '加载邮箱账户失败');
                }
            },
            error: function() {
                JBoltAlert.error('网络错误，请稍后重试');
            }
        });

        // 添加全局键盘快捷键支持
        $(document).on('keydown', function(e) {
            // Ctrl+Enter 或 Cmd+Enter 发送邮件
            if ((e.ctrlKey || e.metaKey) && e.keyCode === 13) {
                e.preventDefault();
                $('#sendBtn').click();
                return false;
            }
        });

        // 加载原始邮件数据
        function loadEmailData() {
            $.ajax({
                url: '/admin/emailMessages/getEmailForCompose',
                data: { id: '#(emailId)', type: '#(type)' },
                dataType: 'json',
                success: function(res) {
                    if (res.state === 'ok') {
                        fillEmailForm(res.data);
                    } else {
                        JBoltAlert.error(res.msg || '加载邮件数据失败');
                    }
                },
                error: function() {
                    JBoltAlert.error('网络错误，请稍后重试');
                }
            });
        }
        
        // 填充邮件表单
        function fillEmailForm(data) {
            if (!data) return;
            
            // 设置发件人账户
            if (data.emailAccountId) {
                $('#fromAccount').val(data.emailAccountId);
            }
            
            // 设置收件人
            if (data.to) {
                $('#to').val(data.to);
            }
            
            // 设置抄送
            if (data.cc) {
                $('#cc').val(data.cc);
            }
            
            // 设置主题
            if (data.subject) {
                $('#subject').val(data.subject);
            }
            
            // 设置内容
            if (data.content) {
                tinymce.get('emailContent').setContent(data.content);
            }
            
            // 显示附件
            if (data.attachments && data.attachments.length > 0) {
                let attachmentHtml = '';
                $.each(data.attachments, function(index, attachment) {
                    attachmentHtml += `
                    <div class="attachment-item" data-id="${attachment.id}">
                        <i class="fa fa-paperclip"></i>
                        ${attachment.fileName}
                        <span class="attachment-remove"><i class="fa fa-times"></i></span>
                    </div>`;
                });
                $('#attachmentList').html(attachmentHtml);
                
                // 附件删除事件
                $('.attachment-remove').click(function() {
                    $(this).parent().remove();
                });
            }
        }
        
        // 附件选择事件
        $('#attachments').change(function(e) {
            let files = e.target.files;
            if (files.length === 0) return;
            
            let attachmentHtml = $('#attachmentList').html() || '';
            
            $.each(files, function(index, file) {
                attachmentHtml += `
                <div class="attachment-item" data-file="${file.name}">
                    <i class="fa fa-paperclip"></i>
                    ${file.name} (${formatFileSize(file.size)})
                    <span class="attachment-remove"><i class="fa fa-times"></i></span>
                </div>`;
            });
            
            $('#attachmentList').html(attachmentHtml);
            
            // 附件删除事件
            $('.attachment-remove').click(function() {
                $(this).parent().remove();
            });
            
            // 重置文件输入
            $(this).val('');
        });
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 优化邮件中的表格样式，确保在各种邮件客户端中正常显示
        function optimizeEmailTables(htmlContent) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;

            const tables = tempDiv.querySelectorAll('table');
            tables.forEach(table => {
                table.setAttribute('cellpadding', '0');
                table.setAttribute('cellspacing', '0');
                table.setAttribute('border', '0');
                table.style.borderCollapse = 'collapse';
                table.style.msoTableLspace = '0pt';
                table.style.msoTableRspace = '0pt';
                table.style.borderSpacing = '0';
                table.style.width = '100%';
                table.style.maxWidth = '600px';
                table.style.margin = '0 auto';

                const cells = table.querySelectorAll('td, th');
                cells.forEach(cell => {
                    cell.style.border = '1px solid #ddd';
                    cell.style.padding = '8px';
                    cell.style.verticalAlign = 'top';
                    cell.style.wordWrap = 'break-word';
                    cell.style.wordBreak = 'break-all';
                    cell.style.msoLineHeightRule = 'exactly';

                    if (cell.tagName.toLowerCase() === 'th') {
                        cell.style.backgroundColor = '#f5f5f5';
                        cell.style.fontWeight = 'bold';
                        cell.style.textAlign = 'center';
                    }

                    cell.removeAttribute('width');
                    cell.removeAttribute('height');
                });
            });

            return tempDiv.innerHTML;
        }

        // 发送按钮点击事件
        $('#sendBtn').click(function() {
            if (!validateForm()) return;

            // 获取表单数据并优化表格
            let formData = new FormData($('#emailForm')[0]);
            let content = tinymce.get('emailContent').getContent();
            content = optimizeEmailTables(content);
            formData.append('content', content);
            
            // 添加选定的附件
            let attachmentFiles = document.getElementById('attachments').files;
            for (let i = 0; i < attachmentFiles.length; i++) {
                formData.append('attachmentFiles', attachmentFiles[i]);
            }
            
            // 添加保留的原始附件
            $('.attachment-item[data-id]').each(function() {
                formData.append('keepAttachments', $(this).data('id'));
            });
            
            $.ajax({
                url: '/admin/emailMessages/sendEmail',
                data: formData,
                type: 'POST',
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function(res) {
                    if (res.state === 'ok') {
                        JBoltAlert.success('邮件发送成功');
                        setTimeout(function() {
                            window.location.href = '/admin/emailMessages';
                        }, 1500);
                    } else {
                        JBoltAlert.error(res.msg || '邮件发送失败');
                    }
                },
                error: function() {
                    JBoltAlert.error('网络错误，请稍后重试');
                }
            });
        });
        
        // 保存草稿按钮点击事件
        $('#saveBtn').click(function() {
            if (!validateForm(true)) return;
            
            // 获取表单数据
            let formData = new FormData($('#emailForm')[0]);
            formData.append('content', tinymce.get('emailContent').getContent());
            formData.append('isDraft', true);
            
            // 添加选定的附件
            let attachmentFiles = document.getElementById('attachments').files;
            for (let i = 0; i < attachmentFiles.length; i++) {
                formData.append('attachmentFiles', attachmentFiles[i]);
            }
            
            // 添加保留的原始附件
            $('.attachment-item[data-id]').each(function() {
                formData.append('keepAttachments', $(this).data('id'));
            });
            
            $.ajax({
                url: '/admin/emailMessages/saveDraft',
                data: formData,
                type: 'POST',
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function(res) {
                    if (res.state === 'ok') {
                        JBoltAlert.success('草稿保存成功');
                    } else {
                        JBoltAlert.error(res.msg || '草稿保存失败');
                    }
                },
                error: function() {
                    JBoltAlert.error('网络错误，请稍后重试');
                }
            });
        });
        
        // 取消按钮点击事件
        $('#cancelBtn').click(function() {
            JBoltConfirm.confirm('确定要取消编辑吗？未保存的内容将丢失。', function() {
                window.location.href = '/admin/emailMessages';
            });
        });
        
        // 表单验证
        function validateForm(isDraft) {
            if (!isDraft) {
                if (!$('#fromAccount').val()) {
                    JBoltAlert.warning('请选择发件人账户');
                    return false;
                }
                
                if (!$('#to').val()) {
                    JBoltAlert.warning('请输入收件人');
                    return false;
                }
                
                if (!$('#subject').val()) {
                    JBoltAlert.warning('请输入邮件主题');
                    return false;
                }
            }
            
            return true;
        }
    });
</script>
#end 