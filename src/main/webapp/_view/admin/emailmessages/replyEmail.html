#if(originalEmail??)
    #set(originalSubject = originalEmail.subject??"")
    #if(isForward??)
        #if(originalSubject.startsWith("Fw:") || originalSubject.startsWith("FW:") || originalSubject.startsWith("fw:"))
            #set(subject = originalSubject)
        #else
            #set(subject = "Fw: " + originalSubject)
        #end
    #else
        #if(originalSubject.startsWith("Re:") || originalSubject.startsWith("RE:") || originalSubject.startsWith("re:"))
            #set(subject = originalSubject)
        #else
            #set(subject = "Re: " + originalSubject)
        #end
    #end
#else
    #set(subject = "")
#end
#set(title=pageTitle??"回复邮件" + subject??"")
#@jboltLayout()
#define main()
<div class="jbolt_page">
    <!-- 内容修改状态指示器 -->
    <div class="content-modified-indicator" id="contentModifiedIndicator">
        <i class="fa fa-exclamation-circle"></i> 邮件内容已修改，请注意保存
    </div>
    
    <div class="jbolt_page_content p-3">
        #if(draftError??)
        <!-- 草稿错误提示 -->
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fa fa-exclamation-triangle"></i> #(draftError)
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        #end
        
        #if(isLoadingDraft??)
        <!-- 草稿加载提示 -->
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fa fa-info-circle"></i> 正在编辑发送失败自动保存的草稿
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        #end
        
        <form id="replyEmailForm" action="/admin/emailMessages/sendEmail" method="post">
            <input type="hidden" name="originalEmailId" value="#(originalEmail.id??)"/>
            <input type="hidden" name="draftId" value="#(draftId??)"/>
            <input type="hidden" name="isForward" id="isForward" value="#(isForward ?? 'false')"/>
            <input type="hidden" name="uploadedAttachmentIds" id="uploadedAttachmentIds" value=""/>
            #if(isLoadingDraft?? && content??)
            <textarea id="draftContentHolder" style="display:none;">#(content)</textarea>
            #end

            <!-- 页面标题 -->
            <div class="mb-3">
                <h5>
                    #if(isLoadingDraft??)
                    <i class="fa fa-edit"></i> 编辑草稿
                    #elseif(originalEmail??)
                        #if(isForward??)
                        <i class="fa fa-share"></i> 转发邮件
                        #else
                        <i class="fa fa-reply"></i> 回复邮件
                        #end
                    #else
                    <i class="fa fa-pen"></i> 撰写新邮件
                    #end
                </h5>
            </div>

            <div class="form-group row">
                <label class="col-sm-1 col-form-label">发件箱</label>
                <div class="col-sm-11">
                    <select title="请选择发件箱" class="form-control" name="fromEmail" id="fromEmail" required
                            data-autoload
                            data-url="admin/emailAccount/userOptions"
                            data-text="=请选择发件箱="
                            data-value=""
                            data-value-attr="text"
                            #if(fromEmail??)
                            data-select="#(fromEmail)"
                            #elseif(originalEmail??)
                            data-select="#(originalEmail.toAddress??)"
                            #end
                            >
                    </select>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-1 col-form-label">收件人</label>
                <div class="col-sm-11">
                    <input title="请输入收件人" type="text" class="form-control" name="toEmail" id="toEmail" 
                           #if(toEmail??)
                           value="#(toEmail)"
                           #elseif(isForward?? && forwardToEmails??)
                           value="#(forwardToEmails)"
                           #elseif(originalEmail?? && !isForward??)
                           value="#(originalEmail.fromAddress??)"
                           #end
                           required/>
                    #if(isForward?? && forwardToEmails??)
                    <small class="form-text text-muted">
                        <i class="fa fa-info-circle"></i> 已自动设置收件人为客户关联公司的用户
                    </small>
                    #end
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-1 col-form-label">抄送</label>
                <div class="col-sm-11">
                    <input title="请输入抄送人" type="text" class="form-control" name="ccEmail" id="ccEmail"
                           #if(ccEmail??)
                           value="#(ccEmail)"
                           #end
                           placeholder="多个邮箱用逗号分隔"/>
                    #if(isReplyAll??)
                    <small class="form-text text-muted">
                        <i class="fa fa-info-circle"></i> 回复全部模式：已自动填入原邮件的抄送人
                    </small>
                    #end
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-1 col-form-label">主题</label>
                <div class="col-sm-11">
                    <input title="请输入邮件主题" type="text" class="form-control" name="subject" id="subject"
                           value="#(subject??)"
                           required/>
                </div>
            </div>

            <div class="form-group row">
                <label class="col-sm-1 col-form-label">正文</label>
                <div class="col-sm-11">
                    <div class="mb-2">
                        <div class="btn-group">
                            #if(originalEmail??)
                            <button type="button" class="btn btn-outline-secondary btn-sm"
                                    onclick="toggleOriginalEmail()">
                                <i class="fa fa-envelope-open"></i> 显示/隐藏原邮件
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm ms-2"
                                    onclick="quoteOriginalEmail()">
                                <i class="fa fa-quote-left"></i> 引用原邮件
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm ms-2"
                                    onclick="showTranslationContent()">
                                <i class="fa fa-language"></i> 引用翻译内容
                            </button>
                            #end
                            <button type="button" class="btn btn-outline-secondary btn-sm ms-2"
                                    onclick="showSignatures()">
                                <i class="fa fa-signature"></i> 插入签名
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm ms-2"
                                    onclick="showTemplates()">
                                <i class="fa fa-file-alt"></i> 使用模板
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm ms-2"
                                    onclick="showAiDialog()">
                                <i class="fa fa-robot"></i> AI写邮件
                            </button>
                            #if(originalEmail??)
                            <button type="button" class="btn btn-outline-info btn-sm ms-2"
                                    onclick="insertTranslationSubject()">
                                <i class="fa fa-language"></i> 插入翻译主题
                            </button>
                            #end
                        </div>
                    </div>
                    <textarea title="请输入邮件正文" id="emailContent" name="emailContent"></textarea>
                </div>
            </div>

            #if(translationContent??)
            <!-- 翻译内容 -->
            <div class="form-group row">
                <label class="col-sm-1 col-form-label">翻译</label>
                <div class="col-sm-11">
                    <div class="card">
                        <div class="card-body translation-content">
                            #(translationContent??)
                        </div>
                    </div>
                </div>
            </div>
            #end

            <div class="form-group row">
                <label class="col-sm-1 col-form-label">附件</label>
                <div class="col-sm-11">
                    <!-- 拖拽上传区域 -->
                    <div class="drag-drop-zone" id="dragDropZone">
                        <div class="drag-drop-content">
                            <i class="fa fa-cloud-upload drag-drop-icon"></i>
                            <p class="drag-drop-text">拖拽文件到此处上传</p>
                            <p class="drag-drop-subtext">或者 <a href="#" id="clickToUpload" class="text-primary">点击选择文件</a></p>
                            <small class="text-muted">支持多文件上传，包括HEIC/HEIF格式图片，建议单个文件不超过30MB</small>
                        </div>
                        <!-- 移除传统的文件上传字段，避免与即时上传功能冲突 -->
                        <input title="请选择文件" type="file" class="form-control-file" multiple id="attachmentInput" accept="*/*,.heic,.heif" style="position: absolute; left: -9999px; opacity: 0;" />
                    </div>
                    
                    <div class="mt-2" id="attachmentPreviewContainer" style="display: none;">
                        <p>
                            <strong>已上传的附件：</strong>
                            <span class="badge bg-primary ms-1" id="attachmentCount">0</span>
                            <button type="button" class="btn btn-sm btn-outline-danger ms-2" id="clearAttachments">
                                <i class="fa fa-trash"></i> 清空附件
                            </button>
                        </p>
                        <div class="d-flex flex-wrap" id="attachmentPreview"></div>
                        <div title="上传进度" id="uploadProgress" class="mt-2" style="display: none;">
                            <div class="progress">
                                <div title="上传进度" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted">正在上传附件...</small>
                        </div>
                    </div>
                    
                    #if(originalEmail?? && isForward?? && originalAttachments??)
                    <div class="mt-2" id="originalAttachments">
                        <p><strong>原始邮件附件：</strong></p>
                        <div class="list-group">
                            #for(attachment : originalAttachments)
                            <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <input title="请选择是否转发附件" type="checkbox" class="form-check-input me-2" name="forwardAttachments" 
                                           value="#(attachment.fileName)" checked id="att_#(attachment.fileName)">
                                    <label for="att_#(attachment.id)">#(attachment.fileName)</label>
                                    #if(attachment.isImage??)
                                    <button type="button" class="btn btn-sm btn-outline-info ms-2" 
                                            onclick="previewAttachment('#(attachment.url)', '#(attachment.fileName)')">
                                        <i class="fa fa-eye"></i> 预览
                                    </button>
                                    #else
                                    <button type="button" class="btn btn-sm btn-outline-info ms-2" 
                                            onclick="previewAttachment('#(attachment.url)', '#(attachment.fileName)')">
                                        <i class="fa fa-download"></i> 下载
                                    </button>
                                    #end
                                </div>
                                <span class="badge bg-secondary">#(attachment.size)</span>
                            </div>
                            #end
                        </div>
                    </div>
                    #end
                </div>
            </div>

            <!-- 浮动按钮组 -->
            <div id="floatingButtonGroup" class="floating-button-group">
                <div class="floating-button-header" id="floatingButtonHeader">
                    <i class="fa fa-arrows drag-handle" title="拖拽移动"></i>
                    <span class="floating-title">邮件操作</span>
                    <button type="button" class="btn-minimize" id="minimizeFloatingButtons" title="最小化">
                        <i class="fa fa-minus"></i>
                    </button>
                </div>
                <div class="floating-button-content" id="floatingButtonContent">
                    <button type="submit" id="sendBtn" class="btn btn-primary btn-sm mb-2"
                            title="发送邮件 (Ctrl+Enter)">
                        <i class="fa fa-paper-plane"></i> 发送
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm mb-2" onclick="saveDraft()"
                            title="保存草稿 (Ctrl+S)">
                        <i class="fa fa-save"></i> 保存草稿
                    </button>
                    <div class="form-check mt-2">
                        <input title="启用后，页面切换时会自动保存草稿" class="form-check-input" type="checkbox" id="autoSaveSwitch" onchange="toggleAutoSave()">
                        <label class="form-check-label" for="autoSaveSwitch">
                            <small>自动保存草稿</small>
                        </label>
                    </div>
                </div>
            </div>

            <!-- 原始按钮组（作为备用，在移动端显示） -->
            <div class="form-group row mt-4 d-md-none">
                <div class="col-sm-12 text-center">
                    <button type="submit" class="btn btn-primary" title="发送邮件 (Ctrl+Enter)">
                        <i class="fa fa-paper-plane"></i> 发送
                    </button>
                    <button type="button" class="btn btn-outline-secondary ms-2" onclick="saveDraft()"
                            title="保存草稿 (Ctrl+S)">
                        <i class="fa fa-save"></i> 保存草稿
                    </button>
                    <div class="form-check form-check-inline ms-3">
                        <input title="启用后，页面切换时会自动保存草稿" class="form-check-input" type="checkbox" onchange="toggleAutoSave()">
                        <label class="form-check-label" title="启用后，页面切换时会自动保存草稿">
                            <small>自动保存草稿</small>
                        </label>
                    </div>
                </div>
            </div>

            #if(originalEmail??)
            <!-- 原始邮件内容 -->
            <div title="原始邮件内容" id="originalEmailContent" class="card" style="display: none;">
                <div class="card-header">
                    原始邮件
                </div>
                <div class="card-body">
                    #(originalEmail.content??)
                </div>
            </div>
            #end
        </form>
    </div>
</div>

<!-- 签名选择模态框 -->
<div class="modal fade" id="signatureModal" tabindex="-1" aria-labelledby="signatureModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="signatureModalLabel">选择邮件签名</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="list-group" id="signatureList"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="insertSignature()">确定</button>
            </div>
        </div>
    </div>
</div>

<!-- 模板选择模态框 -->
<div class="modal fade" id="templateModal" tabindex="-1" aria-labelledby="templateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="templateModalLabel">选择邮件模板</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="list-group" id="templateList"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="applyTemplate()">确定</button>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="previewContent"></div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- AI 功能对话框 -->
<div class="modal fade" id="aiDialog" tabindex="-1" aria-labelledby="aiDialogLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="aiDialogLabel">AI 助手</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- LLM 提供商选择 -->
                <div class="form-group mb-3">
                    <label>AI提供商:</label>
                    <select class="form-control" id="llmProvider" onchange="loadLlmModels()">
                        <option value="">请选择AI提供商</option>
                        <!-- 动态加载选项 -->
                    </select>
                </div>

                <!-- LLM 模型选择 -->
                <div class="form-group mb-3">
                    <label>AI模型:</label>
                    <select class="form-control" id="llmModel">
                        <option value="">请先选择AI提供商</option>
                        <!-- 动态加载选项 -->
                    </select>
                </div>

                <!-- AI 功能选项 -->
                <div class="form-group mb-3">
                    <label>功能选项:</label>
                    <select class="form-control" id="aiOptions">
                        <!-- 动态加载选项 -->
                    </select>
                </div>

                <div class="form-group">
                    <label for="aiPrompt">自定义提示语（可选）：</label>
                    <textarea class="form-control" id="aiPrompt" rows="3"
                              placeholder="可以输入更具体的要求，如语气、风格等"></textarea>
                </div>
                <!-- 进度条 -->
                <div title="AI处理进度" id="aiProgress" class="progress mt-3" style="display: none;">
                    <div title="AI处理进度" class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 100%">
                        正在处理中...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="processAiRequest()">确定</button>
            </div>
        </div>
    </div>
</div>

<!-- AI 结果预览对话框 -->
<div class="modal fade" id="aiResultDialog" tabindex="-1" aria-labelledby="aiResultDialogLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="aiResultDialogLabel">AI 处理结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div title="AI处理结果" id="aiResultContent" style="min-height: 200px; max-height: 400px; overflow-y: auto;"></div>
            </div>
            <div class="modal-footer">
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="replaceEditorContent()">
                        <i class="fa fa-exchange-alt"></i> 替换原文
                    </button>
                    <button type="button" class="btn btn-info" onclick="insertAtCursor()">
                        <i class="fa fa-arrow-right"></i> 插入到光标位置
                    </button>
                    <button type="button" class="btn btn-success" onclick="appendToEditor()">
                        <i class="fa fa-arrow-down"></i> 追加到末尾
                    </button>
                </div>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 翻译内容模态框 -->
<div class="modal fade" id="translationModal" tabindex="-1" aria-labelledby="translationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="translationModalLabel">邮件翻译内容</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="translation-tabs mb-3">
                    <ul class="nav nav-tabs" id="translationTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="translation-text-tab" data-bs-toggle="tab" 
                                    data-bs-target="#translation-text" type="button" role="tab" 
                                    aria-controls="translation-text" aria-selected="true">翻译文本</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="translation-screenshot-tab" data-bs-toggle="tab" 
                                    data-bs-target="#translation-screenshot" type="button" role="tab" 
                                    aria-controls="translation-screenshot" aria-selected="false">翻译截图</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="translationTabContent">
                        <div class="tab-pane fade show active" id="translation-text" role="tabpanel" 
                             aria-labelledby="translation-text-tab">
                            <div id="translationContent" class="p-3 border rounded bg-light">
                                <!-- 翻译内容将在这里动态加载 -->
                                <div class="text-center py-3">
                                    <div class="spinner-border spinner-border-sm text-primary me-2"></div>
                                    <span>正在加载翻译内容...</span>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="translation-screenshot" role="tabpanel" 
                             aria-labelledby="translation-screenshot-tab">
                            <div id="translationScreenshot" class="text-center">
                                <!-- 翻译截图将在这里动态加载 -->
                                <div class="text-center py-3">
                                    <div class="spinner-border spinner-border-sm text-primary me-2"></div>
                                    <span>正在加载翻译截图...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="insertTranslationContent()">
                    <i class="fa fa-plus"></i> 插入翻译内容
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
#end

#define css()
<link rel="stylesheet" href="/assets/css/tinymce-table-enhancement.css">
<style>
    .custom-file-label::after {
        content: "浏览";
    }

    /* 确保模态框显示在最顶层 */
    .modal {
        z-index: 1050 !important;
    }

    .modal-backdrop {
        z-index: 1040 !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
    }

    /* 确保模态框内容在遮罩层之上 */
    .modal-dialog {
        z-index: 1050 !important;
    }

    /* 防止多个backdrop叠加 */
    body.modal-open .modal-backdrop:not(:first-of-type) {
        display: none !important;
    }

    /* 调整模态框背景色和透明度 */
    .modal-content {
        background-color: #fff;
        box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
    }

    /* 调整模态框位置 */
    .modal-dialog {
        margin: 1.75rem auto;
        max-height: calc(100vh - 3.5rem);
    }

    /* 确保模态框垂直居中 */
    .modal.fade .modal-dialog {
        transform: translate(0, 0);
    }

    /* 修复模态框顶部间距 */
    .modal {
        padding-top: 50px;
    }

    /* 使模态框可拖动 */
    .modal-header {
        cursor: move;
    }

    /* 拖动时的样式 */
    .modal-dragging {
        -webkit-user-select: none;
        user-select: none;
    }

    /* 紧凑显示原始邮件内容 */
    #originalEmailContent .card-body {
        padding: 0.75rem;
        line-height: 1.3;
    }

    /* 处理邮件内容中的间距 */
    #originalEmailContent p,
    #originalEmailContent div,
    #originalEmailContent span {
        margin-top: 0.1em !important;
        margin-bottom: 0.1em !important;
        line-height: 1.3 !important;
    }

    /* 移除空元素 */
    #originalEmailContent p:empty,
    #originalEmailContent div:empty,
    #originalEmailContent span:empty {
        display: none !important;
    }

    /* 处理特定邮件客户端格式 */
    #originalEmailContent .WordSection1,
    #originalEmailContent .MsoNormal,
    #originalEmailContent div[class^="Word"] {
        margin: 0 !important;
        padding: 0 !important;
        line-height: 1.3 !important;
    }

    /* 移除连续的<br>标签 */
    #originalEmailContent br + br {
        display: none;
    }

    /* 邮件引用样式 */
    .quoted-content {
        margin: 5px 0 !important;
        padding: 5px 10px !important;
        border-left: 3px solid #ccc;
        background-color: #f8f9fa;
        color: #666;
        font-size: 0.95em;
        line-height: 1.3 !important;
    }

    .quote-header {
        color: #888;
        margin-bottom: 3px !important;
        font-size: 0.9em;
        line-height: 1.2 !important;
    }

    /* TinyMCE 编辑器内容紧凑显示 */
    .tox-tinymce {
        line-height: 1.4 !important;
    }

    .tox-edit-area__iframe {
        line-height: 1.4 !important;
    }

    .email-form {
        margin-bottom: 20px;
    }
    
    .email-form .form-group {
        margin-bottom: 15px;
    }
    
    .tox-tinymce {
        min-height: 400px !important;
    }
    
    /* 隐藏原始邮件内容 */
    #originalEmailContent {
        display: none;
        margin-top: 15px;
    }
    
    /* 引用样式 */
    .quote-header {
        color: #666;
        margin: 10px 0 5px;
    }
    
    .quoted-content {
        border-left: 3px solid #ccc;
        padding-left: 10px;
        color: #666;
    }
    
    /* 自动完成样式 */
    .ac_results {
        padding: 0;
        border: 1px solid #ccc;
        background-color: white;
        overflow: hidden;
        z-index: 99999;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        max-height: 350px;
        overflow-y: auto;
        width: 350px !important;
        position: absolute;
        display: block !important;
    }
    
    .ac_results ul {
        width: 100%;
        list-style-position: outside;
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .ac_results li {
        margin: 0;
        padding: 8px 10px;
        cursor: pointer;
        display: block;
        font-size: 14px;
        line-height: 18px;
        overflow: hidden;
        border-bottom: 1px solid #f0f0f0;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    
    .ac_results li:last-child {
        border-bottom: none;
    }
    
    .ac_loading {
        background: white url('/assets/plugins/bootstrap-fileinput/img/loading.gif') right center no-repeat;
    }
    
    .ac_odd {
        background-color: #f8f9fa;
    }
    
    .ac_over {
        background-color: #007bff;
        color: white;
    }
    
    /* 原始邮件弹窗样式 */
    .original-email-content {
        padding: 15px;
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        max-height: 80vh;
        overflow-y: auto;
    }
    
    /* 原始邮件内容样式 */
    .original-email-content p, 
    .original-email-content div, 
    .original-email-content span {
        margin: 0 !important;
        padding: 0 !important;
        line-height: 1.4 !important;
    }
    
    /* 允许段落之间适当间距 */
    .original-email-content p + p {
        margin-top: 0.3em !important;
    }
    
    /* 改进图片显示 */
    .original-email-content img {
        max-width: 100% !important;
        height: auto !important;
        display: inline-block !important;
        margin: 5px 0 !important;
        cursor: pointer; /* 添加指针样式，提示可以点击 */
        border: 1px solid #eee; /* 添加边框 */
        padding: 2px; /* 添加内边距 */
    }
    
    /* 调整引用内容样式 */
    .original-email-content blockquote {
        margin: 10px 0 !important;
        padding: 10px 0 10px 15px !important;
        border-left: 4px solid #007bff !important;
        background-color: #f8f9fa !important;
        color: #333 !important;
        font-style: normal !important;
    }
    
    /* 修复表格样式 */
    .original-email-content table {
        width: 100% !important;
        max-width: 100% !important;
        margin: 10px 0 !important;
        border-collapse: collapse !important;
    }
    
    .original-email-content th, 
    .original-email-content td {
        padding: 5px !important;
        border: 1px solid #ddd !important;
    }
    
    /* 美化链接 */
    .original-email-content a {
        color: #007bff !important;
        text-decoration: none !important;
    }
    
    .original-email-content a:hover {
        text-decoration: underline !important;
    }
    
    /* 美化Layer弹窗 */
    .layui-layer-title {
        background-color: #f8f9fa !important;
        color: #333 !important;
        font-weight: bold !important;
        border-bottom: 1px solid #eee !important;
    }
    
    .layui-layer {
        border-radius: 5px !important;
        overflow: hidden !important;
    }
    
    .layui-layer-btn {
        background-color: #f8f9fa !important;
        border-top: 1px solid #eee !important;
    }
    
    /* 自定义自动完成样式 */
    .ac_custom_results {
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        max-height: 300px;
        overflow-y: auto;
        z-index: 9999;
    }
    
    .ac_custom_results ul {
        list-style: none;
        margin: 0;
        padding: 0;
    }
    
    .ac_custom_results li {
        padding: 8px 10px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .ac_custom_results li:last-child {
        border-bottom: none;
    }
    
    .ac_custom_results li.selected,
    .ac_custom_results li:hover {
        background-color: #007bff;
        color: #fff;
    }

    /* 附件预览样式 */
    .attachment-preview-item {
        width: 100px;
        height: 100px;
        margin: 5px;
        position: relative;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        cursor: pointer;
    }
    
    .attachment-preview-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .attachment-preview-item .attachment-name {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        padding: 2px 4px;
        font-size: 10px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }
    
    .attachment-preview-item .attachment-remove {
        position: absolute;
        top: 2px;
        right: 2px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 50%;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        cursor: pointer;
        color: #dc3545;
    }
    
    .attachment-preview-item .attachment-remove:hover {
        background: rgba(255, 255, 255, 0.9);
        color: #bd2130;
    }
    
    /* 附件类型图标 */
    .attachment-preview-item .attachment-icon {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        color: #6c757d;
    }
    
    .attachment-preview-item .attachment-icon.pdf {
        color: #dc3545;
    }
    
    .attachment-preview-item .attachment-icon.doc {
        color: #0d6efd;
    }
    
    .attachment-preview-item .attachment-icon.xls {
        color: #198754;
    }
    
    .attachment-preview-item .attachment-icon.zip {
        color: #fd7e14;
    }
    
    /* 图片预览模态框样式 */
    .image-preview-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        z-index: 10000;
        overflow: hidden;
    }
    
    .image-preview-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 90%;
        max-height: 90%;
    }
    
    .image-preview-content img {
        max-width: 100%;
        max-height: 90vh;
        object-fit: contain;
        border: 2px solid white;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    }
    
    .image-preview-close {
        position: absolute;
        top: 20px;
        right: 30px;
        color: white;
        font-size: 30px;
        cursor: pointer;
    }
    
    .image-preview-title {
        position: absolute;
        bottom: 20px;
        left: 0;
        right: 0;
        text-align: center;
        color: white;
        font-size: 16px;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 10px;
    }
    
    /* 翻译内容样式 */
    .translation-content {
        max-height: 500px;
        overflow-y: auto;
        padding: 10px;
    }
    
    .translation-content h3 {
        font-size: 16px;
        font-weight: bold;
        margin-top: 15px;
        margin-bottom: 10px;
        color: #333;
    }
    
    .translation-content img {
        max-width: 100%;
        height: auto;
        margin: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    .translation-content div[style*="background-color: #f9f9f9"] {
        margin-bottom: 15px;
    }
    
    .translation-content div[style*="background-color: #f0f8ff"] {
        margin-bottom: 15px;
    }
    
    /* 翻译内容格式化样式 */
    #translationContent {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        font-size: 14px;
        line-height: 1.6;
        color: #333;
    }
    
    #translationContent p {
        margin-bottom: 10px;
        margin-top: 0;
    }
    
    #translationContent blockquote {
        margin: 10px 0;
        padding: 5px 10px;
        border-left: 3px solid #ccc;
        background-color: #f9f9f9;
        font-style: italic;
    }
    
    #translationContent ol, #translationContent ul {
        margin: 10px 0;
        padding-left: 20px;
    }
    
    #translationContent li {
        margin-bottom: 5px;
    }
    
    /* 保持空格和换行的显示 */
    #translationContent {
        white-space: normal;
        word-wrap: break-word;
        word-break: break-word;
    }
    
    /* 翻译内容插入到编辑器时的样式 */
    .tox-edit-area__iframe .translation-inserted {
        margin: 10px 0;
        padding: 10px;
        border-left: 3px solid #007bff;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    
    .tox-edit-area__iframe .translation-inserted p {
        margin-bottom: 8px;
    }
    
    .tox-edit-area__iframe .translation-inserted blockquote {
        margin: 8px 0;
        padding: 4px 8px;
        border-left: 2px solid #ccc;
        background-color: #f0f0f0;
        font-style: italic;
    }
    
    /* 优化移动端滚动体验 */
    html, body {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior-y: contain;
    }
    
    /* 移动端滚动优化 */
    @media (max-width: 768px) {
        html, body {
            /* 改善触摸滚动 */
            -webkit-overflow-scrolling: touch;
            /* 只防止垂直过度滚动，保持正常滚动 */
            overscroll-behavior-y: contain;
            /* 允许正常的平移操作 */
            touch-action: manipulation;
        }
        
        /* 页面容器滚动优化 */
        .jbolt_page {
            -webkit-overflow-scrolling: touch;
            overscroll-behavior-y: contain;
        }
        
        /* TinyMCE编辑器在移动端的滚动优化 */
        .tox-tinymce {
            -webkit-overflow-scrolling: touch;
        }
        
        .tox-edit-area__iframe {
            -webkit-overflow-scrolling: touch;
        }
        
        /* 模态框内容的滚动优化 */
        .modal-body {
            -webkit-overflow-scrolling: touch;
            touch-action: pan-y;
        }
        
        /* 表单元素触摸优化 */
        input, textarea, select {
            touch-action: manipulation;
        }
        
        /* 按钮和链接触摸优化 */
        button, a, .btn {
            touch-action: manipulation;
        }
        
        /* 拖拽区域在移动端的触摸优化 */
        .drag-drop-zone {
            touch-action: manipulation;
        }
        
        /* 附件预览区域的触摸优化 */
        .attachment-preview-item {
            touch-action: manipulation;
        }
        
        /* 自动完成下拉框的滚动优化 */
        .ac_results, .ac_custom_results {
            -webkit-overflow-scrolling: touch;
            touch-action: pan-y;
        }
        
        /* 原始邮件内容区域的滚动优化 */
        .original-email-content {
            -webkit-overflow-scrolling: touch;
            touch-action: pan-y;
        }
        
        /* 翻译内容区域的滚动优化 */
        .translation-content {
            -webkit-overflow-scrolling: touch;
            touch-action: pan-y;
        }
        
        /* 页面主要内容区域的滚动优化 */
        .jbolt_page_content {
            -webkit-overflow-scrolling: touch;
        }
        
        /* 表单行的触摸优化 */
        .form-group {
            touch-action: manipulation;
        }
        
        /* 工具栏按钮区域的触摸优化 */
        .btn-group {
            touch-action: manipulation;
        }
        
        /* 禁用某些元素的选择，改善触摸体验 */
        .drag-drop-content, .drag-drop-icon, .drag-drop-text {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
    }
    
    /* 页面内容修改状态指示器 */
    .content-modified-indicator {
        position: fixed;
        top: 10px;
        right: 10px;
        background-color: #ffc107;
        color: #212529;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        z-index: 9999;
        display: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    .content-modified-indicator.show {
        display: block;
        animation: fadeInBounce 0.3s ease-out;
    }
    
    @keyframes fadeInBounce {
        0% {
            opacity: 0;
            transform: translateY(-10px) scale(0.8);
        }
        50% {
            transform: translateY(5px) scale(1.1);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }
    
    /* 自动保存开关样式 */
    .form-check-inline {
        display: inline-flex;
        align-items: center;
        margin-left: 0.75rem;
    }
    
    .form-check-inline .form-check-input {
        margin-right: 0.375rem;
    }
    
    .form-check-inline .form-check-label {
        margin-bottom: 0;
        cursor: pointer;
    }
    
    .form-check-inline .form-check-label small {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    /* TinyMCE编辑器优化样式 */
    .tox-tinymce {
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
    }
    
    .tox-toolbar-overlord {
        background: #f8f9fa !important;
        border-bottom: 1px solid #dee2e6 !important;
    }
    
    .tox-toolbar__group {
        border-right: 1px solid #dee2e6 !important;
    }
    
    .tox-tbtn {
        margin: 2px !important;
    }
    
    .tox-tbtn:hover {
        background: #e9ecef !important;
        border-radius: 3px !important;
    }
    
    .tox-tbtn--enabled {
        background: #007bff !important;
        color: white !important;
        border-radius: 3px !important;
    }
    
    /* 字体和字号下拉框样式优化 */
    .tox-listbox {
        min-width: 120px !important;
    }
    
    .tox-listbox--select {
        background: white !important;
        border: 1px solid #ced4da !important;
        border-radius: 0.25rem !important;
    }
    
    .tox-listbox__select-label {
        font-size: 13px !important;
        padding: 4px 8px !important;
    }
    
    /* 确保工具栏按钮文字显示 */
    .tox-tbtn__select-label {
        font-size: 12px !important;
        max-width: none !important;
        overflow: visible !important;
    }
    
    /* 工具栏换行优化 */
    .tox-toolbar__overflow {
        background: #f8f9fa !important;
        border-top: 1px solid #dee2e6 !important;
    }
    
    /* 编辑器内容区域样式 */
    .tox-edit-area__iframe {
        background: white !important;
    }
    
    /* 状态栏样式 */
    .tox-statusbar {
        background: #f8f9fa !important;
        border-top: 1px solid #dee2e6 !important;
        font-size: 12px !important;
    }
    
    /* 工具栏切换按钮特殊样式 */
    .tox-tbtn[aria-label*="更多"], 
    .tox-tbtn[aria-label*="更少"] {
        background: #28a745 !important;
        color: white !important;
        border-radius: 4px !important;
        font-weight: bold !important;
    }
    
    .tox-tbtn[aria-label*="更多"]:hover, 
    .tox-tbtn[aria-label*="更少"]:hover {
        background: #218838 !important;
    }
    
    /* 拖拽上传区域样式 */
    .drag-drop-zone {
        border: 2px dashed #ced4da;
        border-radius: 8px;
        padding: 40px 20px;
        text-align: center;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        min-height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .drag-drop-zone:hover {
        border-color: #007bff;
        background-color: #e7f3ff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,123,255,0.15);
    }
    
    .drag-drop-zone.dragover {
        border-color: #28a745;
        background-color: #d4edda;
        border-style: solid;
    }
    
    .drag-drop-zone.dragover .drag-drop-icon {
        color: #28a745;
        animation: bounce 0.6s infinite alternate;
    }
    
    .drag-drop-content {
        pointer-events: none;
        width: 100%;
    }
    
    .drag-drop-content * {
        pointer-events: none !important;
    }
    
    .drag-drop-icon {
        font-size: 48px;
        color: #6c757d;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }
    
    .drag-drop-zone:hover .drag-drop-icon {
        color: #007bff;
        transform: scale(1.1);
    }
    
    .drag-drop-text {
        font-size: 18px;
        font-weight: 500;
        color: #495057;
        margin-bottom: 5px;
    }
    
    .drag-drop-subtext {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 10px;
    }
    
    .drag-drop-subtext a {
        text-decoration: none;
        font-weight: 500;
    }
    
    .drag-drop-subtext a:hover {
        text-decoration: underline;
    }
    
    /* 拖拽动画 */
    @keyframes bounce {
        0% { transform: translateY(0); }
        100% { transform: translateY(-10px); }
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .drag-drop-zone {
            padding: 30px 15px;
            min-height: 100px;
        }
        
        .drag-drop-icon {
            font-size: 36px;
            margin-bottom: 10px;
        }
        
        .drag-drop-text {
            font-size: 16px;
        }
        
        .drag-drop-subtext {
            font-size: 13px;
        }
    }
    
    /* iOS Safari 特定优化 */
    @supports (-webkit-appearance: none) {
        @media (max-width: 768px) {
            /* iOS 上的滚动弹性效果优化 */
            body {
                -webkit-overflow-scrolling: touch;
                overscroll-behavior: contain;
            }
            
            /* 防止 iOS Safari 地址栏滚动问题 */
            .jbolt_page {
                min-height: 100vh;
                min-height: -webkit-fill-available;
            }
            
            /* iOS 键盘弹出时的滚动优化 */
            input:focus, textarea:focus {
                transform: translateZ(0);
                -webkit-transform: translateZ(0);
            }
        }
    }
    
    /* Android Chrome 特定优化 */
    @media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 1) {
        /* Android 上的触摸反馈优化 */
        button, a, .btn, input, select, textarea {
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
            tap-highlight-color: rgba(0, 0, 0, 0.1);
        }
        
        /* Android 滚动条优化 */
        ::-webkit-scrollbar {
            width: 3px;
            background-color: transparent;
        }
        
        ::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 3px;
        }
    }

    /* 附件预览项样式 */
    .attachment-preview-item {
        position: relative;
        width: 120px;
        height: 120px;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin: 5px;
        padding: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        transition: all 0.3s ease;
    }

    .attachment-preview-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .attachment-preview-item.uploading {
        border-color: #007bff;
        background-color: #f8f9ff;
    }

    .attachment-preview-item.uploaded {
        border-color: #28a745;
        background-color: #f8fff8;
    }

    .attachment-preview-item.failed {
        border-color: #dc3545;
        background-color: #fff8f8;
    }

    .attachment-image {
        max-width: 80px;
        max-height: 60px;
        object-fit: cover;
        border-radius: 4px;
    }

    .attachment-icon {
        font-size: 32px;
        margin-bottom: 8px;
    }

    .attachment-icon.file-pdf { color: #dc3545; }
    .attachment-icon.file-word { color: #2b579a; }
    .attachment-icon.file-excel { color: #217346; }
    .attachment-icon.file-powerpoint { color: #d24726; }
    .attachment-icon.file-text { color: #6c757d; }
    .attachment-icon.file-archive { color: #fd7e14; }
    .attachment-icon.file-default { color: #6c757d; }

    .attachment-name {
        font-size: 11px;
        text-align: center;
        word-break: break-all;
        line-height: 1.2;
        max-height: 24px;
        overflow: hidden;
        margin-top: auto;
    }

    .upload-status {
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 14px;
    }

    .upload-progress-container {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 4px;
        background-color: #e9ecef;
        border-radius: 0 0 8px 8px;
        overflow: hidden;
    }

    .upload-progress {
        height: 100%;
        background-color: #007bff;
        transition: width 0.3s ease;
    }

    .attachment-remove {
        position: absolute;
        top: -5px;
        right: -5px;
        width: 20px;
        height: 20px;
        background-color: #dc3545;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .attachment-preview-item:hover .attachment-remove {
        opacity: 1;
    }

    .attachment-remove:hover {
        background-color: #c82333;
        transform: scale(1.1);
    }

    /* 附件数量徽章样式 */
    #attachmentCount {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-block;
        min-width: 20px;
        text-align: center;
    }

    #attachmentCount.bg-warning {
        animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    /* 浮动按钮组样式 */
    .floating-button-group {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1050;
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        min-width: 180px;
        max-width: 220px;
        transition: all 0.3s ease;
        -webkit-user-select: none;
        user-select: none;
    }

    .floating-button-group.minimized .floating-button-content {
        display: none;
    }

    .floating-button-group.minimized {
        min-width: auto;
        width: auto;
    }

    .floating-button-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 8px 12px;
        border-radius: 8px 8px 0 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: move;
        font-size: 12px;
        font-weight: 600;
        color: #495057;
    }

    .floating-button-group.minimized .floating-button-header {
        border-radius: 8px;
        border-bottom: none;
    }

    .drag-handle {
        cursor: move;
        color: #6c757d;
        margin-right: 8px;
    }

    .floating-title {
        flex: 1;
        text-align: center;
        font-size: 11px;
    }

    .btn-minimize {
        background: none;
        border: none;
        color: #6c757d;
        padding: 2px 4px;
        font-size: 10px;
        cursor: pointer;
        border-radius: 3px;
        transition: all 0.2s ease;
    }

    .btn-minimize:hover {
        background: #e9ecef;
        color: #495057;
    }

    .floating-button-content {
        padding: 12px;
        display: flex;
        flex-direction: column;
    }

    .floating-button-content .btn {
        width: 100%;
        font-size: 12px;
        padding: 6px 12px;
        border-radius: 4px;
    }

    .floating-button-content .form-check {
        margin-top: 8px;
        margin-bottom: 0;
    }

    .floating-button-content .form-check-label {
        font-size: 10px;
        color: #6c757d;
        cursor: pointer;
    }

    .floating-button-content .form-check-input {
        margin-top: 2px;
    }

    /* 拖拽时的样式 */
    .floating-button-group.dragging {
        opacity: 0.8;
        transform: scale(1.02);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .floating-button-group {
            display: none; /* 在移动端隐藏浮动按钮，使用原始按钮组 */
        }
    }

    /* 确保浮动按钮在桌面端显示，移动端隐藏 */
    @media (min-width: 769px) {
        .floating-button-group {
            display: block;
        }

        /* 在桌面端隐藏原始按钮组 */
        .d-md-none {
            display: none !important;
        }
    }
</style>
#end

#define js()
<script src="/assets/libs/tinymce/tinymce.min.js"></script>
<script src="/assets/js/tinymce-table-enhancement.js"></script>
<script src="/assets/plugins/jquery/autocomplete/autocomplete.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
// 全局变量，用于跟踪页面内容是否已修改
let isContentModified = false;
let originalFormData = null;
let isFormSubmitting = false;
let isPageInitialized = false; // 标记页面是否已完成初始化
let autoSaveEnabled = false; // 新增：控制是否启用自动保存草稿功能

$(function() {
    // 初始化页面保护功能
    initPageProtection();
    
    // 初始化自动保存设置
    initAutoSaveSettings();
    
    // 初始化TinyMCE编辑器
    initEditor();
    
    // 清理可能存在的遮罩层
    removeBackdrop();
    
    // 确保jQuery和autocomplete插件已加载
    if (typeof $ === 'function' && typeof $.fn.autocomplete === 'function') {
        // 初始化自动完成功能
        initAutocomplete();
        console.log('自动完成功能已初始化');
    } else {
        console.error('jQuery或autocomplete插件未加载');
        // 延迟尝试初始化
        setTimeout(function() {
            if (typeof $ === 'function' && typeof $.fn.autocomplete === 'function') {
                initAutocomplete();
                console.log('自动完成功能已延迟初始化');
            } else {
                console.error('无法加载自动完成功能');
            }
        }, 1000);
    }
    
    // 表单提交处理
    $('#replyEmailForm').submit(function(e) {
        e.preventDefault();
        sendEmail();
    });
    
    // 检查是否有草稿内容需要加载 - 草稿功能已经通过隐藏域实现
    // var isLoadingDraft = ...;  // 移除有问题的模板语法
    // if (isLoadingDraft) {
    //     console.log('[Draft] 草稿编辑模式');
    // }
    
    // 如果有原始邮件，优化显示
    if ($('#originalEmailContent').length) {
        // 优化原始邮件显示
        optimizeEmailDisplay(document.querySelector('#originalEmailContent .card-body'));
        
        // 获取原始邮件ID
        var originalEmailId = $('input[name="originalEmailId"]').val();
        if (originalEmailId) {
            // 延迟一下确保TinyMCE已经初始化
            setTimeout(function() {
                // 获取原始邮件内容用于回复或转发
                var isForward = $('#isForward').val() === 'true';
                // 检查是否有草稿内容，如果有草稿则不加载原始邮件内容
                var draftContentElement = document.getElementById('draftContentHolder');
                if (!draftContentElement) {
                    loadOriginalEmailContent(originalEmailId, isForward);
                }
            }, 1000);
        }
    }
    
    // 初始化模态框
    initModals();
    
    // 监听ESC键，关闭所有模态框
    $(document).on('keydown', function(e) {
        if (e.keyCode === 27) { // ESC键
            closeAllModals();
        }
    });
    
    // 添加Ctrl+S快捷键保存草稿
    $(document).on('keydown', function(e) {
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 83) { // Ctrl+S 或 Cmd+S
            e.preventDefault();
            saveDraft();
            return false;
        }
    });

    // 添加Ctrl+Enter快捷键发送邮件
    $(document).on('keydown', function(e) {
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 13) { // Ctrl+Enter 或 Cmd+Enter
            e.preventDefault();
            sendEmail();
            return false;
        }
    });

    // 初始化拖拽上传功能
    initDragDropUpload();

    // 全局变量存储已上传的附件
    window.uploadedAttachments = [];
    window.uploadingCount = 0;

    // 初始化发送按钮状态
    updateSendButtonState();

    // 初始化附件数量显示
    updateAttachmentCount();

    // 初始化浮动按钮组
    initFloatingButtonGroup();

    // 附件即时上传功能
    $('#attachmentInput').on('change', function(e) {
        const files = e.target.files;
        if (files.length > 0) {
            $('#attachmentPreviewContainer').show();

            // 开始上传所有选中的文件
            for (let i = 0; i < files.length; i++) {
                uploadAttachmentFile(files[i]);
            }

            // 清空文件输入框，允许重复选择相同文件
            $(this).val('');
        }
    });

    // 上传单个附件文件
    function uploadAttachmentFile(file) {
        // 检查文件大小
        if (file.size > 30 * 1024 * 1024) {
            layer.msg(`文件 ${file.name} 大小超过30MB限制`, {icon: 2});
            return;
        }

        // 创建预览项
        const previewItem = createAttachmentPreviewItem(file);
        $('#attachmentPreview').append(previewItem);

        // 增加上传计数
        window.uploadingCount++;
        updateSendButtonState();
        updateAttachmentCount();

        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);

        // 开始上传
        $.ajax({
            url: '/admin/emailMessages/uploadAttachment',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                const xhr = new window.XMLHttpRequest();
                // 上传进度
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        previewItem.find('.upload-progress').css('width', percentComplete + '%');
                    }
                }, false);
                return xhr;
            },
            success: function(res) {
                if (res.state === 'ok') {
                    // 上传成功
                    const attachmentData = res.data;

                    // 检查是否已存在相同ID的附件，避免重复添加
                    const existingIndex = window.uploadedAttachments.findIndex(att => att.id === attachmentData.id);
                    if (existingIndex === -1) {
                        window.uploadedAttachments.push(attachmentData);
                        console.log('添加新附件:', attachmentData.id, attachmentData.fileName);
                    } else {
                        console.log('附件已存在，跳过添加:', attachmentData.id);
                    }

                    // 更新预览项状态
                    previewItem.removeClass('uploading').addClass('uploaded');
                    previewItem.find('.upload-progress-container').hide();
                    previewItem.find('.upload-status').html('<i class="fa fa-check text-success"></i>');
                    previewItem.attr('data-attachment-id', attachmentData.id);

                    // 更新隐藏字段
                    updateUploadedAttachmentIds();

                    layer.msg(`${file.name} 上传成功`, {icon: 1, time: 1000});
                } else {
                    // 上传失败
                    previewItem.removeClass('uploading').addClass('failed');
                    previewItem.find('.upload-progress-container').hide();
                    previewItem.find('.upload-status').html('<i class="fa fa-times text-danger"></i>');
                    layer.msg(`${file.name} 上传失败: ${res.msg}`, {icon: 2});
                }
            },
            error: function() {
                // 上传失败
                previewItem.removeClass('uploading').addClass('failed');
                previewItem.find('.upload-progress-container').hide();
                previewItem.find('.upload-status').html('<i class="fa fa-times text-danger"></i>');
                layer.msg(`${file.name} 上传失败`, {icon: 2});
            },
            complete: function() {
                // 减少上传计数
                window.uploadingCount--;
                updateSendButtonState();
                updateAttachmentCount();
                markContentAsModified();
            }
        });
    }
    
    // 创建附件预览项
    function createAttachmentPreviewItem(file) {
        const previewItem = $('<div class="attachment-preview-item uploading"></div>');

        // 检查是否为图片文件（包括HEIC格式）
        if (file.type.startsWith('image/') || file.name.toLowerCase().match(/\.(heic|heif)$/)) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewItem.find('.attachment-icon').replaceWith(`<img src="${e.target.result}" alt="${file.name}" class="attachment-image">`);
            };
            reader.readAsDataURL(file);
        } else {
            // 根据文件类型显示不同图标
            let iconClass = 'fa-file';
            let fileTypeClass = 'file-default';

            if (file.type.includes('pdf') || file.name.endsWith('.pdf')) {
                iconClass = 'fa-file-pdf';
                fileTypeClass = 'file-pdf';
            } else if (file.type.includes('word') || file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
                iconClass = 'fa-file-word';
                fileTypeClass = 'file-word';
            } else if (file.type.includes('excel') || file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) {
                iconClass = 'fa-file-excel';
                fileTypeClass = 'file-excel';
            } else if (file.type.includes('powerpoint') || file.name.endsWith('.ppt') || file.name.endsWith('.pptx')) {
                iconClass = 'fa-file-powerpoint';
                fileTypeClass = 'file-powerpoint';
            } else if (file.type.includes('text')) {
                iconClass = 'fa-file-text';
                fileTypeClass = 'file-text';
            } else if (file.type.includes('zip') || file.type.includes('rar') || file.type.includes('7z')) {
                iconClass = 'fa-file-archive';
                fileTypeClass = 'file-archive';
            }

            previewItem.append(`<div class="attachment-icon ${fileTypeClass}"><i class="fa ${iconClass}"></i></div>`);
        }

        // 添加文件名、上传状态和删除按钮
        previewItem.append(`<div class="attachment-name">${file.name}</div>`);
        previewItem.append(`<div class="upload-status"><i class="fa fa-spinner fa-spin"></i></div>`);
        previewItem.append(`
            <div class="upload-progress-container">
                <div class="upload-progress" style="width: 0%"></div>
            </div>
        `);
        previewItem.append(`<div class="attachment-remove"><i class="fa fa-times"></i></div>`);

        // 添加删除事件
        previewItem.find('.attachment-remove').on('click', function(e) {
            e.stopPropagation();
            removeAttachmentPreviewItem(previewItem);
        });

        return previewItem;
    }

    // 移除附件预览项
    function removeAttachmentPreviewItem(previewItem) {
        const attachmentId = previewItem.attr('data-attachment-id');

        // 从已上传附件列表中移除
        if (attachmentId) {
            const beforeCount = window.uploadedAttachments.length;
            window.uploadedAttachments = window.uploadedAttachments.filter(att => att.id !== attachmentId);
            const afterCount = window.uploadedAttachments.length;
            console.log(`移除附件 ${attachmentId}，附件数量从 ${beforeCount} 变为 ${afterCount}`);
            updateUploadedAttachmentIds();
        }

        // 移除预览项
        previewItem.remove();

        // 如果没有附件了，隐藏预览容器
        if ($('#attachmentPreview').children().length === 0) {
            $('#attachmentPreviewContainer').hide();
        }

        markContentAsModified();
    }

    // 更新已上传附件ID字段
    function updateUploadedAttachmentIds() {
        // 确保去重并过滤空值
        const uniqueIds = [...new Set(window.uploadedAttachments
            .map(att => att.id)
            .filter(id => id && id.trim() !== '')
        )];
        const ids = uniqueIds.join(',');
        $('#uploadedAttachmentIds').val(ids);
        console.log('更新附件ID列表:', ids);

        // 同时更新附件数量显示
        updateAttachmentCount();
    }

    // 更新附件数量显示
    function updateAttachmentCount() {
        const totalCount = window.uploadedAttachments.length;
        const uploadingCount = window.uploadingCount || 0;

        // 更新数量徽章
        const countBadge = $('#attachmentCount');
        if (totalCount > 0 || uploadingCount > 0) {
            let displayText = totalCount.toString();
            if (uploadingCount > 0) {
                displayText += ` (+${uploadingCount}上传中)`;
                countBadge.removeClass('bg-primary bg-success').addClass('bg-warning');
            } else {
                countBadge.removeClass('bg-warning bg-primary').addClass('bg-success');
            }
            countBadge.text(displayText);

            // 显示附件预览容器
            $('#attachmentPreviewContainer').show();
        } else {
            countBadge.text('0');
            countBadge.removeClass('bg-warning bg-success').addClass('bg-primary');

            // 如果没有附件，隐藏预览容器
            if ($('#attachmentPreview').children().length === 0) {
                $('#attachmentPreviewContainer').hide();
            }
        }

        console.log(`附件数量更新: 已上传=${totalCount}, 上传中=${uploadingCount}`);
    }

    // 更新发送按钮状态
    function updateSendButtonState() {
        const sendBtn = $('#sendBtn');
        if (window.uploadingCount > 0) {
            sendBtn.prop('disabled', true);
            sendBtn.html('<i class="fa fa-spinner fa-spin"></i> 附件上传中...');
        } else {
            sendBtn.prop('disabled', false);
            sendBtn.html('<i class="fa fa-paper-plane"></i> 发送');
        }
    }

    // 清空附件按钮
    $('#clearAttachments').on('click', function() {
        console.log('清空所有附件，当前附件数量:', window.uploadedAttachments.length);

        // 清空已上传附件列表
        window.uploadedAttachments = [];
        updateUploadedAttachmentIds();

        // 清空文件输入框
        $('#attachmentInput').val('');
        // 清空预览区域
        $('#attachmentPreview').empty();
        // 隐藏预览容器
        $('#attachmentPreviewContainer').hide();

        markContentAsModified();
    });

    // 添加调试函数，方便开发者检查附件状态
    window.debugAttachments = function() {
        console.log('=== 附件调试信息 ===');
        console.log('已上传附件数量:', window.uploadedAttachments.length);
        console.log('附件列表:', window.uploadedAttachments);
        console.log('隐藏字段值:', $('#uploadedAttachmentIds').val());
        console.log('预览项数量:', $('#attachmentPreview').children().length);
        console.log('==================');
    };

    // 初始化浮动按钮组
    function initFloatingButtonGroup() {
        const floatingGroup = $('#floatingButtonGroup');
        const header = $('#floatingButtonHeader');
        const content = $('#floatingButtonContent');
        const minimizeBtn = $('#minimizeFloatingButtons');

        if (floatingGroup.length === 0) {
            console.log('浮动按钮组未找到，跳过初始化');
            return;
        }

        // 从localStorage恢复位置和状态
        restoreFloatingButtonState();

        // 最小化/展开功能
        minimizeBtn.on('click', function() {
            const isMinimized = floatingGroup.hasClass('minimized');
            if (isMinimized) {
                floatingGroup.removeClass('minimized');
                $(this).find('i').removeClass('fa-plus').addClass('fa-minus');
                $(this).attr('title', '最小化');
            } else {
                floatingGroup.addClass('minimized');
                $(this).find('i').removeClass('fa-minus').addClass('fa-plus');
                $(this).attr('title', '展开');
            }

            // 保存状态
            localStorage.setItem('floatingButtonMinimized', !isMinimized);
        });

        // 拖拽功能
        let isDragging = false;
        let startX, startY, startLeft, startTop;

        header.on('mousedown', function(e) {
            if ($(e.target).closest('.btn-minimize').length > 0) {
                return; // 点击最小化按钮时不启动拖拽
            }

            isDragging = true;
            floatingGroup.addClass('dragging');

            startX = e.clientX;
            startY = e.clientY;

            const rect = floatingGroup[0].getBoundingClientRect();
            startLeft = rect.left;
            startTop = rect.top;

            e.preventDefault();
        });

        $(document).on('mousemove', function(e) {
            if (!isDragging) return;

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            let newLeft = startLeft + deltaX;
            let newTop = startTop + deltaY;

            // 边界检查
            const windowWidth = $(window).width();
            const windowHeight = $(window).height();
            const groupWidth = floatingGroup.outerWidth();
            const groupHeight = floatingGroup.outerHeight();

            newLeft = Math.max(10, Math.min(newLeft, windowWidth - groupWidth - 10));
            newTop = Math.max(10, Math.min(newTop, windowHeight - groupHeight - 10));

            floatingGroup.css({
                left: newLeft + 'px',
                top: newTop + 'px',
                right: 'auto'
            });
        });

        $(document).on('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                floatingGroup.removeClass('dragging');

                // 保存位置
                saveFloatingButtonPosition();
            }
        });

        // 窗口大小改变时调整位置
        $(window).on('resize', function() {
            adjustFloatingButtonPosition();
        });

        console.log('浮动按钮组初始化完成');
    }

    // 保存浮动按钮位置
    function saveFloatingButtonPosition() {
        const floatingGroup = $('#floatingButtonGroup');
        const rect = floatingGroup[0].getBoundingClientRect();

        const position = {
            left: rect.left,
            top: rect.top
        };

        localStorage.setItem('floatingButtonPosition', JSON.stringify(position));
    }

    // 恢复浮动按钮状态
    function restoreFloatingButtonState() {
        const floatingGroup = $('#floatingButtonGroup');
        const minimizeBtn = $('#minimizeFloatingButtons');

        // 恢复位置
        const savedPosition = localStorage.getItem('floatingButtonPosition');
        if (savedPosition) {
            try {
                const position = JSON.parse(savedPosition);
                floatingGroup.css({
                    left: position.left + 'px',
                    top: position.top + 'px',
                    right: 'auto'
                });

                // 调整位置确保在可视区域内
                setTimeout(() => adjustFloatingButtonPosition(), 100);
            } catch (e) {
                console.log('恢复浮动按钮位置失败:', e);
            }
        }

        // 恢复最小化状态
        const isMinimized = localStorage.getItem('floatingButtonMinimized') === 'true';
        if (isMinimized) {
            floatingGroup.addClass('minimized');
            minimizeBtn.find('i').removeClass('fa-minus').addClass('fa-plus');
            minimizeBtn.attr('title', '展开');
        }
    }

    // 调整浮动按钮位置（确保在可视区域内）
    function adjustFloatingButtonPosition() {
        const floatingGroup = $('#floatingButtonGroup');
        if (floatingGroup.length === 0) return;

        const windowWidth = $(window).width();
        const windowHeight = $(window).height();
        const groupWidth = floatingGroup.outerWidth();
        const groupHeight = floatingGroup.outerHeight();

        const rect = floatingGroup[0].getBoundingClientRect();
        let newLeft = rect.left;
        let newTop = rect.top;

        // 确保不超出边界
        if (newLeft + groupWidth > windowWidth - 10) {
            newLeft = windowWidth - groupWidth - 10;
        }
        if (newLeft < 10) {
            newLeft = 10;
        }
        if (newTop + groupHeight > windowHeight - 10) {
            newTop = windowHeight - groupHeight - 10;
        }
        if (newTop < 10) {
            newTop = 10;
        }

        // 如果位置有变化，更新位置
        if (Math.abs(newLeft - rect.left) > 1 || Math.abs(newTop - rect.top) > 1) {
            floatingGroup.css({
                left: newLeft + 'px',
                top: newTop + 'px',
                right: 'auto'
            });

            // 保存新位置
            setTimeout(() => saveFloatingButtonPosition(), 100);
        }
    }
});

// 初始化页面保护功能
function initPageProtection() {
    console.log('初始化页面保护功能');
    
    // 优化移动端滚动体验
    optimizeMobileScrolling();
    
    // 增强移动端滚动功能
    enhanceMobileScrolling();
    
    // 监听页面刷新/关闭
    setupBeforeUnloadProtection();
    
    // 监听表单内容变化
    setupContentChangeDetection();
    
    // 监听移动端特有的返回/前进
    setupHistoryProtection();
    
    // 保存初始表单数据，并标记页面初始化完成
    setTimeout(function() {
        saveOriginalFormData();
        // 标记页面初始化完成，开始真正的内容变化检测
        setTimeout(function() {
            isPageInitialized = true;
            console.log('页面初始化完成，开始监听内容变化');
        }, 500); // 再延迟500ms确保所有初始化完成
    }, 3000); // 延长等待时间，确保编辑器完全初始化
}

// 优化移动端滚动体验
function optimizeMobileScrolling() {
    let startY = 0;
    let startX = 0;
    let isAtTop = false;
    let isAtBottom = false;
    let isHorizontalScroll = false;
    
    // 检测是否在可滚动容器内
    function isInScrollableContainer(element) {
        while (element && element !== document.body) {
            const style = window.getComputedStyle(element);
            const overflow = style.overflow + style.overflowY + style.overflowX;
            if (overflow.includes('scroll') || overflow.includes('auto')) {
                return true;
            }
            element = element.parentElement;
        }
        return false;
    }
    
    // 获取元素的滚动状态
    function getScrollState(element) {
        if (!element) {
            return {
                atTop: window.scrollY === 0,
                atBottom: window.scrollY >= document.documentElement.scrollHeight - window.innerHeight - 1
            };
        }
        
        return {
            atTop: element.scrollTop === 0,
            atBottom: element.scrollTop >= element.scrollHeight - element.clientHeight - 1
        };
    }
    
    document.addEventListener('touchstart', function(e) {
        if (e.touches.length > 1) return; // 多点触控，允许缩放
        
        startY = e.touches[0].clientY;
        startX = e.touches[0].clientX;
        
        // 检查是否在页面顶部或底部
        const scrollState = getScrollState();
        isAtTop = scrollState.atTop;
        isAtBottom = scrollState.atBottom;
        isHorizontalScroll = false;
    }, { passive: true });
    
    document.addEventListener('touchmove', function(e) {
        if (e.touches.length > 1) return; // 多点触控，允许缩放
        
        const currentY = e.touches[0].clientY;
        const currentX = e.touches[0].clientX;
        const deltaY = currentY - startY;
        const deltaX = currentX - startX;
        
        // 判断是否为水平滚动
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            isHorizontalScroll = true;
            return; // 水平滚动，不干预
        }
        
        // 检查触摸目标是否在可滚动容器内
        const target = e.target;
        if (isInScrollableContainer(target)) {
            return; // 在可滚动容器内，允许正常滚动
        }
        
        // 检查是否是输入框或编辑器
        if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || 
            target.closest('.tox-edit-area__iframe') || target.closest('.tox-tinymce')) {
            return; // 编辑器区域，允许正常操作
        }
        
        // 只在特定条件下阻止下拉刷新
        const isPullDown = deltaY > 0; // 向下拉
        const isPullUp = deltaY < 0;   // 向上拉
        
        // 在页面顶部且向下拉，且移动距离较大时才阻止（防止下拉刷新）
        if (isAtTop && isPullDown && Math.abs(deltaY) > 10) {
            e.preventDefault();
            return;
        }
        
        // 在页面底部且向上拉，且移动距离较大时才阻止（防止过度滚动）
        if (isAtBottom && isPullUp && Math.abs(deltaY) > 10) {
            e.preventDefault();
            return;
        }
        
    }, { passive: false });
    
    // 防止意外的缩放操作，但允许正常双击缩放
    document.addEventListener('touchend', function(e) {
        // 重置状态
        isHorizontalScroll = false;
    }, { passive: true });
    
    // 防止iOS Safari的意外缩放
    document.addEventListener('gesturestart', function(e) {
        e.preventDefault();
    }, { passive: false });
    
    document.addEventListener('gesturechange', function(e) {
        e.preventDefault();
    }, { passive: false });
    
    document.addEventListener('gestureend', function(e) {
        e.preventDefault();
    }, { passive: false });
    
    console.log('已优化移动端滚动体验');
}

// 添加额外的移动端滚动辅助功能
function enhanceMobileScrolling() {
    // 检测是否为移动设备
    const isMobile = /iPhone|iPad|iPod|Android|webOS|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
                    window.innerWidth <= 768;
    
    if (!isMobile) {
        return;
    }
    
    // 优化TinyMCE编辑器的移动端体验
    function optimizeTinyMCE() {
        const editor = tinymce.get('emailContent');
        if (editor) {
            // 确保编辑器容器可以正常滚动
            const editorContainer = editor.getContainer();
            if (editorContainer) {
                editorContainer.style.webkitOverflowScrolling = 'touch';
                editorContainer.style.touchAction = 'manipulation';
            }
            
            // 优化编辑器内容区域
            const iframe = editorContainer.querySelector('iframe');
            if (iframe) {
                iframe.style.webkitOverflowScrolling = 'touch';
                iframe.style.touchAction = 'manipulation';
            }
        }
    }
    
    // 页面加载完成后优化编辑器
    setTimeout(optimizeTinyMCE, 2000);
    
    // 监听编辑器初始化事件
    document.addEventListener('tinymce-editor-init', optimizeTinyMCE);
    
    // 优化模态框的滚动
    document.addEventListener('shown.bs.modal', function(e) {
        const modal = e.target;
        const modalBody = modal.querySelector('.modal-body');
        if (modalBody) {
            modalBody.style.webkitOverflowScrolling = 'touch';
            modalBody.style.touchAction = 'pan-y';
        }
    });
    
    // 优化动态加载内容的滚动
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // 为新添加的可滚动元素添加触摸优化
                        const scrollableElements = node.querySelectorAll('[style*="overflow"], .modal-body, .ac_results, .ac_custom_results');
                        scrollableElements.forEach(function(element) {
                            element.style.webkitOverflowScrolling = 'touch';
                            if (element.classList.contains('modal-body')) {
                                element.style.touchAction = 'pan-y';
                            }
                        });
                    }
                });
            }
        });
    });
    
    // 开始观察文档变化
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    // 添加调试辅助函数
    window.debugScrolling = function() {
        console.log('页面滚动信息:');
        console.log('- 当前滚动位置:', window.scrollY);
        console.log('- 页面高度:', document.documentElement.scrollHeight);
        console.log('- 窗口高度:', window.innerHeight);
        console.log('- 是否在顶部:', window.scrollY === 0);
        console.log('- 是否在底部:', window.scrollY >= document.documentElement.scrollHeight - window.innerHeight - 1);
        
        const editor = tinymce.get('emailContent');
        if (editor) {
            const editorContainer = editor.getContainer();
            console.log('- 编辑器容器触摸样式:', window.getComputedStyle(editorContainer).webkitOverflowScrolling);
        }
    };
    
    console.log('已增强移动端滚动功能');
    console.log('可使用 window.debugScrolling() 查看滚动状态');
}

// 存储事件处理器引用，以便后续移除
let beforeUnloadHandler = null;
let visibilityChangeHandler = null;
let popstateHandler = null;

// 设置页面卸载前的保护
function setupBeforeUnloadProtection() {
    beforeUnloadHandler = function(e) {
        // 如果正在提交表单，不显示警告
        if (isFormSubmitting) {
            return;
        }

        // 检查内容是否已修改
        if (isContentModified || hasContentChanged()) {
            const message = '您的邮件内容尚未保存，确定要离开此页面吗？';
            e.preventDefault();
            e.returnValue = message;
            return message;
        }
    };

    window.addEventListener('beforeunload', beforeUnloadHandler);
    
    // 监听页面隐藏事件（更现代的方式）
    visibilityChangeHandler = function() {
        if (document.hidden && (isContentModified || hasContentChanged()) && !isFormSubmitting && autoSaveEnabled) {
            // 如果页面被隐藏且内容已修改，且启用了自动保存，则自动保存草稿
            autoSaveDraft();
        }
    };

    document.addEventListener('visibilitychange', visibilityChangeHandler);
    
    console.log('已设置页面卸载保护');
}

// 设置内容变化检测
function setupContentChangeDetection() {
    // 监听表单字段变化（只有在页面初始化完成后才标记为修改）
    $('#replyEmailForm').on('input change', 'input, select, textarea', function(e) {
        // 跳过自动加载的内容变化（如下拉框的自动选择）
        if (!isPageInitialized) {
            console.log('页面初始化中，跳过内容变化检测:', e.target.id || e.target.name);
            return;
        }
        markContentAsModified();
    });
    
    // 监听TinyMCE编辑器变化（在编辑器初始化后设置）
    setTimeout(function() {
        const editor = tinymce.get('emailContent');
        if (editor) {
            let editorInitialized = false;
            
            // 监听编辑器内容加载完成
            editor.on('LoadContent', function() {
                setTimeout(function() {
                    editorInitialized = true;
                    console.log('TinyMCE编辑器内容加载完成');
                }, 1000);
            });
            
            // 监听编辑器变化
            editor.on('input change', function() {
                if (!isPageInitialized || !editorInitialized) {
                    console.log('编辑器初始化中，跳过内容变化检测');
                    return;
                }
                markContentAsModified();
            });
            
            // 监听键盘输入（用户真正开始编辑）
            editor.on('keydown', function(e) {
                // 只有当用户按下可见字符键或删除键时才认为是真正的编辑
                if (!isPageInitialized || !editorInitialized) {
                    return;
                }
                
                // 检查是否是真正的编辑操作
                const isEditingKey = (e.keyCode >= 32 && e.keyCode <= 126) || // 可见字符
                                   e.keyCode === 8 || e.keyCode === 46 || // 退格和删除
                                   e.keyCode === 13; // 回车
                
                if (isEditingKey) {
                    markContentAsModified();
                }
            });
            
            // 监听粘贴操作
            editor.on('paste', function() {
                if (isPageInitialized && editorInitialized) {
                    markContentAsModified();
                }
            });
        }
    }, 4000); // 延长等待时间
    
    // 监听附件变化
    $('#attachmentInput').on('change', function() {
        if (!isPageInitialized) {
            return;
        }
        markContentAsModified();
    });
    
    console.log('已设置内容变化检测');
}

// 设置浏览器历史记录保护
function setupHistoryProtection() {
    // 添加历史记录状态
    if (window.history.pushState) {
        window.history.pushState({ page: 'replyEmail' }, '', window.location.href);
        
        popstateHandler = function(e) {
            if (isFormSubmitting) {
                return;
            }

            if (isContentModified || hasContentChanged()) {
                // 推回历史记录
                window.history.pushState({ page: 'replyEmail' }, '', window.location.href);

                // 显示确认对话框
                layer.confirm('您的邮件内容尚未保存，确定要离开此页面吗？', {
                    icon: 3,
                    title: '提示',
                    btn: ['保存并离开', '直接离开', '取消']
                }, function(index) {
                    // 保存并离开
                    layer.close(index);
                    saveDraftAndLeave();
                }, function(index) {
                    // 直接离开
                    layer.close(index);
                    isFormSubmitting = true;
                    window.history.back();
                }, function(index) {
                    // 取消，什么都不做
                    layer.close(index);
                });
            }
        };

        window.addEventListener('popstate', popstateHandler);
    }
    
    console.log('已设置浏览器历史记录保护');
}



// 标记内容已修改
function markContentAsModified() {
    // 只有在页面完全初始化后才允许标记内容修改
    if (!isPageInitialized) {
        console.log('页面初始化中，跳过内容修改标记');
        return;
    }
    
    if (!isContentModified) {
        isContentModified = true;
        $('#contentModifiedIndicator').addClass('show');
        console.log('内容已标记为修改状态');
    }
}

// 清除内容修改标记
function clearContentModified() {
    isContentModified = false;
    $('#contentModifiedIndicator').removeClass('show');
    console.log('内容修改标记已清除');
}

// 强制清除所有页面保护机制
function forceRemovePageProtection() {
    console.log('强制清除所有页面保护机制');

    // 清除内容修改标记
    isContentModified = false;
    isFormSubmitting = true;

    // 移除beforeunload事件监听器
    window.onbeforeunload = null;
    if (beforeUnloadHandler) {
        window.removeEventListener('beforeunload', beforeUnloadHandler);
        beforeUnloadHandler = null;
    }
    $(window).off('beforeunload');

    // 移除visibilitychange事件监听器
    if (visibilityChangeHandler) {
        document.removeEventListener('visibilitychange', visibilityChangeHandler);
        visibilityChangeHandler = null;
    }

    // 移除popstate事件监听器
    if (popstateHandler) {
        window.removeEventListener('popstate', popstateHandler);
        popstateHandler = null;
    }

    // 清除任何可能的定时器
    if (window.autoSaveTimer) {
        clearTimeout(window.autoSaveTimer);
    }

    // 强制设置全局变量
    window.isContentModified = false;
    window.isFormSubmitting = true;

    console.log('页面保护机制已完全清除');
}

// 检查内容是否已更改
function hasContentChanged() {
    if (!originalFormData) {
        return false;
    }
    
    try {
        const currentFormData = getCurrentFormData();
        
        // 比较关键字段
        const keyFields = ['fromEmail', 'toEmail', 'ccEmail', 'subject'];
        for (let field of keyFields) {
            if (currentFormData[field] !== originalFormData[field]) {
                return true;
            }
        }
        
        // 比较编辑器内容
        const editor = tinymce.get('emailContent');
        if (editor) {
            const currentContent = editor.getContent();
            if (currentContent !== originalFormData.emailContent) {
                return true;
            }
        }
        
        // 比较附件
        const currentFiles = $('#attachmentInput')[0].files;
        if (currentFiles.length !== originalFormData.attachmentCount) {
            return true;
        }
        
        return false;
    } catch (e) {
        console.error('检查内容变化时出错:', e);
        return false;
    }
}

// 保存初始表单数据
function saveOriginalFormData() {
    try {
        originalFormData = getCurrentFormData();
        console.log('已保存初始表单数据');
    } catch (e) {
        console.error('保存初始表单数据时出错:', e);
    }
}

// 获取当前表单数据
function getCurrentFormData() {
    const editor = tinymce.get('emailContent');
    
    return {
        fromEmail: $('#fromEmail').val() || '',
        toEmail: $('#toEmail').val() || '',
        ccEmail: $('#ccEmail').val() || '',
        subject: $('#subject').val() || '',
        emailContent: editor ? editor.getContent() : '',
        attachmentCount: $('#attachmentInput')[0].files.length
    };
}

// 切换自动保存功能
function toggleAutoSave() {
    autoSaveEnabled = $('#autoSaveSwitch').is(':checked');
    
    // 保存用户偏好到localStorage
    localStorage.setItem('emailAutoSaveEnabled', autoSaveEnabled);
    
    if (autoSaveEnabled) {
        layer.msg('已启用自动保存草稿功能', {icon: 1, time: 2000});
        console.log('自动保存草稿功能已启用');
    } else {
        layer.msg('已禁用自动保存草稿功能', {icon: 2, time: 2000});
        console.log('自动保存草稿功能已禁用');
    }
}

// 初始化自动保存设置
function initAutoSaveSettings() {
    // 从localStorage读取用户偏好，默认禁用
    const savedSetting = localStorage.getItem('emailAutoSaveEnabled');
    autoSaveEnabled = savedSetting === 'true'; // 默认false，只有明确设置为'true'才启用
    
    // 更新复选框状态
    $('#autoSaveSwitch').prop('checked', autoSaveEnabled);
    
    console.log('自动保存设置已初始化，当前状态:', autoSaveEnabled ? '启用' : '禁用');
}

// 自动保存草稿
function autoSaveDraft() {
    // 首先检查是否启用了自动保存
    if (!autoSaveEnabled) {
        console.log('自动保存功能已禁用，跳过自动保存');
        return;
    }
    
    console.log('尝试自动保存草稿');
    
    // 检查是否有必要保存
    if (!isContentModified && !hasContentChanged()) {
        console.log('内容未修改，跳过自动保存');
        return;
    }
    
    // 检查必要字段
    if (!$('#fromEmail').val() || !$('#toEmail').val() || !$('#subject').val()) {
        console.log('必要字段不完整，跳过自动保存');
        return;
    }
    
    // 防止频繁自动保存
    const currentTime = Date.now();
    if (window.lastAutoSaveTime && (currentTime - window.lastAutoSaveTime) < 30000) {
        console.log('距离上次自动保存时间太短，跳过本次保存');
        return;
    }
    
    // 执行保存草稿
    window.lastAutoSaveTime = currentTime;
    saveDraft(true); // 传入true表示是自动保存
}

// 保存草稿并离开页面
function saveDraftAndLeave() {
    console.log('保存草稿并离开页面');
    
    // 检查必要字段
    if (!$('#fromEmail').val() || !$('#toEmail').val() || !$('#subject').val()) {
        layer.msg('请填写完整的邮件信息后再保存', {icon: 2});
        return;
    }
    
    // 显示保存提示
    const loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });
    
    // 获取编辑器内容
    const editor = tinymce.get('emailContent');
    const content = editor ? editor.getContent() : '';
    
    // 准备表单数据
    const formData = new FormData($('#replyEmailForm')[0]);
    formData.append('content', content);
    
    // 发送保存草稿请求 - 注意：这里是保存草稿，不是发送邮件
    $.ajax({
        url: '/admin/emailMessages/saveDraft',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(res) {
            layer.close(loadingIndex);
            
            if (res.state === 'ok') {
                layer.msg('草稿已保存，正在离开页面...', {icon: 1, time: 1500});
                
                // 标记为正在提交，避免再次触发保护
                isFormSubmitting = true;
                clearContentModified();
                
                setTimeout(function() {
                    window.history.back();
                }, 1000);
            } else {
                layer.msg('保存草稿失败: ' + (res.msg || '未知错误'), {icon: 2});
            }
        },
        error: function() {
            layer.close(loadingIndex);
            layer.msg('保存草稿失败，网络错误', {icon: 2});
            isFormSubmitting = false;
        }
    });
}

// 初始化自动完成功能
function initAutocomplete() {
    console.log('开始初始化自动完成功能');
    
    // 直接使用jQuery AJAX获取数据，避免使用autocomplete插件的复杂处理
    function fetchEmailAddresses(term, callback) {
        // 如果搜索词为空，不执行搜索
        if (!term || term.trim().length === 0) {
            callback([]);
            return;
        }
        
        // 先显示加载中的提示
        var $input = $('input:focus');
        if ($input.length) {
            var pos = $input.offset();
            var width = $input.outerWidth();
            
            // 移除旧的结果
            $('.ac_custom_results').remove();
            
            // 显示加载中
            var $loading = $('<div class="ac_custom_results loading"></div>').css({
                position: 'absolute',
                top: pos.top + $input.outerHeight(),
                left: pos.left,
                width: width,
                padding: '10px',
                textAlign: 'center',
                backgroundColor: '#fff',
                border: '1px solid #ccc',
                borderRadius: '4px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                zIndex: 9999
            }).text('正在加载...');
            
            $('body').append($loading);
        }
        
        $.ajax({
            url: '/admin/emailMessages/getEmailAddresses',
            dataType: 'json',
            success: function(response) {
                // 移除加载提示
                $('.ac_custom_results.loading').remove();
                
                if (response && response.state === 'ok' && response.data) {
                    // 过滤结果
                    var filteredData = filterResults(response.data, term);
                    callback(filteredData);
                } else {
                    callback([]);
                }
            },
            error: function() {
                // 移除加载提示
                $('.ac_custom_results.loading').remove();
                console.error('获取邮箱地址失败');
                callback([]);
            }
        });
    }
    
    // 过滤结果函数
    function filterResults(data, term) {
        if (!term || !data || !data.length) return [];
        
        // 转换为小写以进行不区分大小写的比较
        term = term.toLowerCase();
        
        // 按匹配程度排序的结果
        var exactMatches = [];
        var partialMatches = [];
        var pinyinMatches = [];
        
        data.forEach(function(item) {
            // 检查精确匹配
            if ((item.value && item.value.toLowerCase() === term) || 
                (item.label && item.label.toLowerCase() === term)) {
                exactMatches.push(item);
                return;
            }
            
            // 检查部分匹配
            if ((item.value && item.value.toLowerCase().indexOf(term) !== -1) || 
                (item.label && item.label.toLowerCase().indexOf(term) !== -1)) {
                partialMatches.push(item);
                return;
            }
            
            // 检查拼音首字母匹配（简单实现）
            // 例如搜索"lh"可匹配"林红"
            if (item.label) {
                var nameParts = item.label.split(/[<\s\(\)]/); // 分割名称
                var name = nameParts[0]; // 取第一部分，通常是名称
                
                // 模拟拼音首字母匹配
                // 这里简单处理，实际可能需要更复杂的拼音匹配库
                if (hasPinyinInitialMatch(name, term)) {
                    pinyinMatches.push(item);
                    return;
                }
            }
        });
        
        // 合并并返回结果，优先级：精确匹配 > 部分匹配 > 拼音匹配
        return exactMatches.concat(partialMatches, pinyinMatches);
    }
    
    // 简单的拼音首字母匹配函数
    function hasPinyinInitialMatch(name, term) {
        // 简单检测常见的中文名字
        if (/[\u4e00-\u9fa5]/.test(name)) {
            // 一些常见的中文拼音首字母对应
            var pinyinMap = {
                '澳': 'a', '林': 'l', '壹': 'y', '琛': 'c', '欧': 'o', 
                '波': 'b', '比': 'b', '汉': 'h', '纳': 'n', '珍': 'z',
                '贝': 'b', '妮': 'n', '弗': 'f', '尔': 'e', '杰': 'j',
                '艾': 'a', '安': 'a', '爱': 'a', '奥': 'a', '巴': 'b',
                '鲍': 'b', '贝': 'b', '本': 'b', '毕': 'b', '博': 'b',
                '布': 'b', '蔡': 'c', '曹': 'c', '陈': 'c', '程': 'c',
                '迟': 'c', '戴': 'd', '邓': 'd', '狄': 'd', '刁': 'd',
                '丁': 'd', '董': 'd', '杜': 'd', '段': 'd', '范': 'f',
                '方': 'f', '冯': 'f', '符': 'f', '甘': 'g', '高': 'g',
                '葛': 'g', '龚': 'g', '古': 'g', '关': 'g', '郭': 'g',
                '韩': 'h', '何': 'h', '贺': 'h', '洪': 'h', '侯': 'h',
                '胡': 'h', '黄': 'h', '霍': 'h', '吉': 'j', '姬': 'j',
                '季': 'j', '简': 'j', '江': 'j', '姜': 'j', '蒋': 'j',
                '金': 'j', '靳': 'j', '康': 'k', '柯': 'k', '孔': 'k',
                '匡': 'k', '赖': 'l', '黎': 'l', '李': 'l', '梁': 'l',
                '廖': 'l', '林': 'l', '刘': 'l', '龙': 'l', '卢': 'l',
                '鲁': 'l', '陆': 'l', '路': 'l', '罗': 'l', '骆': 'l',
                '马': 'm', '梅': 'm', '孟': 'm', '莫': 'm', '牟': 'm',
                '倪': 'n', '聂': 'n', '宁': 'n', '欧': 'o', '潘': 'p',
                '庞': 'p', '裴': 'p', '彭': 'p', '蒲': 'p', '齐': 'q',
                '钱': 'q', '强': 'q', '秦': 'q', '邱': 'q', '裘': 'q',
                '仇': 'q', '权': 'q', '冉': 'r', '任': 'r', '荣': 'r',
                '阮': 'r', '瑞': 'r', '沈': 's', '盛': 's', '施': 's',
                '石': 's', '时': 's', '史': 's', '司': 's', '宋': 's',
                '苏': 's', '孙': 's', '谭': 't', '汤': 't', '唐': 't',
                '陶': 't', '田': 't', '童': 't', '涂': 't', '王': 'w',
                '韦': 'w', '卫': 'w', '魏': 'w', '温': 'w', '文': 'w',
                '翁': 'w', '巫': 'w', '吴': 'w', '伍': 'w', '武': 'w',
                '席': 'x', '夏': 'x', '萧': 'x', '肖': 'x', '项': 'x',
                '谢': 'x', '辛': 'x', '邢': 'x', '熊': 'x', '徐': 'x',
                '许': 'x', '薛': 'x', '严': 'y', '言': 'y', '颜': 'y',
                '杨': 'y', '姚': 'y', '叶': 'y', '伊': 'y', '易': 'y',
                '殷': 'y', '尹': 'y', '应': 'y', '雍': 'y', '尤': 'y',
                '游': 'y', '于': 'y', '余': 'y', '俞': 'y', '虞': 'y',
                '元': 'y', '袁': 'y', '岳': 'y', '云': 'y', '曾': 'z',
                '詹': 'z', '张': 'z', '章': 'z', '赵': 'z', '郑': 'z',
                '钟': 'z', '周': 'z', '朱': 'z', '祝': 'z', '庄': 'z',
                '卓': 'z', '宗': 'z', '邹': 'z', '祖': 'z'
            };
            
            // 构建名字的拼音首字母
            var initials = '';
            for (var i = 0; i < name.length; i++) {
                var char = name.charAt(i);
                if (pinyinMap[char]) {
                    initials += pinyinMap[char];
                }
            }
            
            // 检查拼音首字母是否匹配搜索词
            return initials.indexOf(term) !== -1;
        }
        
        return false;
    }
    
    // 自定义显示下拉列表
    function displayResults(input, data) {
        // 移除旧的结果
        $('.ac_custom_results').remove();
        
        if (!data || data.length === 0) return;
        
        // 创建结果容器
        var $results = $('<div class="ac_custom_results"></div>');
        var $list = $('<ul></ul>');
        
        // 计算位置
        var pos = $(input).offset();
        var width = $(input).outerWidth();
        
        $results.css({
            position: 'absolute',
            top: pos.top + $(input).outerHeight(),
            left: pos.left,
            width: width,
            zIndex: 9999,
            backgroundColor: '#fff',
            border: '1px solid #ccc',
            borderRadius: '4px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
            maxHeight: '300px',
            overflowY: 'auto'
        });
        
        // 添加结果项
        for (var i = 0; i < data.length; i++) {
            var item = data[i];
            var $item = $('<li></li>').css({
                padding: '8px 10px',
                cursor: 'pointer',
                borderBottom: '1px solid #f0f0f0',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
            });
            
            // 安全处理显示文本
            var displayText = item.label || item.value;
            displayText = $('<div>').text(displayText).html(); // 转义HTML
            
            $item.html(displayText);
            $item.data('value', item.value);
            
            // 鼠标悬停效果
            $item.hover(
                function() { $(this).css({ backgroundColor: '#007bff', color: '#fff' }); },
                function() { $(this).css({ backgroundColor: '', color: '' }); }
            );
            
            // 点击选择
            $item.on('click', function() {
                var value = $(this).data('value');
                var currentValue = $(input).val();
                
                // 处理多邮箱地址
                var lastComma = currentValue.lastIndexOf(',');
                var lastSemicolon = currentValue.lastIndexOf(';');
                var lastIndex = Math.max(lastComma, lastSemicolon);
                
                if (lastIndex >= 0) {
                    var prefix = currentValue.substring(0, lastIndex + 1);
                    if (prefix.length > 0 && prefix.charAt(prefix.length - 1) !== ' ') {
                        prefix += ' ';
                    }
                    $(input).val(prefix + value + ', ');
                } else {
                    $(input).val(value + ', ');
                }
                
                // 移除结果列表
                $('.ac_custom_results').remove();
                
                // 聚焦输入框
                $(input).focus();
            });
            
            $list.append($item);
        }
        
        $results.append($list);
        $('body').append($results);
        
        // 点击其他区域关闭下拉列表
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.ac_custom_results').length && !$(e.target).is(input)) {
                $('.ac_custom_results').remove();
            }
        });
    }
    
    // 处理输入框事件
    function setupEmailInput(inputId) {
        var $input = $('#' + inputId);
        var timer = null;
        
        $input.on('keyup', function(e) {
            // 如果是上下键、回车键，不触发搜索
            if (e.keyCode === 38 || e.keyCode === 40 || e.keyCode === 13) {
                return;
            }
            
            // 如果是逗号或分号，添加空格
            var value = $(this).val();
            if (value.endsWith(',') || value.endsWith(';')) {
                $(this).val(value + ' ');
                return;
            }
            
            // 清除之前的定时器
            clearTimeout(timer);
            
            // 设置新的定时器，延迟搜索
            timer = setTimeout(function() {
                var term = $input.val();
                
                // 如果有多个邮箱，只搜索最后一个
                var lastComma = term.lastIndexOf(',');
                var lastSemicolon = term.lastIndexOf(';');
                var lastIndex = Math.max(lastComma, lastSemicolon);
                
                if (lastIndex >= 0) {
                    term = term.substring(lastIndex + 1).trim();
                }
                
                // 如果搜索词太短，不搜索
                if (term.length < 2) {
                    $('.ac_custom_results').remove();
                    return;
                }
                
                // 获取邮箱地址
                fetchEmailAddresses(term, function(data) {
                    displayResults($input[0], data);
                });
            }, 300);
        });
        
        // 处理键盘导航
        $input.on('keydown', function(e) {
            var $results = $('.ac_custom_results');
            if (!$results.length) return;
            
            var $items = $results.find('li');
            var $selected = $results.find('li.selected');
            var index = $selected.length ? $items.index($selected) : -1;
            
            switch (e.keyCode) {
                case 38: // 上
                    e.preventDefault();
                    if (index > 0) {
                        $items.removeClass('selected').css({ backgroundColor: '', color: '' });
                        $items.eq(index - 1).addClass('selected').css({ backgroundColor: '#007bff', color: '#fff' });
                    }
                    break;
                case 40: // 下
                    e.preventDefault();
                    if (index < $items.length - 1) {
                        $items.removeClass('selected').css({ backgroundColor: '', color: '' });
                        $items.eq(index + 1).addClass('selected').css({ backgroundColor: '#007bff', color: '#fff' });
                    } else if (index === -1) {
                        $items.eq(0).addClass('selected').css({ backgroundColor: '#007bff', color: '#fff' });
                    }
                    break;
                case 13: // 回车
                    e.preventDefault();
                    if ($selected.length) {
                        $selected.click();
                    }
                    break;
                case 27: // ESC
                    e.preventDefault();
                    $('.ac_custom_results').remove();
                    break;
            }
        });
    }
    
    // 设置收件人和抄送人输入框
    setupEmailInput('toEmail');
    setupEmailInput('ccEmail');
    
    console.log('自定义自动完成功能已初始化');
}

// 全局变量控制工具栏显示模式
let isFullToolbarMode = false;

// 获取工具栏配置
function getToolbarConfig(isFullMode) {
    if (isFullMode) {
        // 完整工具栏 - 包含所有功能
        return [
            'undo redo | toggleToolbar',
            'formatselect fontfamily fontsize | bold italic underline strikethrough | forecolor backcolor',
            'alignleft aligncenter alignright alignjustify | outdent indent | ltr rtl',
            'bullist numlist | blockquote | subscript superscript | pagebreak nonbreaking',
            'table tablestyles optimizetable | link unlink anchor | image media | charmap emoticons inserttime',
            'searchreplace | visualblocks visualchars code codesample preview fullscreen | removeformat help'
        ].join(' | ');
    } else {
        // 基础工具栏 - 常用功能
        return 'undo redo | fontfamily fontsize | bold italic underline | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | table optimizetable | link image | toggleToolbar';
    }
}

// 切换编辑器工具栏
function toggleEditorToolbar() {
    const editor = tinymce.get('emailContent');
    if (!editor) return;
    
    // 切换模式
    isFullToolbarMode = !isFullToolbarMode;
    
    // 保存当前内容
    const currentContent = editor.getContent();
    
    // 销毁当前编辑器
    editor.destroy();
    
    // 重新初始化编辑器
    setTimeout(function() {
        initEditor();
        
        // 等待编辑器初始化完成后恢复内容
        setTimeout(function() {
            const newEditor = tinymce.get('emailContent');
            if (newEditor) {
                newEditor.setContent(currentContent);
                newEditor.focus();
                
                // 显示提示
                const mode = isFullToolbarMode ? '完整' : '简洁';
                layer.msg('已切换到' + mode + '工具栏模式', {icon: 1, time: 2000});
            }
        }, 500);
    }, 100);
}



// 初始化TinyMCE编辑器
function initEditor() {
    tinymce.init({
        selector: '#emailContent',
        height: 400,
        menubar: false,
        base_url: '/assets/libs/tinymce',
        language: 'zh_CN',
        // 扩展插件列表，只使用存在的插件
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview', 'anchor',
            'searchreplace', 'visualblocks', 'code', 'fullscreen', 'insertdatetime', 'media',
            'table', 'tableenhancement', 'help', 'wordcount', 'emoticons', 'nonbreaking', 'pagebreak', 'save',
            'directionality', 'autosave', 'visualchars', 'codesample'
        ].join(' '),
        // 基础工具栏（默认显示）
        toolbar: getToolbarConfig(isFullToolbarMode),
        // 工具栏模式：wrap表示自动换行，floating表示浮动，sliding表示滑动
        toolbar_mode: 'wrap',
        // 内容样式
        content_css: [
            '/assets/libs/tinymce/skins/ui/oxide/content.min.css',
            '/assets/libs/tinymce/skins/content/default/content.min.css'
        ],
        content_style: `
            body {
                font-family: "Microsoft YaHei", Arial, sans-serif;
                font-size: 12pt;
                line-height: 1.6;
            }
            /* 邮件兼容的表格样式 */
            table {
                border-collapse: collapse !important;
                mso-table-lspace: 0pt !important;
                mso-table-rspace: 0pt !important;
                border-spacing: 0 !important;
                border: none !important;
                table-layout: fixed !important;
                margin: 0 auto !important;
            }
            table td, table th {
                border: 1px solid #ddd !important;
                padding: 8px !important;
                vertical-align: top !important;
                word-wrap: break-word !important;
                word-break: break-all !important;
                mso-line-height-rule: exactly !important;
            }
            table th {
                background-color: #f5f5f5 !important;
                font-weight: bold !important;
                text-align: center !important;
            }
            /* 防止表格在邮件客户端中变形 */
            table[style*="width"] {
                width: auto !important;
                max-width: 100% !important;
            }
            /* Outlook兼容性 */
            table {
                mso-cellspacing: 0 !important;
                mso-padding-alt: 0 !important;
            }
        `,
        license_key: 'gpl',
        
        // 字体配置 - 确保字体能正确显示
        font_size_formats: '8pt 9pt 10pt 11pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 26pt 28pt 36pt 48pt 72pt',
        font_family_formats: 
            '微软雅黑=Microsoft YaHei,sans-serif;' +
            '宋体=SimSun,serif;' +
            '黑体=SimHei,sans-serif;' +
            '楷体=KaiTi,serif;' +
            '仿宋=FangSong,serif;' +
            'Arial=arial,helvetica,sans-serif;' +
            'Times New Roman=times new roman,times,serif;' +
            'Courier New=courier new,courier,monospace;' +
            'Verdana=verdana,geneva,sans-serif;' +
            'Georgia=georgia,palatino,serif;' +
            'Helvetica=helvetica,arial,sans-serif',
        // 颜色配置
        color_map: [
            "000000", "黑色",
            "993300", "深红色", 
            "333300", "深黄色",
            "003300", "深绿色",
            "003366", "深青色",
            "000080", "深蓝色",
            "333399", "靛蓝色",
            "333333", "深灰色",
            "800000", "栗色",
            "FF6600", "橙色",
            "808000", "橄榄色",
            "008000", "绿色",
            "008080", "青色",
            "0000FF", "蓝色",
            "666699", "灰蓝色",
            "808080", "灰色",
            "FF0000", "红色",
            "FF9900", "琥珀色",
            "99CC00", "黄绿色",
            "339966", "海绿色",
            "33CCCC", "浅青色",
            "3366FF", "浅蓝色",
            "800080", "紫色",
            "999999", "中灰色",
            "FF00FF", "洋红色",
            "FFCC00", "金色",
            "FFFF00", "黄色",
            "00FF00", "酸橙色",
            "00FFFF", "水蓝色",
            "00CCFF", "天蓝色",
            "993366", "红紫色",
            "C0C0C0", "银色",
            "FF99CC", "粉红色",
            "FFCC99", "桃色",
            "FFFF99", "浅黄色",
            "CCFFCC", "浅绿色",
            "CCFFFF", "浅青色",
            "99CCFF", "浅蓝色",
            "CC99FF", "淡紫色",
            "FFFFFF", "白色"
        ],
        
        // 表格配置 - 启用表格调整功能
        table_toolbar: 'tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol',
        table_appearance_options: true,  // 启用表格外观选项
        table_grid: true,  // 启用表格网格，支持拖拽调整
        table_resize_bars: true,  // 启用表格调整条
        table_sizing_mode: 'relative',  // 表格尺寸模式：relative, fixed, responsive
        table_column_resizing: true,  // 启用列宽调整
        table_row_resizing: true,  // 启用行高调整
        table_default_attributes: {
            'border': '1',
            'cellpadding': '5',
            'cellspacing': '0',
            'style': 'border-collapse: collapse; width: 100%; table-layout: auto;'  // 添加table-layout: auto支持调整
        },
        table_default_styles: {
            'border-collapse': 'collapse',
            'width': '100%',
            'table-layout': 'auto'  // 自动表格布局，支持列宽调整
        },
        // 表格样式类
        table_class_list: [
            {title: '无样式', value: ''},
            {title: '边框表格', value: 'table-bordered'},
            {title: '条纹表格', value: 'table-striped'},
            {title: '紧凑表格', value: 'table-condensed'}
        ],
        
        // 图片上传配置
        images_upload_url: '/admin/emailMessages/uploadImage',
        images_upload_base_path: '',
        automatic_uploads: true,
        file_picker_types: 'image',
        image_advtab: true,
        image_title: true,
        image_caption: true,
        
        // 粘贴配置
        paste_data_images: true,
        paste_as_text: false,
        paste_auto_cleanup_on_paste: true,
        paste_remove_styles: false,
        paste_remove_styles_if_webkit: false,
        
        // 自动保存配置
        save_onsavecallback: function() {
            saveDraft(true); // 自动保存草稿
        },
        
        // 自定义按钮
        setup: function(editor) {
            // 添加工具栏切换按钮
            editor.ui.registry.addButton('toggleToolbar', {
                text: isFullToolbarMode ? '更少' : '更多',
                tooltip: isFullToolbarMode ? '切换到简洁工具栏' : '切换到完整工具栏',
                onAction: function() {
                    toggleEditorToolbar();
                }
            });
            
            // 添加插入当前时间按钮
            editor.ui.registry.addButton('inserttime', {
                text: '时间',
                tooltip: '插入当前时间',
                onAction: function() {
                    const now = new Date();
                    const timeStr = now.getFullYear() + '-' +
                                  (now.getMonth() + 1).toString().padStart(2, '0') + '-' +
                                  now.getDate().toString().padStart(2, '0') + ' ' +
                                  now.getHours().toString().padStart(2, '0') + ':' +
                                  now.getMinutes().toString().padStart(2, '0');
                    editor.insertContent(timeStr);
                }
            });

            // 添加键盘快捷键支持
            editor.on('keydown', function(e) {
                // Ctrl+Enter 或 Cmd+Enter 发送邮件
                if ((e.ctrlKey || e.metaKey) && e.keyCode === 13) {
                    e.preventDefault();
                    sendEmail();
                    return false;
                }
                // Ctrl+S 或 Cmd+S 保存草稿
                if ((e.ctrlKey || e.metaKey) && e.keyCode === 83) {
                    e.preventDefault();
                    saveDraft();
                    return false;
                }
            });
        },
        // 初始化完成后回调
        init_instance_callback: function(editor) {
            console.log('TinyMCE编辑器初始化完成');
            
            // 检查是否是草稿编辑模式
            var draftContentElement = document.getElementById('draftContentHolder');
            if (draftContentElement) {
                // 如果有草稿内容，直接加载
                var draftContent = draftContentElement.value;
                if (draftContent) {
                    // 临时禁用内容变化检测
                    const originalFlag = isPageInitialized;
                    isPageInitialized = false;
                    
                    editor.setContent(draftContent);
                    console.log('[Draft] 已加载草稿内容');
                    
                    // 恢复内容变化检测状态
                    setTimeout(function() {
                        isPageInitialized = originalFlag;
                    }, 500);
                }
                editor.focus();
            } else {
                // 检查是否有原始邮件内容需要填充
                var hasOriginalEmail = $('#originalEmailContent').length > 0;
                
                if (hasOriginalEmail) {
                    // 这里不需要做任何事情，loadOriginalEmailContent函数会处理内容填充
                    // 只需要确保编辑器已初始化完成
                    editor.focus();
                }
            }
            
            // 触发LoadContent事件，确保编辑器变化检测能正确初始化
            setTimeout(function() {
                editor.fire('LoadContent');
            }, 100);
        },
        // 文件选择器回调
        file_picker_callback: function (callback, value, meta) {
            // 仅处理图片
            if (meta.filetype === 'image') {
                var input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*,.heic,.heif');
                
                input.onchange = function () {
                    var file = this.files[0];
                    var reader = new FileReader();
                    
                    reader.onload = function () {
                        var id = 'blobid' + (new Date()).getTime();
                        var blobCache = tinymce.activeEditor.editorUpload.blobCache;
                        var base64 = reader.result.split(',')[1];
                        var blobInfo = blobCache.create(id, file, base64);
                        blobCache.add(blobInfo);
                        
                        // 调用回调函数，将图片插入编辑器
                        callback(blobInfo.blobUri(), { title: file.name });
                    };
                    
                    reader.readAsDataURL(file);
                };
                
                input.click();
            }
        }
    });
}

// 优化邮件中的表格样式，确保在各种邮件客户端中正常显示
function optimizeEmailTables(htmlContent) {
    // 创建临时DOM元素来处理HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;

    // 获取所有表格
    const tables = tempDiv.querySelectorAll('table');

    tables.forEach(table => {
        // 设置表格基本属性
        table.setAttribute('cellpadding', '0');
        table.setAttribute('cellspacing', '0');
        table.setAttribute('border', '0');
        table.style.borderCollapse = 'collapse';
        table.style.msoTableLspace = '0pt';
        table.style.msoTableRspace = '0pt';
        table.style.borderSpacing = '0';
        table.style.width = '100%';
        table.style.maxWidth = '600px'; // 限制最大宽度，防止在移动设备上过宽
        table.style.margin = '0 auto';

        // 处理表格单元格
        const cells = table.querySelectorAll('td, th');
        cells.forEach(cell => {
            // 设置单元格样式
            cell.style.border = '1px solid #ddd';
            cell.style.padding = '8px';
            cell.style.verticalAlign = 'top';
            cell.style.wordWrap = 'break-word';
            cell.style.wordBreak = 'break-all';
            cell.style.msoLineHeightRule = 'exactly';

            // 表头特殊处理
            if (cell.tagName.toLowerCase() === 'th') {
                cell.style.backgroundColor = '#f5f5f5';
                cell.style.fontWeight = 'bold';
                cell.style.textAlign = 'center';
            }

            // 移除可能导致问题的样式属性
            cell.removeAttribute('width');
            cell.removeAttribute('height');
        });
    });

    return tempDiv.innerHTML;
}

// 发送邮件
function sendEmail() {
    // 检查是否有附件正在上传
    if (window.uploadingCount > 0) {
        layer.msg('请等待附件上传完成后再发送', {icon: 2});
        return;
    }

    // 标记为正在提交表单
    isFormSubmitting = true;

    // 获取编辑器内容并优化表格
    let content = tinymce.get('emailContent').getContent();
    content = optimizeEmailTables(content);

    // 表单验证
    if (!$('#fromEmail').val()) {
        layer.msg('请选择发件箱', {icon: 2});
        isFormSubmitting = false;
        return;
    }

    if (!$('#toEmail').val()) {
        layer.msg('请填写收件人', {icon: 2});
        isFormSubmitting = false;
        return;
    }

    if (!$('#subject').val()) {
        layer.msg('请填写邮件主题', {icon: 2});
        isFormSubmitting = false;
        return;
    }

    // 显示发送中提示
    const loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });

    // 准备表单数据 - 使用普通表单序列化
    const formData = $('#replyEmailForm').serialize() + '&content=' + encodeURIComponent(content);

    // 添加调试信息
    console.log('=== 发送邮件前端调试 ===');
    console.log('表单序列化数据:', $('#replyEmailForm').serialize());
    console.log('完整数据:', formData);
    console.log('fromEmail值:', $('#fromEmail').val());
    console.log('toEmail值:', $('#toEmail').val());
    console.log('========================');

    // 发送请求
    $.ajax({
        url: '/admin/emailMessages/sendEmail',
        type: 'POST',
        data: formData,
        success: function(res) {
            layer.close(loadingIndex);

            if (res.state === 'ok') {
                // 发送成功，强制清除所有保护机制
                forceRemovePageProtection();

                // 显示简短的成功消息，然后立即关闭页面
                layer.msg('邮件已提交发送，页面即将关闭', {icon: 1, time: 800});

                // 立即关闭页面，不等待发送完成
                setTimeout(function() {
                    // 强制标记为提交状态，绕过任何剩余的保护
                    window.isFormSubmitting = true;
                    window.isContentModified = false;

                    // 如果在弹窗中，关闭弹窗
                    if (window.parent && window.parent.layer && window.parent.layer.getFrameIndex) {
                        try {
                            const frameIndex = window.parent.layer.getFrameIndex(window.name);
                            if (frameIndex) {
                                window.parent.layer.close(frameIndex);
                                if (typeof window.parent.refreshEmails === 'function') {
                                    window.parent.refreshEmails();
                                }
                                return;
                            }
                        } catch (e) {
                            console.log('关闭弹窗失败，尝试其他方式');
                        }
                    }

                    // 尝试关闭当前窗口（如果是弹出窗口）
                    try {
                        window.close();
                    } catch (e) {
                        console.log('无法关闭窗口，跳转到列表页');
                    }

                    // 否则返回列表页
                    window.location.href = '/admin/emailMessages';
                }, 500); // 进一步缩短等待时间
            } else {
                layer.msg(res.msg || '邮件发送失败', {icon: 2});
                isFormSubmitting = false;
            }
        },
        error: function() {
            layer.close(loadingIndex);
            layer.msg('网络错误，请稍后重试', {icon: 2});
            isFormSubmitting = false;
        }
    });
}

// 保存草稿
function saveDraft(isAutoSave = false) {
    // 获取编辑器内容
    const content = tinymce.get('emailContent').getContent();
    
    // 表单验证 - 草稿不需要所有字段都必填
    if (!$('#fromEmail').val()) {
        if (!isAutoSave) layer.msg('请选择发件箱', {icon: 2});
        return;
    }
    
    if (!$('#toEmail').val()) {
        if (!isAutoSave) layer.msg('请填写收件人', {icon: 2});
        return;
    }
    
    if (!$('#subject').val()) {
        if (!isAutoSave) layer.msg('请填写邮件主题', {icon: 2});
        return;
    }
    
    // 显示加载提示（自动保存时不显示）
    let loadingIndex = null;
    if (!isAutoSave) {
        loadingIndex = layer.load(1, {
            shade: [0.3, '#000']
        });
    }
    
    // 准备表单数据，使用与sendEmail相同的格式 - 使用普通表单序列化
    const formData = $('#replyEmailForm').serialize() + '&content=' + encodeURIComponent(content);

    // 发送保存草稿请求
    $.ajax({
        url: '/admin/emailMessages/saveDraft',
        type: 'POST',
        data: formData,
        success: function(res) {
            if (loadingIndex) layer.close(loadingIndex);
            
            if (res.state === 'ok') {
                // 草稿保存成功，清除内容修改标记
                clearContentModified();
                
                if (!isAutoSave) {
                    layer.msg('草稿已保存', {icon: 1, time: 2000});
                } else {
                    console.log('自动保存草稿成功');
                }
                
                // 更新draftId，后续保存时会更新而不是新建
                if (res.data && res.data.id) {
                    $('input[name="draftId"]').val(res.data.id);
                }
                
                // 更新原始表单数据
                saveOriginalFormData();
                
                // 可选：显示保存时间提示（仅手动保存时）
                if (!isAutoSave) {
                    setTimeout(function() {
                        const now = new Date();
                        const timeStr = now.getHours().toString().padStart(2, '0') + ':' + 
                                      now.getMinutes().toString().padStart(2, '0');
                        layer.msg('草稿已于 ' + timeStr + ' 保存', {icon: 1, time: 1500});
                    }, 500);
                }
                
            } else {
                if (!isAutoSave) {
                    layer.msg(res.msg || '保存草稿失败', {icon: 2});
                } else {
                    console.error('自动保存草稿失败:', res.msg);
                }
            }
        },
        error: function() {
            if (loadingIndex) layer.close(loadingIndex);
            if (!isAutoSave) {
                layer.msg('网络错误，保存草稿失败', {icon: 2});
            } else {
                console.error('自动保存草稿网络错误');
            }
        }
    });
}

// 优化邮件显示
function optimizeEmailDisplay(container) {
    if (!container) return;
    
    // 移除空段落和空div
    const emptyElements = container.querySelectorAll('p:empty, div:empty, span:empty');
    emptyElements.forEach(el => {
        el.parentNode.removeChild(el);
    });

    // 移除只包含&nbsp;的元素
    const nbspElements = container.querySelectorAll('p, div, span');
    nbspElements.forEach(el => {
        if (el.innerHTML.trim() === '&nbsp;' || el.innerHTML.trim() === ' ') {
            el.parentNode.removeChild(el);
        }
    });

    // 处理Outlook特定格式
    const outlookSpecific = container.querySelectorAll('.MsoNormal');
    outlookSpecific.forEach(el => {
        el.style.margin = '0';
        el.style.padding = '0';
        el.style.lineHeight = '1.3';
    });

    return container;
}

// 切换显示原邮件内容
function toggleOriginalEmail() {
    // 创建弹窗显示原邮件内容
    const originalContent = document.querySelector('#originalEmailContent .card-body').innerHTML;
    const originalSubject = '#(originalEmail.subject??)';
    const originalFrom = '#(originalEmail.fromAddress??)';
    const originalDate = '#(originalEmail.sentDate??)';
    
    // 构建内容，包括工具栏
    const content = `
        <div class="original-email-dialog">
            <div class="original-email-toolbar p-2 bg-light border-bottom">
                <div class="row">
                    <div class="col-md-6">
                        <small><strong>发件人：</strong>${originalFrom}</small><br>
                        <small><strong>时间：</strong>${originalDate}</small>
                    </div>
                    <div class="col-md-6 text-right">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="quoteOriginalEmailFromDialog()">
                            <i class="fa fa-quote-left"></i> 引用此邮件
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-3 original-email-content">${originalContent}</div>
        </div>
    `;
    
    layer.open({
        type: 1,
        title: '原始邮件: ' + originalSubject,
        area: ['80%', '70%'],
        shadeClose: true,
        maxmin: true,
        content: content,
        success: function(layero, index) {
            // 优化显示
            const contentEl = layero.find('.original-email-content')[0];
            optimizeEmailDisplay(contentEl);
            
            // 添加样式
            $(contentEl).css({
                'line-height': '1.4',
                'font-size': '14px',
                'max-height': 'calc(100% - 60px)', // 减去工具栏高度
                'overflow-y': 'auto'
            });
            
            // 为图片添加点击预览功能
            $(contentEl).find('img').on('click', function() {
                previewImage($(this).attr('src'));
            });
        }
    });
}

// 从弹窗中引用原邮件
function quoteOriginalEmailFromDialog() {
    // 关闭当前弹窗
    layer.closeAll();
    
    // 调用原来的引用函数
    quoteOriginalEmail();
}

// 引用原邮件内容
function quoteOriginalEmail() {
    const editor = tinymce.get('emailContent');
    if (!editor) {
        console.error('编辑器未初始化');
        return;
    }

    // 获取优化后的原始内容
    const originalContentEl = document.querySelector('#originalEmailContent .card-body');
    if (!originalContentEl) {
        console.warn('找不到原始邮件内容元素');
        return;
    }
    
    // 先优化原始内容以移除多余空行
    optimizeEmailDisplay(originalContentEl);
    const originalContent = originalContentEl.innerHTML;

    const originalDate = '#(originalEmail.sentDate??)';
    const originalFrom = '#(originalEmail.fromAddress??)';

    const quoteHeader = `<p class="quote-header">On ${originalDate}, ${originalFrom} wrote:</p>`;
    const quotedContent = `
        <div class="quoted-content">
            ${originalContent}
        </div>
        <p><br></p>
    `;
    
    // 在光标位置插入引用内容
    editor.execCommand('mceInsertContent', false, quoteHeader + quotedContent);
}

// 图片预览功能
function previewImage(src) {
    if (!src) return;
    
    layer.open({
        type: 1,
        title: false,
        closeBtn: 1,
        shadeClose: true,
        area: ['80%', '80%'],
        content: `<div class="text-center p-3"><img src="${src}" style="max-width:100%; max-height:calc(100vh - 100px);"></div>`,
        success: function(layero) {
            // 添加键盘导航
            $(document).on('keydown.imagePreview', function(e) {
                if (e.keyCode === 27) { // ESC键
                    layer.closeAll();
                    $(document).off('keydown.imagePreview');
                }
            });
            
            // 添加点击关闭
            $(layero).find('img').on('click', function() {
                layer.closeAll();
                $(document).off('keydown.imagePreview');
            });
        }
    });
}

// 显示签名选择
function showSignatures() {
    // 先移除可能存在的遮罩层
    removeBackdrop();
    
    // 显示加载中提示
    $('#signatureList').html('<div class="text-center p-3"><i class="fa fa-spinner fa-spin"></i> 正在加载签名...</div>');
    
    // 显示模态框
    const signatureModal = new bootstrap.Modal(document.getElementById('signatureModal'), {
        backdrop: 'static',  // 设置为static可以防止点击背景关闭模态框
        keyboard: true       // 允许按ESC键关闭
    });
    signatureModal.show();
    
    // 获取签名列表
    $.ajax({
        url: '/admin/emailMessages/getSignatures',
        type: 'GET',
        dataType: 'json',
        data: {
            fromEmail: $('#fromEmail').val() // 传递当前选中的发件箱
        },
        success: function(res) {
            if (res.state === 'ok' && res.data && res.data.length > 0) {
                let html = '';
                
                // 构建签名列表
                res.data.forEach(function(signature, index) {
                    html += `
                        <div class="list-group-item signature-item" data-id="${signature.id}">
                            <div class="d-flex align-items-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="signatureRadio" 
                                        id="signature${signature.id}" value="${signature.id}" ${index === 0 ? 'checked' : ''}>
                                    <label class="form-check-label" for="signature${signature.id}">
                                        ${signature.name}
                                    </label>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-info ms-auto" 
                                    onclick="previewSignature(${signature.id})">
                                    <i class="fa fa-eye"></i> 预览
                                </button>
                            </div>
                        </div>
                    `;
                });
                
                $('#signatureList').html(html);
            } else {
                $('#signatureList').html('<div class="alert alert-info">没有可用的签名，请先创建签名</div>');
                
                // 添加创建签名的按钮
                $('#signatureList').append(`
                    <div class="text-center mt-3">
                        <a href="/admin/emailSignature/add" target="_blank" class="btn btn-outline-primary">
                            <i class="fa fa-plus"></i> 创建新签名
                        </a>
                    </div>
                `);
            }
        },
        error: function() {
            $('#signatureList').html('<div class="alert alert-danger">获取签名失败，请稍后重试</div>');
        }
    });
}

// 预览签名
function previewSignature(signatureId) {
    $.ajax({
        url: '/admin/emailMessages/getSignatureContent',
        type: 'GET',
        dataType: 'json',
        data: { id: signatureId },
        success: function(res) {
            if (res.state === 'ok' && res.data) {
                // 先关闭可能存在的预览模态框
                const existingPreviewModal = bootstrap.Modal.getInstance(document.getElementById('previewModal'));
                if (existingPreviewModal) {
                    existingPreviewModal.hide();
                    // 手动移除遮罩层
                    removeBackdrop();
                }
                
                // 显示预览模态框
                $('#previewModal .modal-title').text('签名预览');
                $('#previewContent').html(res.data.content);
                
                const previewModal = new bootstrap.Modal(document.getElementById('previewModal'), {
                    backdrop: 'static',
                    keyboard: true
                });
                previewModal.show();
            } else {
                layer.msg('获取签名内容失败', {icon: 2});
            }
        },
        error: function() {
            layer.msg('网络错误，请稍后重试', {icon: 2});
        }
    });
}

// 插入签名
function insertSignature() {
    const selectedSignatureId = $('input[name="signatureRadio"]:checked').val();
    
    if (!selectedSignatureId) {
        layer.msg('请选择一个签名', {icon: 0});
        return;
    }
    
    $.ajax({
        url: '/admin/emailMessages/getSignatureContent',
        type: 'GET',
        dataType: 'json',
        data: { id: selectedSignatureId },
        success: function(res) {
            if (res.state === 'ok' && res.data) {
                // 关闭模态框
                const signatureModal = bootstrap.Modal.getInstance(document.getElementById('signatureModal'));
                if (signatureModal) {
                    signatureModal.hide();
                    // 手动移除遮罩层
                    removeBackdrop();
                }
                
                // 获取编辑器实例
                const editor = tinymce.get('emailContent');
                if (editor) {
                    // 在光标位置插入签名内容，或添加到末尾
                    editor.execCommand('mceInsertContent', false, '<p><br></p>' + res.data.content);
                    
                    // 提示成功
                    layer.msg('已插入签名', {icon: 1});
                }
            } else {
                layer.msg('获取签名内容失败', {icon: 2});
            }
        },
        error: function() {
            layer.msg('网络错误，请稍后重试', {icon: 2});
        }
    });
}

// 显示模板选择
function showTemplates() {
    // 先移除可能存在的遮罩层
    removeBackdrop();
    
    // 显示加载中提示
    $('#templateList').html('<div class="text-center p-3"><i class="fa fa-spinner fa-spin"></i> 正在加载模板...</div>');
    
    // 显示模态框
    const templateModal = new bootstrap.Modal(document.getElementById('templateModal'), {
        backdrop: 'static',  // 设置为static可以防止点击背景关闭模态框
        keyboard: true       // 允许按ESC键关闭
    });
    templateModal.show();
    
    // 获取模板列表
    $.ajax({
        url: '/admin/emailMessages/getTemplates',
        type: 'GET',
        dataType: 'json',
        success: function(res) {
            if (res.state === 'ok' && res.data && res.data.length > 0) {
                let html = '';
                
                // 构建模板列表
                res.data.forEach(function(template, index) {
                    html += `
                        <div class="list-group-item template-item" data-id="${template.id}">
                            <div class="d-flex align-items-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="templateRadio" 
                                        id="template${template.id}" value="${template.id}" ${index === 0 ? 'checked' : ''}>
                                    <label class="form-check-label" for="template${template.id}">
                                        ${template.name}
                                    </label>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-info ms-auto" 
                                    onclick="previewTemplate(${template.id})">
                                    <i class="fa fa-eye"></i> 预览
                                </button>
                            </div>
                        </div>
                    `;
                });
                
                $('#templateList').html(html);
            } else {
                $('#templateList').html('<div class="alert alert-info">没有可用的模板，请先创建模板</div>');
                
                // 添加创建模板的按钮
                $('#templateList').append(`
                    <div class="text-center mt-3">
                        <a href="/admin/emailTemplate/add" target="_blank" class="btn btn-outline-primary">
                            <i class="fa fa-plus"></i> 创建新模板
                        </a>
                    </div>
                `);
            }
        },
        error: function() {
            $('#templateList').html('<div class="alert alert-danger">获取模板失败，请稍后重试</div>');
        }
    });
}

// 预览模板
function previewTemplate(templateId) {
    $.ajax({
        url: '/admin/emailMessages/getTemplateContent',
        type: 'GET',
        dataType: 'json',
        data: { id: templateId },
        success: function(res) {
            if (res.state === 'ok' && res.data) {
                // 先关闭可能存在的预览模态框
                const existingPreviewModal = bootstrap.Modal.getInstance(document.getElementById('previewModal'));
                if (existingPreviewModal) {
                    existingPreviewModal.hide();
                    // 手动移除遮罩层
                    removeBackdrop();
                }
                
                // 显示预览模态框
                $('#previewModal .modal-title').text('模板预览');
                $('#previewContent').html(res.data.content);
                
                const previewModal = new bootstrap.Modal(document.getElementById('previewModal'), {
                    backdrop: 'static',
                    keyboard: true
                });
                previewModal.show();
            } else {
                layer.msg('获取模板内容失败', {icon: 2});
            }
        },
        error: function() {
            layer.msg('网络错误，请稍后重试', {icon: 2});
        }
    });
}

// 应用模板
function applyTemplate() {
    const selectedTemplateId = $('input[name="templateRadio"]:checked').val();
    
    if (!selectedTemplateId) {
        layer.msg('请选择一个模板', {icon: 0});
        return;
    }
    
    // 显示确认对话框
    layer.confirm('应用模板将替换当前编辑器内容，是否继续？', {
        icon: 3,
        title: '提示'
    }, function(index) {
        layer.close(index);
        
        $.ajax({
            url: '/admin/emailMessages/getTemplateContent',
            type: 'GET',
            dataType: 'json',
            data: { id: selectedTemplateId },
            success: function(res) {
                if (res.state === 'ok' && res.data) {
                    // 关闭模态框
                    const templateModal = bootstrap.Modal.getInstance(document.getElementById('templateModal'));
                    if (templateModal) {
                        templateModal.hide();
                        // 手动移除遮罩层
                        removeBackdrop();
                    }
                    
                    // 获取编辑器实例
                    const editor = tinymce.get('emailContent');
                    if (editor) {
                        // 替换编辑器内容
                        editor.setContent(res.data.content);
                        
                        // 提示成功
                        layer.msg('已应用模板', {icon: 1});
                    }
                } else {
                    layer.msg('获取模板内容失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请稍后重试', {icon: 2});
            }
        });
    });
}

// 加载原始邮件内容用于回复或转发
function loadOriginalEmailContent(emailId, isForward) {
    if (!emailId || !tinymce.get('emailContent')) {
        return;
    }
    
    // 获取原始邮件数据，用于构建回复或转发内容
    var originalContent = $('#originalEmailContent .card-body').html();
    var originalSubject = $('#subject').val();
    var originalFrom = isForward ? '' : $('#toEmail').val();
    var originalDate = ''; // 如果有日期字段可以添加
    
    if (!originalContent) {
        return;
    }
    
    // 检查是否存在翻译内容
    var hasTranslationContent = $('.translation-content').length > 0;
    
    if (isForward) {
        // 如果有翻译内容，则不需要添加原始内容
        if (!hasTranslationContent) {
            // 转发邮件，直接包含原始内容
            var forwardHeader = '<p>---------- Forwarded message ----------</p>';
            var forwardContent = '<div>' + originalContent + '</div>';
            
                    // 获取编辑器实例
        var editor = tinymce.get('emailContent');
        if (editor) {
            // 临时禁用内容变化检测
            const originalFlag = isPageInitialized;
            isPageInitialized = false;
            
            // 设置内容，顶部添加空行
            editor.setContent('<p><br></p>' + forwardHeader + forwardContent);
            
            // 将光标移动到顶部
            editor.selection.select(editor.getBody().firstChild, true);
            editor.selection.collapse(true);
            
            // 设置焦点
            editor.focus();
            
            // 恢复内容变化检测状态
            setTimeout(function() {
                isPageInitialized = originalFlag;
            }, 500);
        }
        } else {
            // 有翻译内容，只添加一个空行
            var editor = tinymce.get('emailContent');
            if (editor) {
                editor.setContent('<p><br></p>');
                editor.focus();
            }
        }
    } else {
        // 回复邮件，添加引用格式
        var quoteHeader = '<p class="quote-header">On ' + (originalDate || 'before') + ', ' + (originalFrom || 'sender') + ' wrote:</p>';
        var quotedContent = '<div class="quoted-content">' + originalContent + '</div>';
        
        // 获取编辑器实例
        var editor = tinymce.get('emailContent');
        if (editor) {
            // 临时禁用内容变化检测
            const originalFlag = isPageInitialized;
            isPageInitialized = false;
            
            // 设置内容，顶部添加空行
            editor.setContent('<p><br></p>' + quoteHeader + quotedContent);
            
            // 将光标移动到顶部
            editor.selection.select(editor.getBody().firstChild, true);
            editor.selection.collapse(true);
            
            // 设置焦点
            editor.focus();
            
            // 恢复内容变化检测状态
            setTimeout(function() {
                isPageInitialized = originalFlag;
            }, 500);
        }
    }
}

// 初始化模态框
function initModals() {
    // 确保所有模态框使用Bootstrap 5的方式初始化
    const signatureModal = new bootstrap.Modal(document.getElementById('signatureModal'));
    const templateModal = new bootstrap.Modal(document.getElementById('templateModal'));
    const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
    const aiDialog = new bootstrap.Modal(document.getElementById('aiDialog'));
    const aiResultDialog = new bootstrap.Modal(document.getElementById('aiResultDialog'));
    const translationModal = new bootstrap.Modal(document.getElementById('translationModal'));
    
    // 监听模态框关闭事件，移除遮罩层
    document.getElementById('signatureModal').addEventListener('hidden.bs.modal', function () {
        removeBackdrop();
    });
    
    document.getElementById('templateModal').addEventListener('hidden.bs.modal', function () {
        removeBackdrop();
    });
    
    document.getElementById('previewModal').addEventListener('hidden.bs.modal', function () {
        removeBackdrop();
    });
    
    document.getElementById('aiDialog').addEventListener('hidden.bs.modal', function () {
        removeBackdrop();
    });
    
    document.getElementById('aiResultDialog').addEventListener('hidden.bs.modal', function () {
        removeBackdrop();
    });
    
    document.getElementById('translationModal').addEventListener('hidden.bs.modal', function () {
        removeBackdrop();
    });
}

// 手动移除遮罩层
function removeBackdrop() {
    // 移除所有遮罩层
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => {
        backdrop.remove();
    });
    
    // 移除body上的modal-open类
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
}

// 监听ESC键，关闭所有模态框
function closeAllModals() {
    const modals = [
        'signatureModal',
        'templateModal',
        'previewModal',
        'aiDialog',
        'aiResultDialog',
        'translationModal'
    ];
    
    modals.forEach(function(modalId) {
        const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
        if (modal) {
            modal.hide();
        }
    });
    
    removeBackdrop();
}

// 预览附件
function previewAttachment(url, fileName) {
    // 检查是否是图片
    if (fileName.match(/\.(jpg|jpeg|png|gif|bmp|webp)$/i)) {
        showImagePreview(url, fileName);
    } else {
        // 非图片文件，直接下载
        window.open(url, '_blank');
    }
}

// 显示图片预览
function showImagePreview(src, title) {
    // 创建预览模态框
    const modal = $(`
        <div class="image-preview-modal">
            <span class="image-preview-close">&times;</span>
            <div class="image-preview-content">
                <img src="${src}" alt="${title || ''}">
                ${title ? `<div class="image-preview-title">${title}</div>` : ''}
            </div>
        </div>
    `);
    
    // 添加到body
    $('body').append(modal);
    
    // 显示模态框
    modal.fadeIn(200);
    
    // 点击关闭按钮或模态框背景关闭
    modal.on('click', function(e) {
        if ($(e.target).hasClass('image-preview-modal') || $(e.target).hasClass('image-preview-close')) {
            modal.fadeOut(200, function() {
                modal.remove();
            });
        }
    });
    
    // ESC键关闭
    $(document).on('keydown.imagePreview', function(e) {
        if (e.keyCode === 27) { // ESC键
            modal.fadeOut(200, function() {
                modal.remove();
            });
            $(document).off('keydown.imagePreview');
        }
    });
    
    // 添加图片缩放功能
    const img = modal.find('img');
    let scale = 1;
    const scaleStep = 0.1;
    
    // 鼠标滚轮缩放
    img.on('wheel', function(e) {
        e.preventDefault();
        
        if (e.originalEvent.deltaY < 0) {
            // 放大
            scale += scaleStep;
            if (scale > 3) scale = 3; // 最大放大3倍
        } else {
            // 缩小
            scale -= scaleStep;
            if (scale < 0.5) scale = 0.5; // 最小缩小到0.5倍
        }
        
        img.css('transform', `scale(${scale})`);
    });
    
    // 双击恢复原始大小
    img.on('dblclick', function() {
        scale = 1;
        img.css('transform', 'scale(1)');
    });
}

// 显示翻译内容
function showTranslationContent() {
    // 先移除可能存在的遮罩层
    removeBackdrop();
    
    // 获取原始邮件ID
    const originalEmailId = $('input[name="originalEmailId"]').val();
    
    if (!originalEmailId) {
        layer.msg('无法获取原始邮件ID', {icon: 2});
        return;
    }
    
    // 显示模态框
    const translationModal = new bootstrap.Modal(document.getElementById('translationModal'), {
        backdrop: 'static',  // 设置为static可以防止点击背景关闭模态框
        keyboard: true       // 允许按ESC键关闭
    });
    translationModal.show();
    
    // 加载翻译内容
    $.ajax({
        url: '/admin/emailMessages/getTranslationContent',
        type: 'GET',
        dataType: 'json',
        data: { emailId: originalEmailId },
        success: function(res) {
            if (res.state === 'ok' && res.data) {
                // 处理并显示翻译文本
                const translationContent = res.data.content || '';
                if (translationContent) {
                    // 格式化翻译内容
                    const formattedContent = formatTranslationContent(translationContent);
                    $('#translationContent').html(formattedContent);
                } else {
                    $('#translationContent').html('<div class="alert alert-info">没有翻译内容</div>');
                }
                
                // 显示翻译截图
                if (res.data.screenshots && res.data.screenshots.length > 0) {
                    let screenshotHtml = '<div class="row">';
                    res.data.screenshots.forEach(function(screenshot) {
                        screenshotHtml += `
                            <div class="col-md-6 mb-3">
                                <div class="card">
                                    <img src="common/file?filePath=${screenshot.path}" class="card-img-top" alt="截图">
                                    <div class="card-body p-2">
                                        <small class="text-muted">${screenshot.name || '截图'}</small>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    screenshotHtml += '</div>';
                    $('#translationScreenshot').html(screenshotHtml);
                } else {
                    $('#translationScreenshot').html('<div class="alert alert-info">没有翻译截图</div>');
                }
            } else {
                // 显示错误信息
                $('#translationContent').html('<div class="alert alert-warning">获取翻译内容失败: ' + (res.msg || '未知错误') + '</div>');
                $('#translationScreenshot').html('<div class="alert alert-warning">获取翻译截图失败</div>');
            }
        },
        error: function() {
            // 显示错误信息
            $('#translationContent').html('<div class="alert alert-danger">网络错误，无法获取翻译内容</div>');
            $('#translationScreenshot').html('<div class="alert alert-danger">网络错误，无法获取翻译截图</div>');
        }
    });
}

// 格式化翻译内容
function formatTranslationContent(content) {
    if (!content) return '';
    
    // 检查是否已经是HTML格式（包含HTML标签）
    const htmlTagRegex = /<[a-z][\s\S]*>/i;
    if (htmlTagRegex.test(content)) {
        // 如果已经是HTML，只做基本的清理
        return content
            .replace(/\r\n/g, '\n')  // 统一换行符
            .replace(/\r/g, '\n')    // 统一换行符
            .trim();
    }
    
    // 对纯文本进行HTML转义
    let formattedContent = content
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
    
    // 统一换行符处理
    formattedContent = formattedContent
        .replace(/\r\n/g, '\n')  // Windows换行符转为Unix
        .replace(/\r/g, '\n');   // Mac换行符转为Unix
    
    // 处理制表符
    formattedContent = formattedContent.replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
    
    // 处理多个连续空格（保留缩进和对齐）
    formattedContent = formattedContent
        // 处理行首的空格（缩进）
        .replace(/^( +)/gm, function(match) {
            return '&nbsp;'.repeat(match.length);
        })
        // 处理行中的多个连续空格
        .replace(/ {2,}/g, function(match) {
            return '&nbsp;'.repeat(match.length);
        });
    
    // 分割成行进行处理
    const lines = formattedContent.split('\n');
    const processedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        
        // 跳过空行，但保留它们用于段落分隔
        if (line === '') {
            processedLines.push('');
            continue;
        }
        
        // 检查是否是列表项
        if (/^\d+\.\s+/.test(line)) {
            // 数字列表
            processedLines.push('<li>' + line.replace(/^\d+\.\s+/, '') + '</li>');
        } else if (/^[-*•]\s+/.test(line)) {
            // 无序列表
            processedLines.push('<li>' + line.replace(/^[-*•]\s+/, '') + '</li>');
        } else if (/^>\s*/.test(line)) {
            // 引用格式
            processedLines.push('<blockquote>' + line.replace(/^>\s*/, '') + '</blockquote>');
        } else {
            // 普通文本行
            processedLines.push(line);
        }
    }
    
    // 重新组合内容
    let result = '';
    let inOrderedList = false;
    let inUnorderedList = false;
    let currentParagraph = '';
    
    for (let i = 0; i < processedLines.length; i++) {
        const line = processedLines[i];
        
        if (line === '') {
            // 空行，结束当前段落
            if (currentParagraph) {
                result += '<p>' + currentParagraph + '</p>';
                currentParagraph = '';
            }
            // 关闭列表
            if (inOrderedList) {
                result += '</ol>';
                inOrderedList = false;
            }
            if (inUnorderedList) {
                result += '</ul>';
                inUnorderedList = false;
            }
        } else if (line.startsWith('<li>')) {
            // 列表项
            if (currentParagraph) {
                result += '<p>' + currentParagraph + '</p>';
                currentParagraph = '';
            }
            
            // 检查是否需要开始新的列表
            const nextLine = i + 1 < processedLines.length ? processedLines[i + 1] : '';
            const isOrderedList = /^\d+\.\s+/.test(lines[i]);
            
            if (isOrderedList && !inOrderedList) {
                if (inUnorderedList) {
                    result += '</ul>';
                    inUnorderedList = false;
                }
                result += '<ol>';
                inOrderedList = true;
            } else if (!isOrderedList && !inUnorderedList) {
                if (inOrderedList) {
                    result += '</ol>';
                    inOrderedList = false;
                }
                result += '<ul>';
                inUnorderedList = true;
            }
            
            result += line;
        } else if (line.startsWith('<blockquote>')) {
            // 引用
            if (currentParagraph) {
                result += '<p>' + currentParagraph + '</p>';
                currentParagraph = '';
            }
            if (inOrderedList) {
                result += '</ol>';
                inOrderedList = false;
            }
            if (inUnorderedList) {
                result += '</ul>';
                inUnorderedList = false;
            }
            result += line;
        } else {
            // 普通文本，累积到当前段落
            if (inOrderedList) {
                result += '</ol>';
                inOrderedList = false;
            }
            if (inUnorderedList) {
                result += '</ul>';
                inUnorderedList = false;
            }
            
            if (currentParagraph) {
                currentParagraph += '<br>' + line;
            } else {
                currentParagraph = line;
            }
        }
    }
    
    // 处理最后的内容
    if (currentParagraph) {
        result += '<p>' + currentParagraph + '</p>';
    }
    if (inOrderedList) {
        result += '</ol>';
    }
    if (inUnorderedList) {
        result += '</ul>';
    }
    
    // 清理多余的空标签和格式
    result = result
        .replace(/<p>\s*<\/p>/g, '')  // 移除空段落
        .replace(/<br>\s*<\/p>/g, '</p>')  // 清理段落末尾的br
        .replace(/<p>\s*<br>/g, '<p>')  // 清理段落开头的br
        .replace(/(<\/blockquote>)\s*(<blockquote>)/g, '$1$2')  // 合并连续的引用
        .trim();
    
    return result || '<p>' + content + '</p>';  // 如果处理失败，返回原始内容包装在段落中
}

// 插入翻译内容
function insertTranslationContent() {
    // 获取翻译内容
    const translationContent = $('#translationContent').html();
    
    if (!translationContent || translationContent.includes('没有翻译内容') || translationContent.includes('获取翻译内容失败')) {
        layer.msg('没有可用的翻译内容', {icon: 2});
        return;
    }
    
    // 获取编辑器实例
    const editor = tinymce.get('emailContent');
    if (!editor) {
        layer.msg('编辑器未初始化', {icon: 2});
        return;
    }
    
    // 构建要插入的内容
    const insertContent = `
        <div style="margin: 10px 0; padding: 10px; border-left: 3px solid #007bff; background-color: #f8f9fa;">
            <p><strong>翻译内容：</strong></p>
            <div style="margin-top: 10px;">
                ${translationContent}
            </div>
        </div>
        <p><br></p>
    `;
    
    // 在光标位置插入翻译内容，或添加到顶部
    editor.execCommand('mceInsertContent', false, insertContent);
    
    // 关闭模态框
    const translationModal = bootstrap.Modal.getInstance(document.getElementById('translationModal'));
    if (translationModal) {
        translationModal.hide();
        // 手动移除遮罩层
        removeBackdrop();
    }
    
    // 提示成功
    layer.msg('已插入翻译内容', {icon: 1});
}

// 插入翻译主题
function insertTranslationSubject() {
    // 获取原始邮件ID
    const originalEmailId = $('input[name="originalEmailId"]').val();
    if (!originalEmailId) {
        layer.msg('没有原始邮件信息', {icon: 2});
        return;
    }

    // 显示加载提示
    const loadingIndex = layer.load(1, {
        shade: [0.3, '#000']
    });

    // 首先获取原始邮件信息（包含发件人信息）
    $.ajax({
        url: '/admin/emailMessages/getEmailDetail',
        type: 'GET',
        dataType: 'json',
        data: { emailId: originalEmailId },
        success: function(emailRes) {
            if (emailRes.state === 'ok' && emailRes.data) {
                const originalEmail = emailRes.data;

                // 获取翻译内容
                $.ajax({
                    url: '/admin/emailMessages/getTranslationContent',
                    type: 'GET',
                    dataType: 'json',
                    data: { emailId: originalEmailId },
                    success: function(res) {
                        layer.close(loadingIndex);

                        if (res.state === 'ok' && res.data && res.data.content) {
                            const translationContent = res.data.content;

                            // 使用正则表达式提取主题
                            const subject = extractSubjectFromTranslation(translationContent);

                            if (subject) {
                                // 获取当前主题输入框的值
                                const currentSubject = $('#subject').val();

                                // 构建带有发件人信息的主题
                                const fromDisplayName = originalEmail.fromDisplay;
                                const fromPrefix = fromDisplayName ? `来自"${fromDisplayName}"` : '';

                                // 构建完整的新主题
                                let newSubject = '';
                                if (fromPrefix) {
                                    // 修正主题构建逻辑，避免重复当前主题
                                    newSubject = `${fromPrefix} ${subject}`;
                                } else {
                                    newSubject = subject;
                                }

                                // 检查是否已经包含类似的主题内容
                                if (currentSubject && currentSubject.includes(subject) && currentSubject.includes(fromPrefix)) {
                                    layer.msg('主题中已包含相关内容', {icon: 3});
                                    return;
                                }

                                // 如果当前有主题，询问是否替换
                                if (currentSubject && currentSubject.trim()) {
                                    layer.confirm(`当前主题：${currentSubject}<br/>新主题：${newSubject}<br/>是否替换？`, {
                                        icon: 3,
                                        title: '确认替换主题'
                                    }, function(index) {
                                        $('#subject').val(newSubject);
                                        layer.close(index);
                                        layer.msg('已插入翻译主题: ' + newSubject, {icon: 1});
                                    });
                                } else {
                                    // 如果当前没有主题，直接设置
                                    $('#subject').val(newSubject);
                                    layer.msg('已插入翻译主题: ' + newSubject, {icon: 1});
                                }
                            } else {
                                layer.msg('未能从翻译内容中提取到主题', {icon: 2});
                            }
                        } else {
                            layer.msg('获取翻译内容失败: ' + (res.msg || '未知错误'), {icon: 2});
                        }
                    },
                    error: function() {
                        layer.close(loadingIndex);
                        layer.msg('网络错误，无法获取翻译内容', {icon: 2});
                    }
                });
            } else {
                layer.close(loadingIndex);
                layer.msg('获取邮件信息失败: ' + (emailRes.msg || '未知错误'), {icon: 2});
            }
        },
        error: function() {
            layer.close(loadingIndex);
            layer.msg('网络错误，无法获取邮件信息', {icon: 2});
        }
    });
}

// 从翻译内容中提取主题
function extractSubjectFromTranslation(content) {
    if (!content) return null;

    // 定义多种可能的主题模式
    const patterns = [
        // 邮件主题：xxx
        /邮件主题[：:]\s*(.+?)(?:\n|$|<br|<\/p>|<\/div>)/i,
        // 主题：xxx
        /主题[：:]\s*(.+?)(?:\n|$|<br|<\/p>|<\/div>)/i,
        // Subject: xxx
        /Subject[：:]\s*(.+?)(?:\n|$|<br|<\/p>|<\/div>)/i,
        // 标题：xxx
        /标题[：:]\s*(.+?)(?:\n|$|<br|<\/p>|<\/div>)/i,
        // 邮件标题：xxx
        /邮件标题[：:]\s*(.+?)(?:\n|$|<br|<\/p>|<\/div>)/i
    ];

    for (const pattern of patterns) {
        const match = content.match(pattern);
        if (match && match[1]) {
            let subject = match[1].trim();

            // 清理HTML标签
            subject = subject.replace(/<[^>]*>/g, '');

            // 清理HTML实体
            subject = subject
                .replace(/&nbsp;/g, ' ')
                .replace(/&amp;/g, '&')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&quot;/g, '"')
                .replace(/&#39;/g, "'");

            // 去除多余的空格
            subject = subject.replace(/\s+/g, ' ').trim();

            // 如果主题不为空，返回它（移除长度限制，避免截断长主题）
            if (subject && subject.length > 0) {
                // 如果主题过长，可以适当截断但保留更多内容
                if (subject.length > 500) {
                    subject = subject.substring(0, 500) + '...';
                }
                return subject;
            }
        }
    }

    return null;
}

// 合并文件列表函数
function mergeFileList(existingFiles, newFiles) {
    // 创建一个新的DataTransfer对象来构建合并后的文件列表
    const dt = new DataTransfer();
    
    // 添加已有的文件
    if (existingFiles) {
        for (let i = 0; i < existingFiles.length; i++) {
            const file = existingFiles[i];
            dt.items.add(file);
        }
    }
    
    // 添加新文件，检查重复
    for (let i = 0; i < newFiles.length; i++) {
        const newFile = newFiles[i];
        let isDuplicate = false;
        
        // 检查是否已存在同名文件
        if (existingFiles) {
            for (let j = 0; j < existingFiles.length; j++) {
                const existingFile = existingFiles[j];
                if (existingFile.name === newFile.name && existingFile.size === newFile.size) {
                    isDuplicate = true;
                    break;
                }
            }
        }
        
        // 如果不是重复文件，则添加
        if (!isDuplicate) {
            dt.items.add(newFile);
        } else {
            console.log('跳过重复文件:', newFile.name);
        }
    }
    
    return dt.files;
}

// 从文件输入框中移除指定的文件
function removeFileFromInput(fileName, fileIndex) {
    const fileInput = document.getElementById('attachmentInput');
    if (!fileInput || !fileInput.files) return;
    
    // 创建一个新的DataTransfer对象
    const dt = new DataTransfer();
    
    // 添加除了要删除的文件之外的所有文件
    for (let i = 0; i < fileInput.files.length; i++) {
        const file = fileInput.files[i];
        // 跳过要删除的文件（通过文件名和索引双重验证）
        if (!(file.name === fileName && i === fileIndex)) {
            dt.items.add(file);
        }
    }
    
    // 更新文件输入框
    fileInput.files = dt.files;
    
    console.log('已删除文件:', fileName);
}

// 初始化拖拽上传功能
function initDragDropUpload() {
    const dragDropZone = document.getElementById('dragDropZone');
    const fileInput = document.getElementById('attachmentInput');
    
    if (!dragDropZone || !fileInput) {
        console.error('拖拽上传元素未找到');
        return;
    }
    
    // 防止重复绑定
    $('#dragDropZone').off('click.dragdrop');
    $('#clickToUpload').off('click.dragdrop');
    
    // 使用命名空间绑定点击事件，防止重复绑定
    $('#dragDropZone').on('click.dragdrop', function(e) {
        // 只处理直接点击到拖拽区域的事件，避免子元素冒泡
        if (e.target === this || $(e.target).hasClass('drag-drop-content') || $(e.target).closest('.drag-drop-content').length) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            
            console.log('点击拖拽区域，触发文件选择');
            triggerFileSelect();
        }
    });
    
    // 点击"点击选择文件"链接
    $('#clickToUpload').on('click.dragdrop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        console.log('点击选择文件链接，触发文件选择');
        triggerFileSelect();
    });
    
    // 统一的文件选择触发函数
    function triggerFileSelect() {
        const input = document.getElementById('attachmentInput');
        if (input) {
            try {
                input.click();
            } catch (e) {
                console.error('触发文件选择失败:', e);
                // 备用方法：创建一个新的input元素
                const newInput = document.createElement('input');
                newInput.type = 'file';
                newInput.multiple = true;
                newInput.style.display = 'none';
                document.body.appendChild(newInput);
                newInput.click();
                
                newInput.onchange = function() {
                    // 将选择的文件转移到原始input
                    input.files = newInput.files;
                    const event = new Event('change', { bubbles: true });
                    input.dispatchEvent(event);
                    document.body.removeChild(newInput);
                };
            }
        }
    }
    
    // 拖拽进入
    dragDropZone.addEventListener('dragenter', function(e) {
        e.preventDefault();
        e.stopPropagation();
        dragDropZone.classList.add('dragover');
    });
    
    // 拖拽经过
    dragDropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        dragDropZone.classList.add('dragover');
    });
    
    // 拖拽离开
    dragDropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // 检查是否真的离开了拖拽区域
        const rect = dragDropZone.getBoundingClientRect();
        const x = e.clientX;
        const y = e.clientY;
        
        if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
            dragDropZone.classList.remove('dragover');
        }
    });
    
    // 文件拖放
    dragDropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        dragDropZone.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const originalCount = fileInput.files ? fileInput.files.length : 0;
            
            // 合并新文件和已有文件
            const mergedFiles = mergeFileList(fileInput.files, files);
            
            // 将合并后的文件设置到文件输入框
            fileInput.files = mergedFiles;
            
            // 触发change事件，让现有的文件处理逻辑生效
            const event = new Event('change', { bubbles: true });
            fileInput.dispatchEvent(event);
            
            // 计算实际添加的文件数量
            const actualAdded = mergedFiles.length - originalCount;
            const skipped = files.length - actualAdded;
            
            // 显示成功提示
            if (skipped > 0) {
                layer.msg(`已添加 ${actualAdded} 个文件，跳过 ${skipped} 个重复文件`, {icon: 1, time: 3000});
            } else {
                layer.msg(`已添加 ${actualAdded} 个文件`, {icon: 1, time: 2000});
            }
        }
    });
    
    // 防止页面默认的拖拽行为
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        document.addEventListener(eventName, function(e) {
            e.preventDefault();
            e.stopPropagation();
        });
    });
    
    // 当页面被拖拽时，高亮显示拖拽区域
    let dragCounter = 0;
    
    document.addEventListener('dragenter', function(e) {
        dragCounter++;
        if (e.dataTransfer.types.includes('Files')) {
            dragDropZone.style.borderColor = '#007bff';
            dragDropZone.style.backgroundColor = '#e7f3ff';
        }
    });
    
    document.addEventListener('dragleave', function(e) {
        dragCounter--;
        if (dragCounter <= 0) {
            dragCounter = 0;
            dragDropZone.style.borderColor = '';
            dragDropZone.style.backgroundColor = '';
        }
    });
    
    document.addEventListener('drop', function(e) {
        dragCounter = 0;
        dragDropZone.style.borderColor = '';
        dragDropZone.style.backgroundColor = '';
    });
    
    // 添加调试信息
    console.log('拖拽上传功能已初始化');
    console.log('文件输入框元素:', fileInput);
    console.log('文件输入框是否可见:', fileInput ? window.getComputedStyle(fileInput).display !== 'none' : 'N/A');

    // 添加一个测试按钮用于调试（临时）
    const testButton = $('<button type="button" class="btn btn-sm btn-outline-info mt-2" id="testFileInput">测试文件选择</button>');
    $('#dragDropZone').after(testButton);

    $('#testFileInput').on('click', function() {
        console.log('测试按钮点击，尝试触发文件选择');
        const input = document.getElementById('attachmentInput');
        if (input) {
            console.log('输入框存在，尝试点击');
            input.click();
        } else {
            console.log('输入框不存在');
        }
    });
}

// AI功能相关函数

// 显示AI对话框
function showAiDialog() {
    console.log('显示AI对话框');

    // 清理之前的遮罩层
    removeBackdrop();

    // 加载LLM提供商
    loadLlmProviders();

    // 加载AI选项
    loadAiOptions();

    // 清空自定义提示语
    $('#aiPrompt').val('');

    // 显示模态框
    const aiModal = new bootstrap.Modal(document.getElementById('aiDialog'), {
        backdrop: 'static',
        keyboard: true
    });
    aiModal.show();
}

// 加载AI选项
function loadAiOptions() {
    console.log('加载AI选项');

    // 预定义的AI功能选项
    const aiOptions = [
        { value: 'write', text: '根据提示词写邮件', description: '基于您的提示词和原邮件内容生成新的邮件' },
        { value: 'reply', text: '智能回复', description: '基于原邮件内容生成专业的回复' },
        { value: 'rewrite', text: '改写邮件', description: '改写当前邮件内容，使其更专业得体' },
        { value: 'continue', text: '续写邮件', description: '基于当前内容继续写下去' },
        { value: 'translate', text: '翻译邮件', description: '将邮件内容翻译成其他语言' },
        { value: 'summarize', text: '总结要点', description: '提取邮件的关键信息和要点' }
    ];

    // 清空选项
    $('#aiOptions').empty();

    // 添加默认选项
    $('#aiOptions').append('<option value="">请选择AI功能</option>');

    // 添加AI选项
    aiOptions.forEach(function(option) {
        $('#aiOptions').append(`<option value="${option.value}" title="${option.description}">${option.text}</option>`);
    });

    console.log('AI选项加载完成');
}

// 加载LLM提供商
function loadLlmProviders() {
    console.log('加载LLM提供商');

    $.ajax({
        url: '/admin/emailMessages/getLlmProviders',
        type: 'GET',
        success: function(res) {
            if (res.state === 'ok' && res.data) {
                // 清空提供商选项
                $('#llmProvider').empty();
                $('#llmProvider').append('<option value="">请选择AI提供商</option>');

                // 添加提供商选项
                res.data.forEach(function(provider) {
                    $('#llmProvider').append(`<option value="${provider.name}" data-models='${JSON.stringify(provider.modelList || [])}'>${provider.name} - ${provider.description || ''}</option>`);
                });

                // 默认选择第一个提供商
                if (res.data.length > 0) {
                    $('#llmProvider').val(res.data[0].name);
                    loadLlmModels();
                }

                console.log('LLM提供商加载完成');
            } else {
                console.error('加载LLM提供商失败:', res.msg);
                layer.msg('加载AI提供商失败', {icon: 2});
            }
        },
        error: function() {
            console.error('加载LLM提供商请求失败');
            layer.msg('加载AI提供商失败', {icon: 2});
        }
    });
}

// 加载LLM模型
function loadLlmModels() {
    const selectedProvider = $('#llmProvider').find('option:selected');
    const models = selectedProvider.data('models') || [];

    // 清空模型选项
    $('#llmModel').empty();

    if (models.length === 0) {
        $('#llmModel').append('<option value="">该提供商暂无可用模型</option>');
        return;
    }

    $('#llmModel').append('<option value="">请选择AI模型</option>');

    // 添加模型选项
    models.forEach(function(model) {
        $('#llmModel').append(`<option value="${model.identifier}">${model.name}</option>`);
    });

    // 默认选择第一个模型
    if (models.length > 0) {
        $('#llmModel').val(models[0].identifier);
    }

    console.log('LLM模型加载完成');
}

// 处理AI请求
function processAiRequest() {
    const selectedProvider = $('#llmProvider').val();
    const selectedModel = $('#llmModel').val();
    const selectedOption = $('#aiOptions').val();
    const customPrompt = $('#aiPrompt').val().trim();

    if (!selectedProvider) {
        layer.msg('请选择AI提供商', {icon: 2});
        return;
    }

    if (!selectedModel) {
        layer.msg('请选择AI模型', {icon: 2});
        return;
    }

    if (!selectedOption) {
        layer.msg('请选择AI功能', {icon: 2});
        return;
    }

    console.log('处理AI请求:', selectedProvider, selectedModel, selectedOption, customPrompt);

    // 获取当前邮件内容
    const editor = tinymce.get('emailContent');
    const currentContent = editor ? editor.getContent() : '';

    // 获取原邮件内容
    const originalEmailId = $('input[name="originalEmailId"]').val();
    let originalContent = '';
    if (originalEmailId && $('#originalEmailContent').length > 0) {
        originalContent = $('#originalEmailContent .card-body').text() || '';
    }

    // 根据选择的功能构建内容
    let contentToProcess = '';
    let actionPrompt = '';

    switch (selectedOption) {
        case 'write':
            actionPrompt = customPrompt || '请根据以下信息写一封专业的邮件';
            contentToProcess = originalContent || '请写一封邮件';
            break;
        case 'reply':
            actionPrompt = '请根据以下邮件内容生成一个专业的回复' + (customPrompt ? '，要求：' + customPrompt : '');
            contentToProcess = originalContent || currentContent;
            break;
        case 'rewrite':
            actionPrompt = '请改写以下邮件内容，使其更加专业得体' + (customPrompt ? '，要求：' + customPrompt : '');
            contentToProcess = currentContent || originalContent;
            break;
        case 'continue':
            actionPrompt = '请基于以下内容继续写下去，保持语气和风格的一致性' + (customPrompt ? '，要求：' + customPrompt : '');
            contentToProcess = currentContent || originalContent;
            break;
        case 'translate':
            actionPrompt = '请将以下内容翻译成' + (customPrompt || '中文');
            contentToProcess = currentContent || originalContent;
            break;
        case 'summarize':
            actionPrompt = '请总结以下邮件的关键信息和要点' + (customPrompt ? '，要求：' + customPrompt : '');
            contentToProcess = originalContent || currentContent;
            break;
        default:
            layer.msg('未知的AI功能', {icon: 2});
            return;
    }

    if (!contentToProcess.trim()) {
        layer.msg('没有可处理的内容', {icon: 2});
        return;
    }

    // 显示进度条
    $('#aiProgress').show();

    // 发送AI请求
    const requestData = {
        action: selectedOption,
        option: selectedOption,
        content: contentToProcess,
        prompt: actionPrompt,
        llmProvider: selectedProvider,
        llmModel: selectedModel
    };

    // 如果有原邮件ID，也传递给后端
    if (originalEmailId) {
        requestData.originalEmailId = originalEmailId;
    }

    $.ajax({
        url: '/admin/emailMessages/aiProcess',
        type: 'POST',
        data: requestData,
        success: function(res) {
            $('#aiProgress').hide();

            if (res.state === 'ok' && res.data) {
                // 显示AI结果
                showAiResult(res.data, selectedOption);

                // 关闭AI对话框
                const aiModal = bootstrap.Modal.getInstance(document.getElementById('aiDialog'));
                if (aiModal) {
                    aiModal.hide();
                }
            } else {
                layer.msg('AI处理失败: ' + (res.msg || '未知错误'), {icon: 2});
            }
        },
        error: function(xhr, status, error) {
            $('#aiProgress').hide();
            console.error('AI请求失败:', error);
            layer.msg('AI请求失败，请稍后重试', {icon: 2});
        }
    });
}

// 显示AI结果
function showAiResult(result, actionType) {
    console.log('显示AI结果:', actionType, result);

    // 清理之前的遮罩层
    removeBackdrop();

    // 设置结果内容
    $('#aiResultContent').html(formatAiResult(result, actionType));

    // 显示结果模态框
    const aiResultModal = new bootstrap.Modal(document.getElementById('aiResultDialog'), {
        backdrop: 'static',
        keyboard: true
    });
    aiResultModal.show();
}

// 格式化AI结果
function formatAiResult(result, actionType) {
    if (!result) return '<p class="text-muted">没有生成内容</p>';

    // 根据不同的操作类型进行格式化
    let formattedResult = result;

    // 如果结果包含HTML标签，直接使用
    if (/<[a-z][\s\S]*>/i.test(result)) {
        formattedResult = result;
    } else {
        // 纯文本格式化
        formattedResult = result
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>');

        // 包装在段落中
        if (!formattedResult.startsWith('<p>')) {
            formattedResult = '<p>' + formattedResult + '</p>';
        }
    }

    return `
        <div class="ai-result-content">
            <div class="alert alert-info">
                <i class="fa fa-robot"></i> AI生成的内容：
            </div>
            <div class="ai-generated-content" style="border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; background-color: #f8f9fa;">
                ${formattedResult}
            </div>
        </div>
    `;
}

// 替换编辑器内容
function replaceEditorContent() {
    const aiContent = extractAiContent();
    if (!aiContent) {
        layer.msg('没有可用的AI内容', {icon: 2});
        return;
    }

    const editor = tinymce.get('emailContent');
    if (!editor) {
        layer.msg('编辑器未初始化', {icon: 2});
        return;
    }

    // 确认替换
    layer.confirm('确定要替换当前邮件内容吗？此操作不可撤销。', {
        icon: 3,
        title: '确认替换'
    }, function(index) {
        editor.setContent(aiContent);
        layer.close(index);
        closeAiResultDialog();
        layer.msg('已替换邮件内容', {icon: 1});
        markContentAsModified();
    });
}

// 插入到光标位置
function insertAtCursor() {
    const aiContent = extractAiContent();
    if (!aiContent) {
        layer.msg('没有可用的AI内容', {icon: 2});
        return;
    }

    const editor = tinymce.get('emailContent');
    if (!editor) {
        layer.msg('编辑器未初始化', {icon: 2});
        return;
    }

    // 在光标位置插入内容
    editor.execCommand('mceInsertContent', false, aiContent);
    closeAiResultDialog();
    layer.msg('已插入AI内容', {icon: 1});
    markContentAsModified();
}

// 追加到末尾
function appendToEditor() {
    const aiContent = extractAiContent();
    if (!aiContent) {
        layer.msg('没有可用的AI内容', {icon: 2});
        return;
    }

    const editor = tinymce.get('emailContent');
    if (!editor) {
        layer.msg('编辑器未初始化', {icon: 2});
        return;
    }

    // 获取当前内容
    const currentContent = editor.getContent();

    // 添加分隔符和新内容
    const separator = currentContent.trim() ? '<br><br>' : '';
    const newContent = currentContent + separator + aiContent;

    editor.setContent(newContent);
    closeAiResultDialog();
    layer.msg('已追加AI内容', {icon: 1});
    markContentAsModified();
}

// 提取AI生成的内容
function extractAiContent() {
    const aiGeneratedDiv = document.querySelector('.ai-generated-content');
    if (!aiGeneratedDiv) {
        return null;
    }

    return aiGeneratedDiv.innerHTML;
}

// 关闭AI结果对话框
function closeAiResultDialog() {
    const aiResultModal = bootstrap.Modal.getInstance(document.getElementById('aiResultDialog'));
    if (aiResultModal) {
        aiResultModal.hide();
    }
    removeBackdrop();
}
</script>
#end