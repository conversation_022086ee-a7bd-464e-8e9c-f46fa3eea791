<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TinyMCE 颜色功能测试</title>
    <script src="/assets/libs/tinymce/tinymce.min.js"></script>
</head>
<body>
    <div style="padding: 20px;">
        <h1>TinyMCE 颜色功能测试</h1>
        <p>这个页面用于测试TinyMCE编辑器的字体颜色和背景颜色功能。</p>
        
        <form>
            <div style="margin-bottom: 20px;">
                <label for="testEditor">测试编辑器：</label>
                <textarea id="testEditor" name="content">
                    <p>这是一段测试文本。请选择文字并使用工具栏中的颜色按钮来测试字体颜色和背景颜色功能。</p>
                    <p>工具栏中应该包含：</p>
                    <ul>
                        <li>forecolor - 字体颜色按钮</li>
                        <li>backcolor - 背景颜色按钮</li>
                    </ul>
                </textarea>
            </div>
        </form>
    </div>

    <script>
        tinymce.init({
            selector: '#testEditor',
            height: 400,
            menubar: false,
            plugins: 'link image code table lists emoticons',
            toolbar: 'undo redo | bold italic | forecolor backcolor | alignleft aligncenter alignright | code | table | numlist bullist | link image | emoticons',
            // 颜色配置
            color_map: [
                "000000", "黑色",
                "993300", "深红色", 
                "333300", "深黄色",
                "003300", "深绿色",
                "003366", "深青色",
                "000080", "深蓝色",
                "333399", "靛蓝色",
                "333333", "深灰色",
                "800000", "栗色",
                "FF6600", "橙色",
                "808000", "橄榄色",
                "008000", "绿色",
                "008080", "青色",
                "0000FF", "蓝色",
                "666699", "灰蓝色",
                "808080", "灰色",
                "FF0000", "红色",
                "FF9900", "琥珀色",
                "99CC00", "黄绿色",
                "339966", "海绿色",
                "33CCCC", "浅青色",
                "3366FF", "浅蓝色",
                "800080", "紫色",
                "999999", "中灰色",
                "FF00FF", "洋红色",
                "FFCC00", "金色",
                "FFFF00", "黄色",
                "00FF00", "酸橙色",
                "00FFFF", "水蓝色",
                "00CCFF", "天蓝色",
                "993366", "红紫色",
                "C0C0C0", "银色",
                "FF99CC", "粉红色",
                "FFCC99", "桃色",
                "FFFF99", "浅黄色",
                "CCFFCC", "浅绿色",
                "CCFFFF", "浅青色",
                "99CCFF", "浅蓝色",
                "CC99FF", "淡紫色",
                "FFFFFF", "白色"
            ],
            language: 'zh_CN',
            base_url: '/assets/libs/tinymce'
        });
    </script>
</body>
</html>
