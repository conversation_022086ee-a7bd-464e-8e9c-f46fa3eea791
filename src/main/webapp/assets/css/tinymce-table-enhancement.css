/* TinyMCE表格增强样式 */

/* 编辑器内表格样式 */
.tox-edit-area__iframe table {
    border-collapse: collapse !important;
    width: 100% !important;
    table-layout: auto !important;
    margin: 10px 0 !important;
}

.tox-edit-area__iframe table td,
.tox-edit-area__iframe table th {
    border: 1px solid #ddd !important;
    padding: 8px !important;
    vertical-align: top !important;
    word-wrap: break-word !important;
    position: relative !important;
}

.tox-edit-area__iframe table th {
    background-color: #f5f5f5 !important;
    font-weight: bold !important;
    text-align: center !important;
}

/* 表格调整手柄样式 */
.tox-edit-area__iframe table td:hover,
.tox-edit-area__iframe table th:hover {
    background-color: rgba(0, 123, 255, 0.1) !important;
}

/* 表格选中状态 */
.tox-edit-area__iframe table.mce-item-selected {
    outline: 2px solid #007bff !important;
}

.tox-edit-area__iframe table td.mce-item-selected,
.tox-edit-area__iframe table th.mce-item-selected {
    background-color: rgba(0, 123, 255, 0.2) !important;
}

/* 表格工具栏样式优化 */
.tox-toolbar__group .tox-tbtn--select {
    min-width: 60px;
}

/* 表格属性对话框优化 */
.tox-dialog__body-content .tox-form__group {
    margin-bottom: 10px;
}

/* 响应式表格 */
@media (max-width: 768px) {
    .tox-edit-area__iframe table {
        font-size: 12px !important;
    }
    
    .tox-edit-area__iframe table td,
    .tox-edit-area__iframe table th {
        padding: 4px !important;
    }
}

/* 邮件预览中的表格样式 */
.email-preview table {
    border-collapse: collapse !important;
    width: 100% !important;
    max-width: 600px !important;
    margin: 10px auto !important;
    mso-table-lspace: 0pt !important;
    mso-table-rspace: 0pt !important;
    border-spacing: 0 !important;
}

.email-preview table td,
.email-preview table th {
    border: 1px solid #ddd !important;
    padding: 8px !important;
    vertical-align: top !important;
    word-wrap: break-word !important;
    word-break: break-all !important;
    mso-line-height-rule: exactly !important;
}

.email-preview table th {
    background-color: #f5f5f5 !important;
    font-weight: bold !important;
    text-align: center !important;
}

/* Outlook兼容性样式 */
.email-preview table {
    mso-cellspacing: 0 !important;
    mso-padding-alt: 0 !important;
}

/* 表格拖拽调整指示器 */
.tox-edit-area__iframe .mce-resize-bar {
    background-color: #007bff !important;
    opacity: 0.7 !important;
}

.tox-edit-area__iframe .mce-resize-bar:hover {
    opacity: 1 !important;
}

/* 表格边框调整 */
.tox-edit-area__iframe table[data-mce-selected] {
    outline: 2px solid #007bff !important;
    outline-offset: 1px !important;
}

/* 表格单元格内容对齐 */
.tox-edit-area__iframe table .text-left {
    text-align: left !important;
}

.tox-edit-area__iframe table .text-center {
    text-align: center !important;
}

.tox-edit-area__iframe table .text-right {
    text-align: right !important;
}

/* 表格行高亮 */
.tox-edit-area__iframe table tr:hover {
    background-color: rgba(0, 123, 255, 0.05) !important;
}

/* 表格工具提示 */
.table-resize-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    pointer-events: none;
}
