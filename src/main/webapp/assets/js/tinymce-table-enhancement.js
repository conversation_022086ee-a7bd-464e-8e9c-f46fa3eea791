/**
 * TinyMCE表格增强功能
 * 解决表格拖拽调整和邮件兼容性问题
 */

// 表格增强配置
const TableEnhancement = {
    // 初始化表格增强功能
    init: function(editor) {
        this.editor = editor;
        this.setupTableEvents();
        this.addCustomCommands();
        this.enhanceTableToolbar();
    },

    // 设置表格事件
    setupTableEvents: function() {
        const editor = this.editor;
        
        // 表格插入后的处理
        editor.on('NewBlock', function(e) {
            const element = e.newBlock;
            if (element && element.nodeName === 'TABLE') {
                TableEnhancement.optimizeTableForEditing(element);
            }
        });

        // 表格选中时的处理
        editor.on('NodeChange', function(e) {
            const element = e.element;
            if (element && (element.nodeName === 'TABLE' || element.closest('table'))) {
                TableEnhancement.highlightTable(element);
            }
        });

        // 表格内容变化时的处理
        editor.on('input', function(e) {
            const tables = editor.dom.select('table');
            tables.forEach(table => {
                TableEnhancement.maintainTableStructure(table);
            });
        });
    },

    // 优化表格以便编辑
    optimizeTableForEditing: function(table) {
        const editor = this.editor;
        
        // 设置表格基本属性
        editor.dom.setAttrib(table, 'contenteditable', 'true');
        editor.dom.setStyle(table, 'width', '100%');
        editor.dom.setStyle(table, 'border-collapse', 'collapse');
        editor.dom.setStyle(table, 'table-layout', 'auto');
        
        // 处理表格单元格
        const cells = editor.dom.select('td, th', table);
        cells.forEach(cell => {
            editor.dom.setStyle(cell, 'border', '1px solid #ddd');
            editor.dom.setStyle(cell, 'padding', '8px');
            editor.dom.setStyle(cell, 'vertical-align', 'top');
            editor.dom.setAttrib(cell, 'contenteditable', 'true');
        });
    },

    // 高亮显示表格
    highlightTable: function(element) {
        const editor = this.editor;
        const table = element.nodeName === 'TABLE' ? element : element.closest('table');
        
        if (table) {
            // 移除其他表格的高亮
            const allTables = editor.dom.select('table');
            allTables.forEach(t => {
                editor.dom.removeClass(t, 'mce-item-selected');
            });
            
            // 添加当前表格的高亮
            editor.dom.addClass(table, 'mce-item-selected');
        }
    },

    // 维护表格结构
    maintainTableStructure: function(table) {
        const editor = this.editor;
        
        // 确保所有单元格都有内容或至少有一个空格
        const cells = editor.dom.select('td, th', table);
        cells.forEach(cell => {
            if (!cell.textContent.trim() && !cell.querySelector('img, br')) {
                cell.innerHTML = '&nbsp;';
            }
        });
    },

    // 添加自定义命令
    addCustomCommands: function() {
        const editor = this.editor;
        
        // 添加表格样式命令
        editor.addCommand('mceTableAddClass', function(ui, value) {
            const table = editor.dom.getParent(editor.selection.getNode(), 'table');
            if (table) {
                editor.dom.addClass(table, value);
            }
        });

        // 添加表格优化命令
        editor.addCommand('mceOptimizeTable', function() {
            const table = editor.dom.getParent(editor.selection.getNode(), 'table');
            if (table) {
                TableEnhancement.optimizeTableForEmail(table);
            }
        });
    },

    // 增强表格工具栏
    enhanceTableToolbar: function() {
        const editor = this.editor;
        
        // 添加表格样式按钮
        editor.ui.registry.addMenuButton('tablestyles', {
            text: '表格样式',
            fetch: function(callback) {
                const items = [
                    {
                        type: 'menuitem',
                        text: '基础表格',
                        onAction: function() {
                            editor.execCommand('mceTableAddClass', false, 'basic-table');
                        }
                    },
                    {
                        type: 'menuitem',
                        text: '边框表格',
                        onAction: function() {
                            editor.execCommand('mceTableAddClass', false, 'bordered-table');
                        }
                    },
                    {
                        type: 'menuitem',
                        text: '条纹表格',
                        onAction: function() {
                            editor.execCommand('mceTableAddClass', false, 'striped-table');
                        }
                    }
                ];
                callback(items);
            }
        });

        // 添加表格优化按钮
        editor.ui.registry.addButton('optimizetable', {
            text: '优化表格',
            tooltip: '优化表格以便在邮件中正确显示',
            onAction: function() {
                editor.execCommand('mceOptimizeTable');
                editor.notificationManager.open({
                    text: '表格已优化，适合邮件发送',
                    type: 'success',
                    timeout: 3000
                });
            }
        });
    },

    // 优化表格以便在邮件中显示
    optimizeTableForEmail: function(table) {
        const editor = this.editor;
        
        // 设置邮件兼容的表格属性
        editor.dom.setAttrib(table, 'cellpadding', '0');
        editor.dom.setAttrib(table, 'cellspacing', '0');
        editor.dom.setAttrib(table, 'border', '0');
        editor.dom.setStyle(table, 'border-collapse', 'collapse');
        editor.dom.setStyle(table, 'mso-table-lspace', '0pt');
        editor.dom.setStyle(table, 'mso-table-rspace', '0pt');
        editor.dom.setStyle(table, 'border-spacing', '0');
        editor.dom.setStyle(table, 'width', '100%');
        editor.dom.setStyle(table, 'max-width', '600px');
        editor.dom.setStyle(table, 'margin', '0 auto');

        // 优化单元格
        const cells = editor.dom.select('td, th', table);
        cells.forEach(cell => {
            editor.dom.setStyle(cell, 'border', '1px solid #ddd');
            editor.dom.setStyle(cell, 'padding', '8px');
            editor.dom.setStyle(cell, 'vertical-align', 'top');
            editor.dom.setStyle(cell, 'word-wrap', 'break-word');
            editor.dom.setStyle(cell, 'word-break', 'break-all');
            editor.dom.setStyle(cell, 'mso-line-height-rule', 'exactly');
            
            // 移除可能导致问题的属性
            editor.dom.removeAttrib(cell, 'width');
            editor.dom.removeAttrib(cell, 'height');
            
            // 表头特殊处理
            if (cell.nodeName === 'TH') {
                editor.dom.setStyle(cell, 'background-color', '#f5f5f5');
                editor.dom.setStyle(cell, 'font-weight', 'bold');
                editor.dom.setStyle(cell, 'text-align', 'center');
            }
        });
    },

    // 获取表格HTML（邮件兼容版本）
    getEmailCompatibleTableHTML: function(table) {
        const clonedTable = table.cloneNode(true);
        this.optimizeTableForEmail(clonedTable);
        
        // 包装Outlook兼容的条件注释
        return `<!--[if mso]>
<table cellpadding="0" cellspacing="0" border="0" style="border-collapse: collapse; mso-table-lspace: 0pt; mso-table-rspace: 0pt;">
<![endif]-->
${clonedTable.outerHTML}
<!--[if mso]>
</table>
<![endif]-->`;
    }
};

// 自动初始化
if (typeof tinymce !== 'undefined') {
    tinymce.PluginManager.add('tableenhancement', function(editor) {
        TableEnhancement.init(editor);
        
        return {
            getMetadata: function() {
                return {
                    name: 'Table Enhancement',
                    url: 'https://example.com'
                };
            }
        };
    });
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TableEnhancement;
}
