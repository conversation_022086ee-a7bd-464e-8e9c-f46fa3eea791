{"version": 3, "sources": ["../../../js/i18n/defaults-cs_CZ.js"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAChB,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;AAC1C,IAAI,eAAe,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;AAC3C,IAAI,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAC7C,IAAI,cAAc,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;AAC5H,IAAI,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAC7B,IAAI,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACjC,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,EAAE,EAAE,CAAC;AACL,GAAG,MAAM,EAAE,CAAC", "file": "defaults-cs_CZ.js", "sourcesContent": ["(function ($) {\r\n  $.fn.selectpicker.defaults = {\r\n    noneSelectedText: '<PERSON><PERSON> v<PERSON>',\r\n    noneResultsText: '<PERSON><PERSON><PERSON><PERSON> vý<PERSON>ky {0}',\r\n    countSelectedText: '<PERSON><PERSON>č<PERSON> {0} z {1}',\r\n    maxOptionsText: ['Limit překro<PERSON>en ({n} {var} max)', 'Limit skupiny překro<PERSON> ({n} {var} max)', ['položek', 'položka']],\r\n    multipleSeparator: ', ',\r\n    selectAllText: 'V<PERSON><PERSON><PERSON> V<PERSON>',\r\n    deselectAllText: 'Odzna<PERSON>it Vše'\r\n  };\r\n})(jQuery);\r\n"]}