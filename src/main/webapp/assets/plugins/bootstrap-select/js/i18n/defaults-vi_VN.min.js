/*!
 * Bootstrap-select v1.13.9 (https://developer.snapappointments.com/bootstrap-select)
 *
 * Copyright 2012-2019 SnapAppointments, LLC
 * Licensed under MIT (https://github.com/snapappointments/bootstrap-select/blob/master/LICENSE)
 */

!function(e,n){void 0===e&&void 0!==window&&(e=window),"function"==typeof define&&define.amd?define(["jquery"],function(e){return n(e)}):"object"==typeof module&&module.exports?module.exports=n(require("jquery")):n(e.jQuery)}(this,function(e){e.fn.selectpicker.defaults={noneSelectedText:"Ch\u01b0a ch\u1ecdn",noneResultsText:"Kh\xf4ng c\xf3 k\u1ebft qu\u1ea3 cho {0}",countSelectedText:function(e,n){return"{0} m\u1ee5c \u0111\xe3 ch\u1ecdn"},maxOptionsText:function(e,n){return["Kh\xf4ng th\u1ec3 ch\u1ecdn (gi\u1edbi h\u1ea1n {n} m\u1ee5c)","Kh\xf4ng th\u1ec3 ch\u1ecdn (gi\u1edbi h\u1ea1n {n} m\u1ee5c)"]},selectAllText:"Ch\u1ecdn t\u1ea5t c\u1ea3",deselectAllText:"B\u1ecf ch\u1ecdn",multipleSeparator:", "}});