/* 地域选择样式 */
a.btntest{
    display: block;
    padding: 5px 10px;
    margin-bottom: 20px;
    background-color: #00b3ee;
}
.display-none{
    display: none;
}
.checkbtn a{
    font-size: 15px;
    color: #333;
    text-decoration: none;
    cursor: pointer;
}
.ri{
    float: right;
}
.clearfloat:after{display:block;clear:both;content:"";visibility:hidden;height:0}
.clearfloat{zoom:1}
.place-div>div{
    width: 100%;
    background: #f5f5f5;
    padding: 20px;
}
.place{
    display: inline-block;
    width:25%;
    height:30px;
    position: relative;
    box-sizing: border-box;
    border-top:2px solid #fff;
    border-left:2px solid #fff;
    border-right:2px solid #fff;
    padding-left:10px;
}
.place-active{
    border-top:2px solid #CAE4FF;
    border-left:2px solid #CAE4FF;
    border-right:2px solid #CAE4FF;
    background-color: #EDF6FF;
}
.place-div .placegroup{
    margin-left: 15px;
    width: 100%;
    background-color: #fff;
    padding: 20px;
    box-sizing: border-box;
    position: relative;
}
.place-div .checkbtn{
    background-color: #FBFBFB;
    margin: 0 10px 0 15px;
    width: 100%;
}
.place-div .checkbtn img{
    height: 10px;
    margin-left: 3px;
}
.place-div .checkbtn .allCheck img{
    height: 8px;
}
.place-div .checkbtn .ri{
    border-right: none;
}
.place-div .checkbtn a{
    border-right: 1px solid #ccc;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    width: 60px;
    text-align: center;
}
.place-div .bigplace label{
    display: inline-block;
    float: left;
    width: 100%;
    font-weight: bold;
    text-align: left;
    height:30px;
    cursor: pointer;
}
.place-div .bigplace input,.place-div .smallplace input{
    margin-top: 3px;
    margin-right: 5px;
    float: left;
    height: auto;
}
.place-div .smallplace{
    position: absolute;
    top:28px;
    left:-2px;
    width:300px;
    z-index: 2;
    display: none;
    border:2px solid #CAE4FF;
    background-color: #EDF6FF;
    padding-left:10px;
}
.place-div .smallplace .ratio{
    color: red;
}
.colorEDF6FF{
    background-color: #EDF6FF;
}
.place-div .smallplace label{
    padding-right: 10px;
    text-align: left;
    display: inline-block;
    float: left;
    cursor: pointer;
}
.place-div .smallplace .citys{
    width: auto;
    background-color: #fff;
    position: absolute;
    top: 30px;
    border: 1px solid #ccc;
    z-index: 100;
    display: none;
}
.place-div .smallplace .place-tooltips:hover .citys{
    display: block;
}
.place-div .smallplace .citys>i.jt{
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 10px solid #ccc;
    position: absolute;
    top: -10px;
    left: 20px;
}
.place-div .smallplace .citys>i.jt i{
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 10px solid #fff;
    position: absolute;
    top: 2px;
    left: -8px;
}
.place-div .smallplace .citys .row-div{
    min-width: 250px;
    padding: 10px;
    box-sizing: border-box;
}
.place-div .smallplace .citys .row-div label span{
    max-width: 175px;
    float: left;
}
.place-div .smallplace .citys .row-div p{
    float: left;
    min-width: 115px;
}
.place-div .smallplace p{
    float: left;
    width: auto;
    margin: 0;
    margin-bottom: 10px;
    margin-top: 5px;
}
.place-div .smallplace>div{
    float: left;
    width: auto;
    margin: 0;
    padding-bottom: 10px;
    padding-top: 5px;
    position: relative;
    font-size: 0.9em;
}

.show-place-div .smallplace label{
    width: auto;
}
.show-place-div{
    margin-left: 85px;
    font-size: 15px;
}
.show-place-div .bigplace input, .show-place-div .smallplace input{
    margin-left: 0;
}
.show-place-div .smallplace .citys .row-div p{
    margin: 5px 0 10px 0;
}