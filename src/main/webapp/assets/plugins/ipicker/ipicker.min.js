/*!
 * iPicker v4.0.2
 * Copyright (C) 2020-2021, ZG
 * Released under the MIT license.
 */
!((e,i)=>{typeof exports==="object"&&typeof module!=="undefined"?module.exports=i():typeof define==="function"&&define.amd?define(i):(e=e||self,e.iPicker=i())})(typeof window!=="undefined"?window:this,e=>{"use strict";const i={type:e=>Object.prototype.toString.call(e).slice(8,-1).toLowerCase(),isCorrectObject:e=>!!(i.type(e)==="object"&&Object.keys(e).length),isCorrectNumber:(e,i)=>!!(Number.isSafeInteger(e)&&(!i?e>0:e>=0)),isFunction:e=>i.type(e)==="function",uid(e){const i=Math.random().toString(36).substr(2,10);return e?Symbol(i):i},delayTimer(e=0){return new Promise(i=>{const t=window.setTimeout(()=>{window.clearTimeout(t);i()},e)})},mergeParam(e={},t){const r={};for(const s in t){const n=e[s];if(i.type(n)==="object"){r[s]=i.mergeParam(n,t[s])}else{r[s]=n===0?n:n||t[s]}}return r}};const t=(()=>{function e(e){const i=e[0]?e.length:0;this.length=i;for(let t=0;t<i;t++){this[t]=e[t]}return this}e.prototype={each(e){for(let i=0,t=this.length;i<t;i++){e.call(this[i],i,this[i])}return this},get(e=0){return this[e]},click(e){return this.each(function(){this.addEventListener("click",function(i){e.call(this,i)})})},hasClass(e){return this[0].classList.contains(e)},addClass(e){return this.each(function(){for(const i of e.split(" ")){this.classList.add(i)}})},removeClass(e){return this.each(function(){for(const i of e.split(" ")){this.classList.remove(i)}})},toggleClass(e){return this.each(function(){for(const i of e.split(" ")){this.classList.toggle(i)}})},css(e,t){function r(e,i){return document.defaultView.getComputedStyle(e,null).getPropertyValue(i)}function s(e,i,t){e.style[i]=t}return typeof e==="string"&&!t?r(this[0],e):this.each(function(){if(e&&t){s(this,e,t)}if(i.isCorrectObject(e)&&!t){for(const i in e){s(this,i,e[i])}}})},html(e){return this.each(function(){this.innerHTML=e})},text(e){return this[0].textContent},val(e){return this.each(function(){this.value=e})},eq(e){if(typeof e==="number"){const i=[];if(e<this.length){i.push(this[e])}return t(i)}},index(){if(this[0]){let e=this[0];let i=0;while((e=e.previousSibling)!==null){e.nodeType===1&&i++}return i}},prev(){const e=[];this.each(function(){const i=this.previousElementSibling;i&&e.push(i)});return t(e)},next(){const e=[];this.each(function(){const i=this.nextElementSibling;i&&e.push(i)});return t(e)},nextAll(){const e=[];this.each(function(){let i=this.nextElementSibling;function r(){if(i){e.push(i);i=t(i).get().nextElementSibling;r()}}r()});return t(e)},parent(){const e=[];this.each(function(){e.push(this.parentNode)});return t(e)},find(e){const i=[];this.each(function(){const t=this.querySelectorAll(e);for(var r=0,s=t.length;r<s;r++){t[r].nodeType===1&&i.push(t[r])}});return t(i)},siblings:function(){const e=[];this.each(function(){const i=this.parentNode.children;for(let t=0,r=i.length;t<r;t++){if(i[t]!==this){e.push(i[t])}}});return t(e)},add(e){let i=this;const r=t(e);for(let e=0,t=r.length;e<t;e++){i[i.length]=r[e];i.length++}return i},data(e,i){return typeof e==="string"&&!i?this[0].dataset[e]:this.each(function(){if(e&&i){this.dataset[e]=i}else{for(const i in e){this.dataset[i]=e[i]}}})},remove(){return this.each(function(){this.parentNode&&this.parentNode.removeChild(this)})},show(){return this.each(function(){this.style.display="block"})},hide(){return this.each(function(){this.style.display="";if(t(this).css("display")!=="none"){this.style.display="none"}})}};return function(i){let t=[];if(typeof i==="string"){t=document.querySelectorAll(i)}if(i.nodeType||i===document){t.push(i)}else if(i.length>0&&i[0]&&i[0].nodeType){for(let e=0,r=i.length;e<r;e++){t.push(i[e])}}return new e(t)}})();const r={theme:"select",data:{props:{code:"code",name:"name"},source:null,when:null},level:3,radius:2,width:200,height:34,maxHeight:300,disabled:[],disabledItem:[],selected:[],selectedCallback:()=>{},placeholder:["省","市","区"],separator:"/",clearable:false,strict:false,onlyShowLastLevel:false,icon:"arrow",onClear:()=>{},onSelect:()=>{}};const s={originalElem:new WeakMap,options:new WeakMap,value:new WeakMap,id:new WeakMap,target:new Map};const n=new Map;const c="__iPicker-default-style__";const a={createFrame(e,{theme:i,level:t,icon:r,clearable:s,disabled:n},c){let a=`\n<div class="iPicker-container">\n<div class="iPicker-result">\n<input \ntype="text" \nautocomplete="off" \nspellcheck="false"\nclass="iPicker-input"\nreadonly\n>\n<i class="arrow-icon ${r==="arrow-outline"?"arrow-outline":"arrow-triangle"}"></i>\n${s?'<i class="clear-icon"></i>':""}\n</div>\n<div class="iPicker-list iPicker-${i}">___</div>\n</div>\n`;switch(i){case"select":a=a.replace("___","<ul></ul>").repeat(t);break;case"cascader":a=a.replace("___","<ul></ul>".repeat(t));break;case"panel":a=a.replace("___",`\n<div class="iPicker-panel-tab">\n<div class="iPicker-panel-tab-active">省份</div>\n${t>1?"<div>城市</div>":""}\n${t>2?"<div>区县</div>":""}\n</div>\n<div class="iPicker-panel-content">${"<ul></ul>".repeat(t)}</div>\n`);break}e.addClass("iPicker-target").html(a).data({theme:i,id:c.toString().replace(/(\(|\))/g,"")})},createList(e,i,t){return new Promise(r=>{let s="";const n=i.theme==="cascader";if(!t){const{code:t,name:r}=i.data.props||{};e.forEach(e=>{s+=`\n<li data-code="${e[t]}" data-name="${e[r]}">\n<span>${e[r]}</span>\n${n?"<i></i>":""}\n</li>\n`})}else{for(const i in e){s+=`\n<li data-code="${i}" data-name="${e[i]}">\n<span>${e[i]}</span>\n${n?"<i></i>":""}\n</li>\n`}}r(s)})},getData(e,t,r,s){return new Promise(c=>{function a(e,t){if(i.isFunction(r.data.when)){return r.data.when(e,t)}else{return e}}if(!s){const i=n.get(e);if(i){c(a(i,t))}else{r.data.source(e,t).then(i=>{n.set(e,i);c(a(i,t))})}}else{r.data.source.then(i=>{c(a(i[e],t))})}})},getSelected(e){const i=e.find(".iPicker-list-active");const r=i.length;const[s,n,c]=[[],[],[]];if(r){i.each(function(){const e=t(this).data("code");const i=t(this).data("name");s.push(e);n.push(i);c.push({code:e,name:i})})}return[s,n,c]},cacheSelected(e,i){s.value.set(e,i)}};const l=(e,n)=>{if(!e||!n||typeof e!=="string"||!e.trim()||!i.isCorrectObject(n)||!i.isCorrectObject(n.data)||!n.data.source||!i.isFunction(n.data.source)&&i.type(n.data.source)!=="promise"){return}const o=t(e);const d=o.get();if(!d){return}const u=i.mergeParam(n,r);if(!i.isCorrectNumber(u.level)||u.level<1||u.level>3){u.level=3}const h=u.theme==="select";const f=u.theme==="cascader";const p=u.theme==="panel";const g=i.type(u.data.source)==="promise";const b=i.isFunction(u.data.source);const k=i.isFunction(u.onClear);const M=i.isFunction(u.onSelect);const m=d._iPicker_uid_||i.uid(true);s.originalElem.set(d,e);s.options.set(d,u);s.target.set(m,d);s.id.set(d,m);d._iPicker_uid_=m;if(!document.getElementById(c)){document.head.insertAdjacentHTML("afterbegin",`\n<style id="${c}">.iPicker-target{position:relative;height:34px}.iPicker-container,.iPicker-target *{box-sizing:border-box;font-size:14px;margin:0;padding:0}.iPicker-container{height:34px;float:left;position:relative}.iPicker-container:not(:first-of-type){margin-left:10px}.iPicker-result{display:flex;align-items:center;position:relative;background:#fff;border:#d6d6d6 solid 1px;min-width:100px;height:34px;border-radius:2px;cursor:pointer;user-select:none}.iPicker-result.iPicker-result-active:not(.iPicker-disabled),.iPicker-result.iPicker-result-active:not(.iPicker-disabled):hover{border:#00b8ff solid 1px}.iPicker-result.iPicker-result-active i::before{transform:scale(.55) rotate(180deg)}.iPicker-result.iPicker-result-active i.arrow-outline::before{transform:scale(.72) rotate(180deg)}.iPicker-result i{position:absolute;top:0;right:0;display:block;width:30px;height:34px}.iPicker-result i::before{position:absolute;top:0;right:2px;display:block;width:28px;height:100%;background-position:center;background-repeat:no-repeat;content:"";opacity:.5;transition:transform .2s;transform:scale(.55)}.iPicker-result i.arrow-icon::before{background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNTc2OTk1MjQ3Njc4IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjI2NTAiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTUzNS40NjY2NjcgODEyLjhsNDUwLjEzMzMzMy01NjMuMmMxNC45MzMzMzMtMTkuMiAyLjEzMzMzMy00OS4wNjY2NjctMjMuNDY2NjY3LTQ5LjA2NjY2N0g2MS44NjY2NjdjLTI1LjYgMC0zOC40IDI5Ljg2NjY2Ny0yMy40NjY2NjcgNDkuMDY2NjY3bDQ1MC4xMzMzMzMgNTYzLjJjMTIuOCAxNC45MzMzMzMgMzQuMTMzMzMzIDE0LjkzMzMzMyA0Ni45MzMzMzQgMHoiIHAtaWQ9IjI2NTEiIGZpbGw9IiMwMDAwMDAiPjwvcGF0aD48L3N2Zz4=)}.iPicker-result i.clear-icon::before{background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjA3Njc2MTg3NDk0IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjMzNDIiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTUxMS45MTg2NDcgMTYzLjE1NDkxN2MtMTkzLjQzODY0MSAwLTM1MS42OTcwMzcgMTU4LjI1NDMwNC0zNTEuNjk3MDM3IDM1MS42OTkwODQgMCAxOTMuNDM5NjY0IDE1OC4yNTczNzQgMzUxLjY5NzAzNyAzNTEuNjk3MDM3IDM1MS42OTcwMzcgMTkzLjM5NDYzOCAwIDM1MS42NTMwMzUtMTU4LjI1NjM1IDM1MS42NTMwMzUtMzUxLjY5NzAzN0M4NjMuNTcwNjU5IDMyMS40MDkyMjEgNzA1LjMxMzI4NiAxNjMuMTU0OTE3IDUxMS45MTg2NDcgMTYzLjE1NDkxN002ODcuNzM2OTc4IDY0MS40NTIzMjdsLTQ5LjE5ODUxNSA0OS4yMjQwOTgtMTI2LjYxOTgxNi0xMjYuNjAwMzczLTEyNi41NzM3NjcgMTI2LjYwMDM3My00OS4zMDQ5MzktNDkuMjI0MDk4IDEyNi42MzYxODktMTI2LjU5ODMyNi0xMjYuNjM2MTg5LTEyNi42MDAzNzMgNDkuMzA0OTM5LTQ5LjIyNDA5OCAxMjYuNTczNzY3IDEyNi42MDAzNzMgMTI2LjYxOTgxNi0xMjYuNjAwMzczIDQ5LjE5ODUxNSA0OS4yMjQwOTgtMTI2LjU3Mzc2NyAxMjYuNjAwMzczTDY4Ny43MzY5NzggNjQxLjQ1MjMyN3oiIHAtaWQ9IjMzNDMiIGZpbGw9IiMwMDAwMDAiPjwvcGF0aD48L3N2Zz4=);transform:scale(1);z-index:10}.iPicker-result i.arrow-outline::before{background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjA1MDYzNzA0MzAzIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjM0NDMiIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PC9zdHlsZT48L2RlZnM+PHBhdGggZD0iTTUxMiA3MzBjLTYuNCAwLTEyLjgtMi40LTE3LjctNy4zbC0zODYtMzg2Yy05LjgtOS44LTkuOC0yNS42IDAtMzUuNCA5LjgtOS44IDI1LjYtOS44IDM1LjQgMEw1MTIgNjY5LjZsMzY4LjMtMzY4LjNjOS44LTkuOCAyNS42LTkuOCAzNS40IDAgOS44IDkuOCA5LjggMjUuNiAwIDM1LjRsLTM4NiAzODZjLTQuOSA0LjktMTEuMyA3LjMtMTcuNyA3LjN6IiBmaWxsPSIjMzMzMzMzIiBwLWlkPSIzNDQ0Ij48L3BhdGg+PC9zdmc+);transform:scale(.72);opacity:.9}.iPicker-result.iPicker-disabled{cursor:not-allowed;background:#f2f5fa;color:#898989;border-color:#dfdfdf}.iPicker-result.iPicker-disabled input{cursor:not-allowed;background:#f2f5fa;color:#898989}.iPicker-input{display:block;width:100%;width:calc(100% - 23px);height:32px;padding:0 10px;outline:0;border:0;cursor:pointer;user-select:none}.iPicker-result input::selection{background:#fff}.iPicker-list.iPicker-list-ontop{transform-origin:center bottom}.iPicker-list-show-temporary{display:block!important;opacity:0!important;pointer-events:none!important}.iPicker-input::-webkit-input-placeholder{color:#aaa}.iPicker-input::-moz-placeholder{color:#aaa}.iPicker-target[data-theme=panel] .iPicker-list{width:370px}.iPicker-list{position:relative;z-index:10;display:none;overflow-x:hidden;overflow-y:auto;overscroll-behavior:contain;user-select:none;background:#fff;border:#ddd solid 1px;box-shadow:rgba(0,0,0,.12) 0 2px 6px;transform-origin:center top;transform:scaleY(0);transition-property:transform,opacity;transition-duration:.2s}.iPicker-list ul{display:block;overflow:hidden}.iPicker-list li{cursor:pointer;transition:.2s}.iPicker-list li span{pointer-events:none}.iPicker-list li.iPicker-list-active,.iPicker-list li:hover{color:#00b8ff;background:rgba(0,184,255,.1)}.iPicker-list li.iPicker-list-disabled{cursor:not-allowed;background:#f2f5fa;color:#b6b6b6}.iPicker-panel-tab{display:flex;align-items:center;justify-content:flex-start;height:36px;background-color:#f5f5f5;border-bottom:#ddd solid 1px}.iPicker-panel-tab>div{cursor:pointer;padding:0 20px;height:36px;line-height:36px}.iPicker-panel-tab>div:last-child.iPicker-panel-tab-active,.iPicker-panel-tab>div:not(:last-child){border-right:#d3d3d3 solid 1px}.iPicker-panel-tab>div.iPicker-panel-tab-active{background:#fff;cursor:default;position:relative;height:37px;border-bottom:#fff solid 1px;color:#00b8ff}.iPicker-panel-content{padding:10px 0;min-height:50px;overflow-x:hidden;overflow-y:auto}.iPicker-panel-content ul{display:none}.iPicker-panel li{float:left;display:block;margin:2px 4px;padding:4px 10px;border-radius:2px}.iPicker-panel li span{font-size:13px}.iPicker-cascader li,.iPicker-select li{position:relative;display:block;padding:6px 12px;list-style:none;transition:.25s;overflow:hidden;clear:both;word-break:break-all}.iPicker-cascader li:first-child,.iPicker-select li:first-child{margin-top:8px}.iPicker-cascader li:last-child,.iPicker-select li:last-child{margin-bottom:8px}.iPicker-list.iPicker-cascader{overflow:hidden}.iPicker-cascader ul{position:relative;z-index:4;display:none;width:200px;float:left;overflow-y:auto;overscroll-behavior:contain}.iPicker-cascader ul:nth-child(2){z-index:3;margin-left:-2px}.iPicker-cascader ul:nth-child(3){z-index:2;margin-left:0}.iPicker-cascader ul:nth-child(4){z-index:1}.iPicker-cascader ul:not(:last-child){border-right:#dfdfdf solid 1px}.iPicker-cascader li i{display:block;position:absolute;top:50%;right:10px;width:8px;height:8px;margin-top:-4px;border-top:#6f6f6f solid 1px;border-right:#6f6f6f solid 1px;transform:scale(.8) rotate(45deg)}.iPicker-cascader li.iPicker-list-disabled i{opacity:.4}.iPicker-cascader li.iPicker-list-active i{border-top-color:#00b8ff;border-right-color:#00b8ff}.iPicker-cascader ul:last-child li i{display:none}.iPicker-list.iPicker-list-show{display:block;transform:scaleY(1)}.iPicker-list.iPicker-list-hide{transform:scaleY(0);opacity:0}</style>\n`)}a.createFrame(o,u,m);const P=o.find(".iPicker-container");const v=o.find(".iPicker-result");const y=o.find(".iPicker-input");const x=o.find(".iPicker-list");const w=x.find("ul");w.each(function(e){t(this).data("level",++e)});if(i.isCorrectNumber(u.maxHeight)&&u.maxHeight>=100){x.css("maxHeight",`${u.maxHeight}px`);if(f){w.css("maxHeight",`${u.maxHeight}px`)}if(p){x.find(".iPicker-panel-content").css("height",`${u.maxHeight-38}px`)}}if(i.isCorrectNumber(u.width)&&u.width>=100){v.css("width",`${u.width}px`);if(h){x.css("width",`${u.width}px`)}}if(typeof u.width==="string"&&u.width.trim().endsWith("%")){v.css("width",u.width);if(h){x.css("width",u.width)}else{P.css("width",u.width)}}if(i.isCorrectNumber(u.height)&&u.height>=20){v.css("height",`${u.height}px`);y.css("height",`${u.height-2}px`);y.next().css("height",`${u.height-2}px`)}if(u.disabledItem===true){new MutationObserver(()=>{o.find("li").addClass("iPicker-list-disabled")}).observe(d,{childList:true,subtree:true})}if(Array.isArray(u.disabledItem)&&u.disabledItem.length){for(const e of[...new Set(u.disabledItem)]){new MutationObserver(()=>{const i=d.querySelector(`[data-code="${e}"]:not(.iPicker-list-disabled)`);if(i){i.classList.add("iPicker-list-disabled")}}).observe(d,{childList:true,subtree:true})}}if(u.disabled===true){u.disabled=[1,2,3].slice(0,u.level)}if(i.isCorrectNumber(u.disabled)){u.disabled=[u.disabled]}if(Array.isArray(u.disabled)&&u.disabled.length){for(const e of[...new Set(u.disabled)]){if(i.isCorrectNumber(e)&&e>=1&&e<=3){v.eq(e-1).addClass("iPicker-disabled")}}}if(h&&Array.isArray(u.placeholder)){u.placeholder.forEach((e,i)=>{const t=y.eq(i).get();if(t){t.setAttribute("placeholder",u.placeholder[i]||r.placeholder[i])}})}if(f||p){if(typeof u.placeholder!=="string"||!u.placeholder.trim()){u.placeholder="请选择地区"}y.eq(0).get().setAttribute("placeholder",u.placeholder)}if(i.isCorrectNumber(u.radius,true)){v.add(y).css("borderRadius",`${u.radius}px`)}v.find(".clear-icon").hide();v.each(function(){const e=t(this).get();e.addEventListener("mouseenter",()=>{const i=e.querySelector("input");if(i){if(i.value&&!e.classList.contains("iPicker-disabled")){t(this).find(".clear-icon").show().prev().hide()}}});e.addEventListener("mouseleave",()=>{t(this).find(".clear-icon").hide().prev().show()})});const N=o.find(".clear-icon");if(!h){N.click(()=>{N.hide().prev().show();l.clear(m);if(k){u.onClear()}})}else{N.each(function(){const e=t(this);e.click(function(){e.hide().prev().show();const i=e.parent();const t=i.next().find("ul");const r=+t.data("index");i.find("input").val("");t.find(".iPicker-list-active").removeClass("iPicker-list-active");i.parent().nextAll().find("input").val("").parent().next().find("ul").html("");L();z(i.next());if(k){u.onClear()}})})}v.each(function(){t(this).find("input, .arrow-icon").click(function(){const e=t(this).parent();const i=e.next();const r=e.parent().parent().data("id");const s=t(`.iPicker-target:not([data-id="${r}"]) .iPicker-list`);if(s.length){z(s)}if(!e.hasClass("iPicker-disabled")){if(!i.find("li").length){return}e.toggleClass("iPicker-result-active");if(i.hasClass("iPicker-list-show")){z(i)}else{if(p){o.find(".iPicker-panel-tab > div:first-child").addClass("iPicker-panel-tab-active").siblings().removeClass("iPicker-panel-tab-active");o.find(".iPicker-panel-content > ul:first-child").show().siblings().hide()}let t=false;const r=parseInt(e.css("height"));function n(){if(t){return}t=true;i.addClass("iPicker-list-show-temporary");const s=document.documentElement.clientHeight-e.get().getBoundingClientRect().bottom;const n=parseInt(i.css("height"));if(s<n){i.css("marginTop",`-${n+r}px`).addClass("iPicker-list-ontop")}else{i.css("marginTop","0px").removeClass("iPicker-list-ontop")}t=false;i.removeClass("iPicker-list-show-temporary")}n();window.addEventListener("scroll",n);window.addEventListener("resize",n);i.addClass("iPicker-list-show").removeClass("iPicker-list-hide")}}})});function z(e){if(e.hasClass("iPicker-list-show")){e.addClass("iPicker-list-hide").removeClass("iPicker-list-show").prev().removeClass("iPicker-result-active");e.show();i.delayTimer(200).then(()=>{e.get().style.removeProperty("display")});if(!h){L()}const r=e.parent().parent();const n=s.options.get(r.get());if(n.strict){i.delayTimer(200).then(()=>{const[e]=s.value.get(r.get());const i=e.length;if(i&&i!==n.level){function c(){return new Promise(e=>{if(i===1){if(n.level===2){e()}else{j(w.eq(1).find("li:first-child").data("code"),3).then(()=>{e()})}}else{e()}})}c().then(()=>{w.each(function(){if(!t(this).find(".iPicker-list-active").length){t(this).find("li:first-child").addClass("iPicker-list-active")}});L()})}})}}}function j(e,i){return new Promise(t=>{a.getData(e,i,u,g).then(e=>{a.createList(e,u,g).then(e=>{const r=w.eq(i-1);r.html(e).nextAll().html("");if(h){r.parent().parent().nextAll().find("ul").html("")}if(f){let e=0;w.each(function(){if(this.innerHTML){e++}});x.css("width",`${200*e}px`);w.eq(i-1).show().nextAll().hide()}if(p){w.eq(i-1).show().siblings().hide();o.find(`.iPicker-panel-tab > div:nth-child(${i})`).addClass("iPicker-panel-tab-active").siblings().removeClass("iPicker-panel-tab-active")}L();t()})})})}function L(){const e=a.getSelected(o);a.cacheSelected(d,e);const i=u.separator.trim().charAt(0);function t(e){if(e){if(f||p){if(u.onlyShowLastLevel){e=e.split(i).slice(-1)[0].trim()}}}return e}if(h){e[1].forEach((e,i)=>{y.eq(i).val(t(e))})}else{const r=e[1].join(` ${i} `);y.eq(0).val(t(r))}if(M){if(e[1].length){u.onSelect(...s.value.get(d))}}}j(g?"86":null,1).then(()=>{d.dataset.promise="true";if(Array.isArray(u.selected)&&u.selected.length){u.selected=[...new Set(u.selected)];for(const e of u.selected){if(u.disabledItem.includes(e)){return}}!function e(t){j(u.selected[t-1],t+1).then(()=>{t++;if(t<u.level){e(t)}else{u.selected.forEach(e=>{o.find(`li[data-code="${e}"]`).addClass("iPicker-list-active")});L();if(i.isFunction(u.selectedCallback)){u.selectedCallback()}}})}(1)}});o.click(e=>{if(e.target.nodeName.toLowerCase()!=="li"){return}const i=t(e.target);const r=i.parent();if(i.hasClass("iPicker-list-disabled")){return}i.addClass("iPicker-list-active").siblings().removeClass("iPicker-list-active");j(i.data("code"),+r.data("level")+1);if(h){z(r.parent());r.parent().parent().nextAll().find(".iPicker-result input").val("")}if(r.index()===u.level-1){if(f){z(r.parent())}if(p){z(r.parent().parent())}}});if(f){w.css({minHeight:`${u.maxHeight}px`,maxHeight:`${u.maxHeight}px`})}if(p){o.find(".iPicker-panel-tab > div").click(function(){const e=t(this).index();if(!t(this).parent().next().find("ul").eq(e).find("li").length){return}t(this).addClass("iPicker-panel-tab-active").siblings().removeClass("iPicker-panel-tab-active");o.find(".iPicker-panel-content ul").eq(t(this).index()).show().siblings().hide()})}t(document).click(function(e){P.each(function(i){if(e.target!==this&&!this.contains(e.target)){z(x.eq(i))}})});return m};l.create=((e,i)=>{return l(e,i)});l.set=((e,i)=>{const t=s.target.get(e);if(!e||!t||!i||!Array.isArray(i)||!i.length){return}l.clear(e);if(t.dataset.promise){r()}else{new MutationObserver(()=>{r()}).observe(t,{attributes:true})}function r(){const e=t.querySelectorAll("ul");!function r(s){const n=t.querySelector(`[data-code="${i[s]}"]`);if(e[s+1]){new MutationObserver(()=>{++s;if(s<i.length){r(s)}}).observe(e[s+1],{childList:true});n.click()}else{n&&n.click()}}(0)}});l.get=((e,i)=>{const t=s.target.get(e);if(!e||!t){return}const r=s.value.get(t);if(i==="code"||i===undefined){return r[0]}if(i==="name"){return r[1]}if(i==="all"){return r[2]}});l.clear=(e=>{const i=s.target.get(e);if(!e||!i){return}const r=t(i);const n=s.options.get(i);s.value.set(i,[[],[],[]]);r.find("input").val("");r.find("li").removeClass("iPicker-list-active");r.find("ul").each(function(e){const i=t(this);if(e){i.html("");if(n.theme==="cascader"){i.parent().css("width","200px");i.get().style.removeProperty("display")}}});if(n.theme==="panel"){r.find(".iPicker-panel-tab > div").eq(0).addClass("iPicker-panel-tab-active").siblings().removeClass("iPicker-panel-tab-active");r.find(".iPicker-panel-content > ul").eq(0).show().siblings().hide()}r.find(".iPicker-list").get().scrollTop=0;r.find("ul").get().scrollTop=0;return e});l.reset=(e=>{const i=s.target.get(e);if(!e||!i){return}return l(s.originalElem.get(i),s.options.get(i))});l.destroy=(e=>{const i=s.target.get(e);if(!e||!i){return}s.originalElem.delete(i);s.value.delete(i);s.options.delete(i);s.id.delete(i);s.target.delete(i._iPicker_uid_);delete i._iPicker_uid_;i.innerHTML="";if(!document.querySelector(".iPicker-container")){t(`#${c}`).remove()}});l.enabled=((e,r)=>{const n=s.target.get(e);if(!e||!n||!r){return}const c=t(n);const a=c.find(".iPicker-result");if(r===true){a.removeClass("iPicker-disabled")}if(i.isCorrectNumber(r)){r=[r]}if(Array.isArray(r)&&r.length){r.forEach(e=>{if(i.isCorrectNumber(e)&&e>=1&&e<=3){a.eq(e-1).removeClass("iPicker-disabled")}})}return e});l.disabled=((e,r)=>{const n=s.target.get(e);if(!e||!n||!r){return}const c=t(n);const a=c.find(".iPicker-result");if(r===true){a.addClass("iPicker-disabled")}if(i.isCorrectNumber(r)){r=[r]}if(Array.isArray(r)&&r.length){r.forEach(e=>{if(i.isCorrectNumber(e)&&e>=1&&e<=3){a.eq(e-1).addClass("iPicker-disabled")}})}return e});const o=e=>e.filter(e=>typeof e==="string"&&e.match(/^\d{6,12}$/));l.enabledItem=((e,i)=>{const r=s.target.get(e);if(!e||!r||!i){return}const n={childList:true,subtree:true};if(i===true){new MutationObserver(()=>{t(r).find("li").removeClass("iPicker-list-disabled")}).observe(r,n)}if(Array.isArray(i)&&i.length){i=o([...new Set(i)]);const e=i.length;new MutationObserver(()=>{for(let t=0;t<e;t++){const e=r.querySelector(`[data-code="${i[t]}"]`);if(e){e.classList.remove("iPicker-list-disabled")}}}).observe(r,n)}return e});l.disabledItem=((e,i)=>{const r=s.target.get(e);if(!e||!r||!i){return}const n={childList:true,subtree:true};if(i===true){new MutationObserver(()=>{t(r).find("li").addClass("iPicker-list-disabled")}).observe(r,n)}if(Array.isArray(i)&&i.length){i=o([...new Set(i)]);const e=i.length;new MutationObserver(()=>{for(let t=0;t<e;t++){const e=r.querySelector(`[data-code="${i[t]}"]`);if(e){e.classList.add("iPicker-list-disabled")}}}).observe(r,n)}return e});return l});