{"version": 3, "file": "summernote-lite.js", "sources": ["../src/js/base/renderer.js", "../src/js/lite/ui/TooltipUI.js", "../src/js/lite/ui/DropdownUI.js", "../src/js/lite/ui/ModalUI.js", "../src/js/lite/ui.js", "../src/js/base/summernote-en-US.js", "../src/js/base/core/env.js", "../src/js/base/core/func.js", "../src/js/base/core/lists.js", "../src/js/base/core/key.js", "../src/js/base/core/dom.js", "../src/js/base/core/range.js", "../src/js/base/core/async.js", "../src/js/base/editing/History.js", "../src/js/base/editing/Style.js", "../src/js/base/editing/Bullet.js", "../src/js/base/editing/Typing.js", "../src/js/base/editing/Table.js", "../src/js/base/module/Editor.js", "../src/js/base/module/Clipboard.js", "../src/js/base/module/Dropzone.js", "../src/js/base/module/Codeview.js", "../src/js/base/module/Statusbar.js", "../src/js/base/module/Fullscreen.js", "../src/js/base/module/Handle.js", "../src/js/base/module/AutoLink.js", "../src/js/base/module/AutoSync.js", "../src/js/base/module/Placeholder.js", "../src/js/base/module/Buttons.js", "../src/js/base/module/Toolbar.js", "../src/js/base/module/LinkDialog.js", "../src/js/base/module/LinkPopover.js", "../src/js/base/module/ImageDialog.js", "../src/js/base/module/ImagePopover.js", "../src/js/base/module/TablePopover.js", "../src/js/base/module/VideoDialog.js", "../src/js/base/module/HelpDialog.js", "../src/js/base/module/AirPopover.js", "../src/js/base/module/HintPopover.js", "../src/js/base/Context.js", "../src/js/summernote.js", "../src/js/lite/settings.js"], "sourcesContent": ["import $ from 'jquery';\n\nclass Renderer {\n  constructor(markup, children, options, callback) {\n    this.markup = markup;\n    this.children = children;\n    this.options = options;\n    this.callback = callback;\n  }\n\n  render($parent) {\n    const $node = $(this.markup);\n\n    if (this.options && this.options.contents) {\n      $node.html(this.options.contents);\n    }\n\n    if (this.options && this.options.className) {\n      $node.addClass(this.options.className);\n    }\n\n    if (this.options && this.options.data) {\n      $.each(this.options.data, (k, v) => {\n        $node.attr('data-' + k, v);\n      });\n    }\n\n    if (this.options && this.options.click) {\n      $node.on('click', this.options.click);\n    }\n\n    if (this.children) {\n      const $container = $node.find('.note-children-container');\n      this.children.forEach((child) => {\n        child.render($container.length ? $container : $node);\n      });\n    }\n\n    if (this.callback) {\n      this.callback($node, this.options);\n    }\n\n    if (this.options && this.options.callback) {\n      this.options.callback($node);\n    }\n\n    if ($parent) {\n      $parent.append($node);\n    }\n\n    return $node;\n  }\n}\n\nexport default {\n  create: (markup, callback) => {\n    return function() {\n      const options = typeof arguments[1] === 'object' ? arguments[1] : arguments[0];\n      let children = $.isArray(arguments[0]) ? arguments[0] : [];\n      if (options && options.children) {\n        children = options.children;\n      }\n      return new Renderer(markup, children, options, callback);\n    };\n  }\n};\n", "class TooltipUI {\n  constructor($node, options) {\n    this.$node = $node;\n    this.options = $.extend({}, {\n      title: '',\n      target: options.container,\n      trigger: 'hover focus',\n      placement: 'bottom'\n    }, options);\n\n    // create tooltip node\n    this.$tooltip = $([\n      '<div class=\"note-tooltip in\">',\n      '  <div class=\"note-tooltip-arrow\"/>',\n      '  <div class=\"note-tooltip-content\"/>',\n      '</div>'\n    ].join(''));\n\n    // define event\n    if (this.options.trigger !== 'manual') {\n      const showCallback = this.show.bind(this);\n      const hideCallback = this.hide.bind(this);\n      const toggleCallback = this.toggle.bind(this);\n\n      this.options.trigger.split(' ').forEach(function(eventName) {\n        if (eventName === 'hover') {\n          $node.off('mouseenter mouseleave');\n          $node.on('mouseenter', showCallback).on('mouseleave', hideCallback);\n        } else if (eventName === 'click') {\n          $node.on('click', toggleCallback);\n        } else if (eventName === 'focus') {\n          $node.on('focus', showCallback).on('blur', hideCallback);\n        }\n      });\n    }\n  }\n\n  show() {\n    const $node = this.$node;\n    const offset = $node.offset();\n\n    const $tooltip = this.$tooltip;\n    const title = this.options.title || $node.attr('title') || $node.data('title');\n    const placement = this.options.placement || $node.data('placement');\n\n    $tooltip.addClass(placement);\n    $tooltip.addClass('in');\n    $tooltip.find('.note-tooltip-content').text(title);\n    $tooltip.appendTo(this.options.target);\n\n    const nodeWidth = $node.outerWidth();\n    const nodeHeight = $node.outerHeight();\n    const tooltipWidth = $tooltip.outerWidth();\n    const tooltipHeight = $tooltip.outerHeight();\n\n    if (placement === 'bottom') {\n      $tooltip.css({\n        top: offset.top + nodeHeight,\n        left: offset.left + (nodeWidth / 2 - tooltipWidth / 2)\n      });\n    } else if (placement === 'top') {\n      $tooltip.css({\n        top: offset.top - tooltipHeight,\n        left: offset.left + (nodeWidth / 2 - tooltipWidth / 2)\n      });\n    } else if (placement === 'left') {\n      $tooltip.css({\n        top: offset.top + (nodeHeight / 2 - tooltipHeight / 2),\n        left: offset.left - tooltipWidth\n      });\n    } else if (placement === 'right') {\n      $tooltip.css({\n        top: offset.top + (nodeHeight / 2 - tooltipHeight / 2),\n        left: offset.left + nodeWidth\n      });\n    }\n  }\n\n  hide() {\n    this.$tooltip.removeClass('in');\n    this.$tooltip.remove();\n  }\n\n  toggle() {\n    if (this.$tooltip.hasClass('in')) {\n      this.hide();\n    } else {\n      this.show();\n    }\n  }\n}\n\nexport default TooltipUI;\n", "class DropdownUI {\n  constructor($node, options) {\n    this.$button = $node;\n    this.options = $.extend({}, {\n      target: options.container\n    }, options);\n    this.setEvent();\n  }\n\n  setEvent() {\n    this.$button.on('click', (e) => {\n      this.toggle();\n      e.stopImmediatePropagation();\n    });\n  }\n\n  clear() {\n    var $parent = $('.note-btn-group.open');\n    $parent.find('.note-btn.active').removeClass('active');\n    $parent.removeClass('open');\n  }\n\n  show() {\n    this.$button.addClass('active');\n    this.$button.parent().addClass('open');\n\n    var $dropdown = this.$button.next();\n    var offset = $dropdown.offset();\n    var width = $dropdown.outerWidth();\n    var windowWidth = $(window).width();\n    var targetMarginRight = parseFloat($(this.options.target).css('margin-right'));\n\n    if (offset.left + width > windowWidth - targetMarginRight) {\n      $dropdown.css('margin-left', windowWidth - targetMarginRight - (offset.left + width));\n    } else {\n      $dropdown.css('margin-left', '');\n    }\n  }\n\n  hide() {\n    this.$button.removeClass('active');\n    this.$button.parent().removeClass('open');\n  }\n\n  toggle() {\n    var isOpened = this.$button.parent().hasClass('open');\n\n    this.clear();\n\n    if (isOpened) {\n      this.hide();\n    } else {\n      this.show();\n    }\n  }\n}\n\n$(document).on('click', function(e) {\n  if (!$(e.target).closest('.note-btn-group').length) {\n    $('.note-btn-group.open').removeClass('open');\n  }\n});\n\n$(document).on('click.note-dropdown-menu', function(e) {\n  $(e.target).closest('.note-dropdown-menu').parent().removeClass('open');\n});\n\nexport default DropdownUI;\n", "class ModalUI {\n  constructor($node, options) {\n    this.options = $.extend({}, {\n      target: options.container || 'body'\n    }, options);\n\n    this.$modal = $node;\n    this.$backdrop = $('<div class=\"note-modal-backdrop\" />');\n  }\n\n  show() {\n    if (this.options.target === 'body') {\n      this.$backdrop.css('position', 'fixed');\n      this.$modal.css('position', 'fixed');\n    } else {\n      this.$backdrop.css('position', 'absolute');\n      this.$modal.css('position', 'absolute');\n    }\n\n    this.$backdrop.appendTo(this.options.target).show();\n    this.$modal.appendTo(this.options.target).addClass('open').show();\n\n    this.$modal.trigger('note.modal.show');\n    this.$modal.off('click', '.close').on('click', '.close', this.hide.bind(this));\n  }\n\n  hide() {\n    this.$modal.removeClass('open').hide();\n    this.$backdrop.hide();\n    this.$modal.trigger('note.modal.hide');\n  }\n}\n\nexport default ModalUI;\n", "import renderer from '../base/renderer';\nimport TooltipUI from './ui/TooltipUI';\nimport DropdownUI from './ui/DropdownUI';\nimport ModalUI from './ui/ModalUI';\n\nconst editor = renderer.create('<div class=\"note-editor note-frame\"/>');\nconst toolbar = renderer.create('<div class=\"note-toolbar\" role=\"toolbar\"/>');\nconst editingArea = renderer.create('<div class=\"note-editing-area\"/>');\nconst codable = renderer.create('<textarea class=\"note-codable\" role=\"textbox\" aria-multiline=\"true\"/>');\nconst editable = renderer.create('<div class=\"note-editable\" contentEditable=\"true\" role=\"textbox\" aria-multiline=\"true\"/>');\nconst statusbar = renderer.create([\n  '<output class=\"note-status-output\" role=\"status\" aria-live=\"polite\"/>',\n  '<div class=\"note-statusbar\" role=\"resize\">',\n  '  <div class=\"note-resizebar\" role=\"seperator\" aria-orientation=\"horizontal\" aria-label=\"resize\">',\n  '    <div class=\"note-icon-bar\"/>',\n  '    <div class=\"note-icon-bar\"/>',\n  '    <div class=\"note-icon-bar\"/>',\n  '  </div>',\n  '</div>'\n].join(''));\n\nconst airEditor = renderer.create('<div class=\"note-editor\"/>');\nconst airEditable = renderer.create([\n  '<div class=\"note-editable\" contentEditable=\"true\" role=\"textbox\" aria-multiline=\"true\"/>',\n  '<output class=\"note-status-output\" role=\"status\" aria-live=\"polite\"/>'\n].join(''));\n\nconst buttonGroup = renderer.create('<div class=\"note-btn-group\">');\nconst button = renderer.create('<button type=\"button\" class=\"note-btn\" role=\"button\" tabindex=\"-1\">', function($node, options) {\n  // set button type\n  if (options && options.tooltip) {\n    $node.attr({\n      'aria-label': options.tooltip\n    });\n    $node.data('_lite_tooltip', new TooltipUI($node, {\n      title: options.tooltip,\n      container: options.container\n    }));\n  }\n  if (options.contents) {\n    $node.html(options.contents);\n  }\n\n  if (options && options.data && options.data.toggle === 'dropdown') {\n    $node.data('_lite_dropdown', new DropdownUI($node, {\n      container: options.container\n    }));\n  }\n});\n\nconst dropdown = renderer.create('<div class=\"note-dropdown-menu\" role=\"list\">', function($node, options) {\n  const markup = $.isArray(options.items) ? options.items.map(function(item) {\n    const value = (typeof item === 'string') ? item : (item.value || '');\n    const content = options.template ? options.template(item) : item;\n    const $temp = $('<a class=\"note-dropdown-item\" href=\"#\" data-value=\"' + value + '\" role=\"listitem\" aria-label=\"' + item + '\"></a>');\n\n    $temp.html(content).data('item', item);\n\n    return $temp;\n  }) : options.items;\n\n  $node.html(markup).attr({ 'aria-label': options.title });\n\n  $node.on('click', '> .note-dropdown-item', function(e) {\n    const $a = $(this);\n\n    const item = $a.data('item');\n    const value = $a.data('value');\n\n    if (item.click) {\n      item.click($a);\n    } else if (options.itemClick) {\n      options.itemClick(e, item, value);\n    }\n  });\n});\n\nconst dropdownCheck = renderer.create('<div class=\"note-dropdown-menu note-check\" role=\"list\">', function($node, options) {\n  const markup = $.isArray(options.items) ? options.items.map(function(item) {\n    const value = (typeof item === 'string') ? item : (item.value || '');\n    const content = options.template ? options.template(item) : item;\n\n    const $temp = $('<a class=\"note-dropdown-item\" href=\"#\" data-value=\"' + value + '\" role=\"listitem\" aria-label=\"' + item + '\"></a>');\n    $temp.html([icon(options.checkClassName), ' ', content]).data('item', item);\n    return $temp;\n  }) : options.items;\n\n  $node.html(markup).attr({ 'aria-label': options.title });\n\n  $node.on('click', '> .note-dropdown-item', function(e) {\n    const $a = $(this);\n\n    const item = $a.data('item');\n    const value = $a.data('value');\n\n    if (item.click) {\n      item.click($a);\n    } else if (options.itemClick) {\n      options.itemClick(e, item, value);\n    }\n  });\n});\n\nconst dropdownButtonContents = function(contents, options) {\n  return contents + ' ' + icon(options.icons.caret, 'span');\n};\n\nconst dropdownButton = function(opt, callback) {\n  return buttonGroup([\n    button({\n      className: 'dropdown-toggle',\n      contents: opt.title + ' ' + icon('note-icon-caret'),\n      tooltip: opt.tooltip,\n      data: {\n        toggle: 'dropdown'\n      }\n    }),\n    dropdown({\n      className: opt.className,\n      items: opt.items,\n      template: opt.template,\n      itemClick: opt.itemClick\n    })\n  ], { callback: callback }).render();\n};\n\nconst dropdownCheckButton = function(opt, callback) {\n  return buttonGroup([\n    button({\n      className: 'dropdown-toggle',\n      contents: opt.title + ' ' + icon('note-icon-caret'),\n      tooltip: opt.tooltip,\n      data: {\n        toggle: 'dropdown'\n      }\n    }),\n    dropdownCheck({\n      className: opt.className,\n      checkClassName: opt.checkClassName,\n      items: opt.items,\n      template: opt.template,\n      itemClick: opt.itemClick\n    })\n  ], { callback: callback }).render();\n};\n\nconst paragraphDropdownButton = function(opt) {\n  return buttonGroup([\n    button({\n      className: 'dropdown-toggle',\n      contents: opt.title + ' ' + icon('note-icon-caret'),\n      tooltip: opt.tooltip,\n      data: {\n        toggle: 'dropdown'\n      }\n    }),\n    dropdown([\n      buttonGroup({\n        className: 'note-align',\n        children: opt.items[0]\n      }),\n      buttonGroup({\n        className: 'note-list',\n        children: opt.items[1]\n      })\n    ])\n  ]).render();\n};\n\nconst tableMoveHandler = function(event, col, row) {\n  const PX_PER_EM = 18;\n  const $picker = $(event.target.parentNode); // target is mousecatcher\n  const $dimensionDisplay = $picker.next();\n  const $catcher = $picker.find('.note-dimension-picker-mousecatcher');\n  const $highlighted = $picker.find('.note-dimension-picker-highlighted');\n  const $unhighlighted = $picker.find('.note-dimension-picker-unhighlighted');\n\n  let posOffset;\n  // HTML5 with jQuery - e.offsetX is undefined in Firefox\n  if (event.offsetX === undefined) {\n    const posCatcher = $(event.target).offset();\n    posOffset = {\n      x: event.pageX - posCatcher.left,\n      y: event.pageY - posCatcher.top\n    };\n  } else {\n    posOffset = {\n      x: event.offsetX,\n      y: event.offsetY\n    };\n  }\n\n  const dim = {\n    c: Math.ceil(posOffset.x / PX_PER_EM) || 1,\n    r: Math.ceil(posOffset.y / PX_PER_EM) || 1\n  };\n\n  $highlighted.css({ width: dim.c + 'em', height: dim.r + 'em' });\n  $catcher.data('value', dim.c + 'x' + dim.r);\n\n  if (dim.c > 3 && dim.c < col) {\n    $unhighlighted.css({ width: dim.c + 1 + 'em' });\n  }\n\n  if (dim.r > 3 && dim.r < row) {\n    $unhighlighted.css({ height: dim.r + 1 + 'em' });\n  }\n\n  $dimensionDisplay.html(dim.c + ' x ' + dim.r);\n};\n\nconst tableDropdownButton = function(opt) {\n  return buttonGroup([\n    button({\n      className: 'dropdown-toggle',\n      contents: opt.title + ' ' + icon('note-icon-caret'),\n      tooltip: opt.tooltip,\n      data: {\n        toggle: 'dropdown'\n      }\n    }),\n    dropdown({\n      className: 'note-table',\n      items: [\n        '<div class=\"note-dimension-picker\">',\n        '  <div class=\"note-dimension-picker-mousecatcher\" data-event=\"insertTable\" data-value=\"1x1\"/>',\n        '  <div class=\"note-dimension-picker-highlighted\"/>',\n        '  <div class=\"note-dimension-picker-unhighlighted\"/>',\n        '</div>',\n        '<div class=\"note-dimension-display\">1 x 1</div>'\n      ].join('')\n    })\n  ], {\n    callback: function($node) {\n      const $catcher = $node.find('.note-dimension-picker-mousecatcher');\n      $catcher.css({\n        width: opt.col + 'em',\n        height: opt.row + 'em'\n      })\n        .mousedown(opt.itemClick)\n        .mousemove(function(e) {\n          tableMoveHandler(e, opt.col, opt.row);\n        });\n    }\n  }).render();\n};\n\nconst palette = renderer.create('<div class=\"note-color-palette\"/>', function($node, options) {\n  const contents = [];\n  for (let row = 0, rowSize = options.colors.length; row < rowSize; row++) {\n    const eventName = options.eventName;\n    const colors = options.colors[row];\n    const colorsName = options.colorsName[row];\n    const buttons = [];\n    for (let col = 0, colSize = colors.length; col < colSize; col++) {\n      const color = colors[col];\n      const colorName = colorsName[col];\n      buttons.push([\n        '<button type=\"button\" class=\"note-btn note-color-btn\"',\n        'style=\"background-color:', color, '\" ',\n        'data-event=\"', eventName, '\" ',\n        'data-value=\"', color, '\" ',\n        'title=\"', colorName, '\" ',\n        'aria-label=\"', colorName, '\" ',\n        'data-toggle=\"button\" tabindex=\"-1\"></button>'\n      ].join(''));\n    }\n    contents.push('<div class=\"note-color-row\">' + buttons.join('') + '</div>');\n  }\n  $node.html(contents.join(''));\n\n  $node.find('.note-color-btn').each(function() {\n    $(this).data('_lite_tooltip', new TooltipUI($(this), {\n      container: options.container\n    }));\n  });\n});\n\nconst colorDropdownButton = function(opt, type) {\n  return buttonGroup({\n    className: 'note-color',\n    children: [\n      button({\n        className: 'note-current-color-button',\n        contents: opt.title,\n        tooltip: opt.lang.color.recent,\n        click: opt.currentClick,\n        callback: function($button) {\n          const $recentColor = $button.find('.note-recent-color');\n\n          if (type !== 'foreColor') {\n            $recentColor.css('background-color', '#FFFF00');\n            $button.attr('data-backColor', '#FFFF00');\n          }\n        }\n      }),\n      button({\n        className: 'dropdown-toggle',\n        contents: icon('note-icon-caret'),\n        tooltip: opt.lang.color.more,\n        data: {\n          toggle: 'dropdown'\n        }\n      }),\n      dropdown({\n        items: [\n          '<div>',\n          '<div class=\"note-btn-group btn-background-color\">',\n          '  <div class=\"note-palette-title\">' + opt.lang.color.background + '</div>',\n          '  <div>',\n          '<button type=\"button\" class=\"note-color-reset note-btn note-btn-block\" ' +\n          ' data-event=\"backColor\" data-value=\"inherit\">',\n          opt.lang.color.transparent,\n          '    </button>',\n          '  </div>',\n          '  <div class=\"note-holder\" data-event=\"backColor\"/>',\n          '  <div class=\"btn-sm\">',\n          '    <input type=\"color\" id=\"html5bcp\" class=\"note-btn btn-default\" value=\"#21104A\" style=\"width:100%;\" data-value=\"cp\">',\n          '    <button type=\"button\" class=\"note-color-reset btn\" data-event=\"backColor\" data-value=\"cpbackColor\">',\n          opt.lang.color.cpSelect,\n          '    </button>',\n          '  </div>',\n          '</div>',\n          '<div class=\"note-btn-group btn-foreground-color\">',\n          '  <div class=\"note-palette-title\">' + opt.lang.color.foreground + '</div>',\n          '  <div>',\n          '<button type=\"button\" class=\"note-color-reset note-btn note-btn-block\" ' +\n          ' data-event=\"removeFormat\" data-value=\"foreColor\">',\n          opt.lang.color.resetToDefault,\n          '    </button>',\n          '  </div>',\n          '  <div class=\"note-holder\" data-event=\"foreColor\"/>',\n          '  <div class=\"btn-sm\">',\n          '    <input type=\"color\" id=\"html5fcp\" class=\"note-btn btn-default\" value=\"#21104A\" style=\"width:100%;\" data-value=\"cp\">',\n          '    <button type=\"button\" class=\"note-color-reset btn\" data-event=\"foreColor\" data-value=\"cpforeColor\">',\n          opt.lang.color.cpSelect,\n          '    </button>',\n          '  </div>',\n          '</div>',\n          '</div>'\n        ].join(''),\n        callback: function($dropdown) {\n          $dropdown.find('.note-holder').each(function() {\n            const $holder = $(this);\n            $holder.append(palette({\n              colors: opt.colors,\n              eventName: $holder.data('event')\n            }).render());\n          });\n\n          if (type === 'fore') {\n            $dropdown.find('.btn-background-color').hide();\n            $dropdown.css({ 'min-width': '210px' });\n          } else if (type === 'back') {\n            $dropdown.find('.btn-foreground-color').hide();\n            $dropdown.css({ 'min-width': '210px' });\n          }\n        },\n        click: function(event) {\n          const $button = $(event.target);\n          const eventName = $button.data('event');\n          let value = $button.data('value');\n          const foreinput = document.getElementById('html5fcp').value;\n          const backinput = document.getElementById('html5bcp').value;\n          if (value === 'cp') {\n            event.stopPropagation();\n          } else if (value === 'cpbackColor') {\n            value = backinput;\n          } else if (value === 'cpforeColor') {\n            value = foreinput;\n          }\n\n          if (eventName && value) {\n            const key = eventName === 'backColor' ? 'background-color' : 'color';\n            const $color = $button.closest('.note-color').find('.note-recent-color');\n            const $currentButton = $button.closest('.note-color').find('.note-current-color-button');\n\n            $color.css(key, value);\n            $currentButton.attr('data-' + eventName, value);\n\n            if (type === 'fore') {\n              opt.itemClick('foreColor', value);\n            } else if (type === 'back') {\n              opt.itemClick('backColor', value);\n            } else {\n              opt.itemClick(eventName, value);\n            }\n          }\n        }\n      })\n    ]\n  }).render();\n};\n\nconst dialog = renderer.create('<div class=\"note-modal\" aria-hidden=\"false\" tabindex=\"-1\" role=\"dialog\"/>', function($node, options) {\n  if (options.fade) {\n    $node.addClass('fade');\n  }\n  $node.attr({\n    'aria-label': options.title\n  });\n  $node.html([\n    '  <div class=\"note-modal-content\">',\n    (options.title\n      ? '    <div class=\"note-modal-header\">' +\n    '      <button type=\"button\" class=\"close\" aria-label=\"Close\" aria-hidden=\"true\"><i class=\"note-icon-close\"></i></button>' +\n    '      <h4 class=\"note-modal-title\">' + options.title + '</h4>' +\n    '    </div>' : ''\n    ),\n    '    <div class=\"note-modal-body\">' + options.body + '</div>',\n    (options.footer\n      ? '    <div class=\"note-modal-footer\">' + options.footer + '</div>' : ''\n    ),\n    '  </div>'\n  ].join(''));\n\n  $node.data('modal', new ModalUI($node, options));\n});\n\nconst videoDialog = function(opt) {\n  const body = '<div class=\"note-form-group\">' +\n    '<label class=\"note-form-label\">' +\n    opt.lang.video.url + ' <small class=\"text-muted\">' +\n    opt.lang.video.providers + '</small>' +\n    '</label>' +\n    '<input class=\"note-video-url note-input\" type=\"text\" />' +\n    '</div>';\n  const footer = [\n    '<button type=\"button\" href=\"#\" class=\"note-btn note-btn-primary note-video-btn disabled\" disabled>',\n    opt.lang.video.insert,\n    '</button>'\n  ].join('');\n\n  return dialog({\n    title: opt.lang.video.insert,\n    fade: opt.fade,\n    body: body,\n    footer: footer\n  }).render();\n};\n\nconst imageDialog = function(opt) {\n  const body = '<div class=\"note-form-group note-group-select-from-files\">' +\n    '<label class=\"note-form-label\">' + opt.lang.image.selectFromFiles + '</label>' +\n    '<input class=\"note-note-image-input note-input\" type=\"file\" name=\"files\" accept=\"image/*\" multiple=\"multiple\" />' +\n    opt.imageLimitation +\n    '</div>' +\n    '<div class=\"note-form-group\" style=\"overflow:auto;\">' +\n    '<label class=\"note-form-label\">' + opt.lang.image.url + '</label>' +\n    '<input class=\"note-image-url note-input\" type=\"text\" />' +\n    '</div>';\n  const footer = [\n    '<button href=\"#\" type=\"button\" class=\"note-btn note-btn-primary note-btn-large note-image-btn disabled\" disabled>',\n    opt.lang.image.insert,\n    '</button>'\n  ].join('');\n\n  return dialog({\n    title: opt.lang.image.insert,\n    fade: opt.fade,\n    body: body,\n    footer: footer\n  }).render();\n};\n\nconst linkDialog = function(opt) {\n  const body = '<div class=\"note-form-group\">' +\n    '<label class=\"note-form-label\">' + opt.lang.link.textToDisplay + '</label>' +\n    '<input class=\"note-link-text note-input\" type=\"text\" />' +\n    '</div>' +\n    '<div class=\"note-form-group\">' +\n    '<label class=\"note-form-label\">' + opt.lang.link.url + '</label>' +\n    '<input class=\"note-link-url note-input\" type=\"text\" value=\"http://\" />' +\n    '</div>' +\n    (!opt.disableLinkTarget\n      ? '<div class=\"checkbox\">' +\n      '<label>' + '<input type=\"checkbox\" checked> ' + opt.lang.link.openInNewWindow + '</label>' +\n      '</div>' : ''\n    );\n  const footer = [\n    '<button href=\"#\" type=\"button\" class=\"note-btn note-btn-primary note-link-btn disabled\" disabled>',\n    opt.lang.link.insert,\n    '</button>'\n  ].join('');\n\n  return dialog({\n    className: 'link-dialog',\n    title: opt.lang.link.insert,\n    fade: opt.fade,\n    body: body,\n    footer: footer\n  }).render();\n};\n\nconst popover = renderer.create([\n  '<div class=\"note-popover bottom\">',\n  '  <div class=\"note-popover-arrow\"/>',\n  '  <div class=\"popover-content note-children-container\"/>',\n  '</div>'\n].join(''), function($node, options) {\n  const direction = typeof options.direction !== 'undefined' ? options.direction : 'bottom';\n\n  $node.addClass(direction).hide();\n\n  if (options.hideArrow) {\n    $node.find('.note-popover-arrow').hide();\n  }\n});\n\nconst checkbox = renderer.create('<div class=\"checkbox\"></div>', function($node, options) {\n  $node.html([\n    '<label' + (options.id ? ' for=\"' + options.id + '\"' : '') + '>',\n    ' <input role=\"checkbox\" type=\"checkbox\"' + (options.id ? ' id=\"' + options.id + '\"' : ''),\n    (options.checked ? ' checked' : ''),\n    ' aria-checked=\"' + (options.checked ? 'true' : 'false') + '\"/>',\n    (options.text ? options.text : ''),\n    '</label>'\n  ].join(''));\n});\n\nconst icon = function(iconClassName, tagName) {\n  tagName = tagName || 'i';\n  return '<' + tagName + ' class=\"' + iconClassName + '\"/>';\n};\n\nconst ui = {\n  editor: editor,\n  toolbar: toolbar,\n  editingArea: editingArea,\n  codable: codable,\n  editable: editable,\n  statusbar: statusbar,\n  airEditor: airEditor,\n  airEditable: airEditable,\n  buttonGroup: buttonGroup,\n  button: button,\n  dropdown: dropdown,\n  dropdownCheck: dropdownCheck,\n  dropdownButton: dropdownButton,\n  dropdownButtonContents: dropdownButtonContents,\n  dropdownCheckButton: dropdownCheckButton,\n  paragraphDropdownButton: paragraphDropdownButton,\n  tableDropdownButton: tableDropdownButton,\n  colorDropdownButton: colorDropdownButton,\n  palette: palette,\n  dialog: dialog,\n  videoDialog: videoDialog,\n  imageDialog: imageDialog,\n  linkDialog: linkDialog,\n  popover: popover,\n  checkbox: checkbox,\n  icon: icon,\n\n  toggleBtn: function($btn, isEnable) {\n    $btn.toggleClass('disabled', !isEnable);\n    $btn.attr('disabled', !isEnable);\n  },\n\n  toggleBtnActive: function($btn, isActive) {\n    $btn.toggleClass('active', isActive);\n  },\n\n  check: function($dom, value) {\n    $dom.find('.checked').removeClass('checked');\n    $dom.find('[data-value=\"' + value + '\"]').addClass('checked');\n  },\n\n  onDialogShown: function($dialog, handler) {\n    $dialog.one('note.modal.show', handler);\n  },\n\n  onDialogHidden: function($dialog, handler) {\n    $dialog.one('note.modal.hide', handler);\n  },\n\n  showDialog: function($dialog) {\n    $dialog.data('modal').show();\n  },\n\n  hideDialog: function($dialog) {\n    $dialog.data('modal').hide();\n  },\n\n  /**\n   * get popover content area\n   *\n   * @param $popover\n   * @returns {*}\n   */\n  getPopoverContent: function($popover) {\n    return $popover.find('.note-popover-content');\n  },\n\n  /**\n   * get dialog's body area\n   *\n   * @param $dialog\n   * @returns {*}\n   */\n  getDialogBody: function($dialog) {\n    return $dialog.find('.note-modal-body');\n  },\n\n  createLayout: function($note, options) {\n    const $editor = (options.airMode ? ui.airEditor([\n      ui.editingArea([\n        ui.airEditable()\n      ])\n    ]) : ui.editor([\n      ui.toolbar(),\n      ui.editingArea([\n        ui.codable(),\n        ui.editable()\n      ]),\n      ui.statusbar()\n    ])).render();\n\n    $editor.insertAfter($note);\n\n    return {\n      note: $note,\n      editor: $editor,\n      toolbar: $editor.find('.note-toolbar'),\n      editingArea: $editor.find('.note-editing-area'),\n      editable: $editor.find('.note-editable'),\n      codable: $editor.find('.note-codable'),\n      statusbar: $editor.find('.note-statusbar')\n    };\n  },\n\n  removeLayout: function($note, layoutInfo) {\n    $note.html(layoutInfo.editable.html());\n    layoutInfo.editor.remove();\n    $note.off('summernote'); // remove summernote custom event\n    $note.show();\n  }\n};\n\nexport default ui;\n", "import $ from 'jquery';\n\n$.summernote = $.summernote || {\n  lang: {}\n};\n\n$.extend($.summernote.lang, {\n  'en-US': {\n    font: {\n      bold: 'Bold',\n      italic: 'Italic',\n      underline: 'Underline',\n      clear: 'Remove Font Style',\n      height: 'Line Height',\n      name: 'Font Family',\n      strikethrough: 'Strikethrough',\n      subscript: 'Subscript',\n      superscript: 'Superscript',\n      size: 'Font Size'\n    },\n    image: {\n      image: 'Picture',\n      insert: 'Insert Image',\n      resizeFull: 'Resize Full',\n      resizeHalf: 'Resize Half',\n      resizeQuarter: 'Resize Quarter',\n      floatLeft: 'Float Left',\n      floatRight: 'Float Right',\n      floatNone: 'Float None',\n      shapeRounded: 'Shape: Rounded',\n      shapeCircle: 'Shape: Circle',\n      shapeThumbnail: 'Shape: Thumbnail',\n      shapeNone: 'Shape: None',\n      dragImageHere: 'Drag image or text here',\n      dropImage: 'Drop image or Text',\n      selectFromFiles: 'Select from files',\n      maximumFileSize: 'Maximum file size',\n      maximumFileSizeError: 'Maximum file size exceeded.',\n      url: 'Image URL',\n      remove: 'Remove Image',\n      original: 'Original'\n    },\n    video: {\n      video: 'Video',\n      videoLink: 'Video Link',\n      insert: 'Insert Video',\n      url: 'Video URL',\n      providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion or Youku)'\n    },\n    link: {\n      link: 'Link',\n      insert: 'Insert Link',\n      unlink: 'Unlink',\n      edit: 'Edit',\n      textToDisplay: 'Text to display',\n      url: 'To what URL should this link go?',\n      openInNewWindow: 'Open in new window'\n    },\n    table: {\n      table: 'Table',\n      addRowAbove: 'Add row above',\n      addRowBelow: 'Add row below',\n      addColLeft: 'Add column left',\n      addColRight: 'Add column right',\n      delRow: 'Delete row',\n      delCol: 'Delete column',\n      delTable: 'Delete table'\n    },\n    hr: {\n      insert: 'Insert Horizontal Rule'\n    },\n    style: {\n      style: 'Style',\n      p: 'Normal',\n      blockquote: 'Quote',\n      pre: 'Code',\n      h1: 'Header 1',\n      h2: 'Header 2',\n      h3: 'Header 3',\n      h4: 'Header 4',\n      h5: 'Header 5',\n      h6: 'Header 6'\n    },\n    lists: {\n      unordered: 'Unordered list',\n      ordered: 'Ordered list'\n    },\n    options: {\n      help: 'Help',\n      fullscreen: 'Full Screen',\n      codeview: 'Code View'\n    },\n    paragraph: {\n      paragraph: 'Paragraph',\n      outdent: 'Outdent',\n      indent: 'Indent',\n      left: 'Align left',\n      center: 'Align center',\n      right: 'Align right',\n      justify: 'Justify full'\n    },\n    color: {\n      recent: 'Recent Color',\n      more: 'More Color',\n      background: 'Background Color',\n      foreground: 'Foreground Color',\n      transparent: 'Transparent',\n      setTransparent: 'Set transparent',\n      reset: 'Reset',\n      resetToDefault: 'Reset to default',\n      cpSelect: 'Select'\n    },\n    shortcut: {\n      shortcuts: 'Keyboard shortcuts',\n      close: 'Close',\n      textFormatting: 'Text formatting',\n      action: 'Action',\n      paragraphFormatting: 'Paragraph formatting',\n      documentStyle: 'Document Style',\n      extraKeys: 'Extra keys'\n    },\n    help: {\n      'insertParagraph': 'Insert Paragraph',\n      'undo': 'Undoes the last command',\n      'redo': 'Redoes the last command',\n      'tab': 'Tab',\n      'untab': 'Untab',\n      'bold': 'Set a bold style',\n      'italic': 'Set a italic style',\n      'underline': 'Set a underline style',\n      'strikethrough': 'Set a strikethrough style',\n      'removeFormat': 'Clean a style',\n      'justifyLeft': 'Set left align',\n      'justifyCenter': 'Set center align',\n      'justifyRight': 'Set right align',\n      'justifyFull': 'Set full align',\n      'insertUnorderedList': 'Toggle unordered list',\n      'insertOrderedList': 'Toggle ordered list',\n      'outdent': 'Outdent on current paragraph',\n      'indent': 'Indent on current paragraph',\n      'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n      'formatH1': 'Change current block\\'s format as H1',\n      'formatH2': 'Change current block\\'s format as H2',\n      'formatH3': 'Change current block\\'s format as H3',\n      'formatH4': 'Change current block\\'s format as H4',\n      'formatH5': 'Change current block\\'s format as H5',\n      'formatH6': 'Change current block\\'s format as H6',\n      'insertHorizontalRule': 'Insert horizontal rule',\n      'linkDialog.show': 'Show Link Dialog'\n    },\n    history: {\n      undo: 'Undo',\n      redo: 'Redo'\n    },\n    specialChar: {\n      specialChar: 'SPECIAL CHARACTERS',\n      select: 'Select Special characters'\n    }\n  }\n});\n", "import $ from 'jquery';\nconst isSupportAmd = typeof define === 'function' && define.amd; // eslint-disable-line\n\n/**\n * returns whether font is installed or not.\n *\n * @param {String} fontName\n * @return {Boolean}\n */\nfunction isFontInstalled(fontName) {\n  const testFontName = fontName === 'Comic Sans MS' ? 'Courier New' : 'Comic Sans MS';\n  const $tester = $('<div>').css({\n    position: 'absolute',\n    left: '-9999px',\n    top: '-9999px',\n    fontSize: '200px'\n  }).text('mmmmmmmmmwwwwwww').appendTo(document.body);\n\n  const originalWidth = $tester.css('fontFamily', testFontName).width();\n  const width = $tester.css('fontFamily', fontName + ',' + testFontName).width();\n\n  $tester.remove();\n\n  return originalWidth !== width;\n}\n\nconst userAgent = navigator.userAgent;\nconst isMSIE = /MSIE|Trident/i.test(userAgent);\nlet browserVersion;\nif (isMSIE) {\n  let matches = /MSIE (\\d+[.]\\d+)/.exec(userAgent);\n  if (matches) {\n    browserVersion = parseFloat(matches[1]);\n  }\n  matches = /Trident\\/.*rv:([0-9]{1,}[.0-9]{0,})/.exec(userAgent);\n  if (matches) {\n    browserVersion = parseFloat(matches[1]);\n  }\n}\n\nconst isEdge = /Edge\\/\\d+/.test(userAgent);\n\nlet hasCodeMirror = !!window.CodeMirror;\nif (!hasCodeMirror && isSupportAmd) {\n  // Webpack\n  if (typeof __webpack_require__ === 'function') { // eslint-disable-line\n    try {\n      // If CodeMirror can't be resolved, `require.resolve` will throw an\n      // exception and `hasCodeMirror` won't be set to `true`.\n      require.resolve('codemirror');\n      hasCodeMirror = true;\n    } catch (e) {\n      // do nothing\n    }\n  } else if (typeof require !== 'undefined') {\n    // Browserify\n    if (typeof require.resolve !== 'undefined') {\n      try {\n        // If CodeMirror can't be resolved, `require.resolve` will throw an\n        // exception and `hasCodeMirror` won't be set to `true`.\n        require.resolve('codemirror');\n        hasCodeMirror = true;\n      } catch (e) {\n        // do nothing\n      }\n    // Almond/Require\n    } else if (typeof require.specified !== 'undefined') {\n      hasCodeMirror = require.specified('codemirror');\n    }\n  }\n}\n\nconst isSupportTouch =\n  (('ontouchstart' in window) ||\n   (navigator.MaxTouchPoints > 0) ||\n   (navigator.msMaxTouchPoints > 0));\n\n// [workaround] IE doesn't have input events for contentEditable\n// - see: https://goo.gl/4bfIvA\nconst inputEventName = (isMSIE || isEdge) ? 'DOMCharacterDataModified DOMSubtreeModified DOMNodeInserted' : 'input';\n\n/**\n * @class core.env\n *\n * Object which check platform and agent\n *\n * @singleton\n * @alternateClassName env\n */\nexport default {\n  isMac: navigator.appVersion.indexOf('Mac') > -1,\n  isMSIE,\n  isEdge,\n  isFF: !isEdge && /firefox/i.test(userAgent),\n  isPhantom: /PhantomJS/i.test(userAgent),\n  isWebkit: !isEdge && /webkit/i.test(userAgent),\n  isChrome: !isEdge && /chrome/i.test(userAgent),\n  isSafari: !isEdge && /safari/i.test(userAgent),\n  browserVersion,\n  jqueryVersion: parseFloat($.fn.jquery),\n  isSupportAmd,\n  isSupportTouch,\n  hasCodeMirror,\n  isFontInstalled,\n  isW3CRangeSupport: !!document.createRange,\n  inputEventName\n};\n", "/**\n * @class core.func\n *\n * func utils (for high-order func's arg)\n *\n * @singleton\n * @alternateClassName func\n */\nfunction eq(itemA) {\n  return function(itemB) {\n    return itemA === itemB;\n  };\n}\n\nfunction eq2(itemA, itemB) {\n  return itemA === itemB;\n}\n\nfunction peq2(propName) {\n  return function(itemA, itemB) {\n    return itemA[propName] === itemB[propName];\n  };\n}\n\nfunction ok() {\n  return true;\n}\n\nfunction fail() {\n  return false;\n}\n\nfunction not(f) {\n  return function() {\n    return !f.apply(f, arguments);\n  };\n}\n\nfunction and(fA, fB) {\n  return function(item) {\n    return fA(item) && fB(item);\n  };\n}\n\nfunction self(a) {\n  return a;\n}\n\nfunction invoke(obj, method) {\n  return function() {\n    return obj[method].apply(obj, arguments);\n  };\n}\n\nlet idCounter = 0;\n\n/**\n * generate a globally-unique id\n *\n * @param {String} [prefix]\n */\nfunction uniqueId(prefix) {\n  const id = ++idCounter + '';\n  return prefix ? prefix + id : id;\n}\n\n/**\n * returns bnd (bounds) from rect\n *\n * - IE Compatibility Issue: http://goo.gl/sRLOAo\n * - Scroll Issue: http://goo.gl/sNjUc\n *\n * @param {Rect} rect\n * @return {Object} bounds\n * @return {Number} bounds.top\n * @return {Number} bounds.left\n * @return {Number} bounds.width\n * @return {Number} bounds.height\n */\nfunction rect2bnd(rect) {\n  const $document = $(document);\n  return {\n    top: rect.top + $document.scrollTop(),\n    left: rect.left + $document.scrollLeft(),\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n}\n\n/**\n * returns a copy of the object where the keys have become the values and the values the keys.\n * @param {Object} obj\n * @return {Object}\n */\nfunction invertObject(obj) {\n  const inverted = {};\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      inverted[obj[key]] = key;\n    }\n  }\n  return inverted;\n}\n\n/**\n * @param {String} namespace\n * @param {String} [prefix]\n * @return {String}\n */\nfunction namespaceToCamel(namespace, prefix) {\n  prefix = prefix || '';\n  return prefix + namespace.split('.').map(function(name) {\n    return name.substring(0, 1).toUpperCase() + name.substring(1);\n  }).join('');\n}\n\n/**\n * Returns a function, that, as long as it continues to be invoked, will not\n * be triggered. The function will be called after it stops being called for\n * N milliseconds. If `immediate` is passed, trigger the function on the\n * leading edge, instead of the trailing.\n * @param {Function} func\n * @param {Number} wait\n * @param {Boolean} immediate\n * @return {Function}\n */\nfunction debounce(func, wait, immediate) {\n  let timeout;\n  return function() {\n    const context = this;\n    const args = arguments;\n    const later = () => {\n      timeout = null;\n      if (!immediate) {\n        func.apply(context, args);\n      }\n    };\n    const callNow = immediate && !timeout;\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n    if (callNow) {\n      func.apply(context, args);\n    }\n  };\n}\n\nexport default {\n  eq,\n  eq2,\n  peq2,\n  ok,\n  fail,\n  self,\n  not,\n  and,\n  invoke,\n  uniqueId,\n  rect2bnd,\n  invertObject,\n  namespaceToCamel,\n  debounce\n};\n", "import $ from 'jquery';\nimport func from './func';\n\n/**\n * returns the first item of an array.\n *\n * @param {Array} array\n */\nfunction head(array) {\n  return array[0];\n}\n\n/**\n * returns the last item of an array.\n *\n * @param {Array} array\n */\nfunction last(array) {\n  return array[array.length - 1];\n}\n\n/**\n * returns everything but the last entry of the array.\n *\n * @param {Array} array\n */\nfunction initial(array) {\n  return array.slice(0, array.length - 1);\n}\n\n/**\n * returns the rest of the items in an array.\n *\n * @param {Array} array\n */\nfunction tail(array) {\n  return array.slice(1);\n}\n\n/**\n * returns item of array\n */\nfunction find(array, pred) {\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    const item = array[idx];\n    if (pred(item)) {\n      return item;\n    }\n  }\n}\n\n/**\n * returns true if all of the values in the array pass the predicate truth test.\n */\nfunction all(array, pred) {\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    if (!pred(array[idx])) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * returns index of item\n */\nfunction indexOf(array, item) {\n  return $.inArray(item, array);\n}\n\n/**\n * returns true if the value is present in the list.\n */\nfunction contains(array, item) {\n  return indexOf(array, item) !== -1;\n}\n\n/**\n * get sum from a list\n *\n * @param {Array} array - array\n * @param {Function} fn - iterator\n */\nfunction sum(array, fn) {\n  fn = fn || func.self;\n  return array.reduce(function(memo, v) {\n    return memo + fn(v);\n  }, 0);\n}\n\n/**\n * returns a copy of the collection with array type.\n * @param {Collection} collection - collection eg) node.childNodes, ...\n */\nfunction from(collection) {\n  const result = [];\n  const length = collection.length;\n  let idx = -1;\n  while (++idx < length) {\n    result[idx] = collection[idx];\n  }\n  return result;\n}\n\n/**\n * returns whether list is empty or not\n */\nfunction isEmpty(array) {\n  return !array || !array.length;\n}\n\n/**\n * cluster elements by predicate function.\n *\n * @param {Array} array - array\n * @param {Function} fn - predicate function for cluster rule\n * @param {Array[]}\n */\nfunction clusterBy(array, fn) {\n  if (!array.length) { return []; }\n  const aTail = tail(array);\n  return aTail.reduce(function(memo, v) {\n    const aLast = last(memo);\n    if (fn(last(aLast), v)) {\n      aLast[aLast.length] = v;\n    } else {\n      memo[memo.length] = [v];\n    }\n    return memo;\n  }, [[head(array)]]);\n}\n\n/**\n * returns a copy of the array with all false values removed\n *\n * @param {Array} array - array\n * @param {Function} fn - predicate function for cluster rule\n */\nfunction compact(array) {\n  const aResult = [];\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    if (array[idx]) { aResult.push(array[idx]); }\n  }\n  return aResult;\n}\n\n/**\n * produces a duplicate-free version of the array\n *\n * @param {Array} array\n */\nfunction unique(array) {\n  const results = [];\n\n  for (let idx = 0, len = array.length; idx < len; idx++) {\n    if (!contains(results, array[idx])) {\n      results.push(array[idx]);\n    }\n  }\n\n  return results;\n}\n\n/**\n * returns next item.\n * @param {Array} array\n */\nfunction next(array, item) {\n  const idx = indexOf(array, item);\n  if (idx === -1) { return null; }\n\n  return array[idx + 1];\n}\n\n/**\n * returns prev item.\n * @param {Array} array\n */\nfunction prev(array, item) {\n  const idx = indexOf(array, item);\n  if (idx === -1) { return null; }\n\n  return array[idx - 1];\n}\n\n/**\n * @class core.list\n *\n * list utils\n *\n * @singleton\n * @alternateClassName list\n */\nexport default {\n  head,\n  last,\n  initial,\n  tail,\n  prev,\n  next,\n  find,\n  contains,\n  all,\n  sum,\n  from,\n  isEmpty,\n  clusterBy,\n  compact,\n  unique\n};\n", "import lists from './lists';\nimport func from './func';\n\nconst KEY_MAP = {\n  'BACKSPACE': 8,\n  'TAB': 9,\n  'ENTER': 13,\n  'SPACE': 32,\n  'DELETE': 46,\n\n  // Arrow\n  'LEFT': 37,\n  'UP': 38,\n  'RIGHT': 39,\n  'DOWN': 40,\n\n  // Number: 0-9\n  'NUM0': 48,\n  'NUM1': 49,\n  'NUM2': 50,\n  'NUM3': 51,\n  'NUM4': 52,\n  'NUM5': 53,\n  'NUM6': 54,\n  'NUM7': 55,\n  'NUM8': 56,\n\n  // Alphabet: a-z\n  'B': 66,\n  'E': 69,\n  'I': 73,\n  'J': 74,\n  'K': 75,\n  'L': 76,\n  'R': 82,\n  'S': 83,\n  'U': 85,\n  'V': 86,\n  'Y': 89,\n  'Z': 90,\n\n  'SLASH': 191,\n  'LEFTBRACKET': 219,\n  'BACKSLASH': 220,\n  'RIGHTBRACKET': 221\n};\n\n/**\n * @class core.key\n *\n * Object for keycodes.\n *\n * @singleton\n * @alternateClassName key\n */\nexport default {\n  /**\n   * @method isEdit\n   *\n   * @param {Number} keyCode\n   * @return {Boolean}\n   */\n  isEdit: (keyCode) => {\n    return lists.contains([\n      KEY_MAP.BACKSPACE,\n      KEY_MAP.TAB,\n      KEY_MAP.ENTER,\n      KEY_MAP.SPACE,\n      KEY_MAP.DELETE\n    ], keyCode);\n  },\n  /**\n   * @method isMove\n   *\n   * @param {Number} keyCode\n   * @return {Boolean}\n   */\n  isMove: (keyCode) => {\n    return lists.contains([\n      KEY_MAP.LEFT,\n      KEY_MAP.UP,\n      KEY_MAP.RIGHT,\n      KEY_MAP.DOWN\n    ], keyCode);\n  },\n  /**\n   * @property {Object} nameFromCode\n   * @property {String} nameFromCode.8 \"BACKSPACE\"\n   */\n  nameFromCode: func.invertObject(KEY_MAP),\n  code: KEY_MAP\n};\n", "import $ from 'jquery';\nimport func from './func';\nimport lists from './lists';\nimport env from './env';\n\nconst NBSP_CHAR = String.fromCharCode(160);\nconst ZERO_WIDTH_NBSP_CHAR = '\\ufeff';\n\n/**\n * @method isEditable\n *\n * returns whether node is `note-editable` or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction isEditable(node) {\n  return node && $(node).hasClass('note-editable');\n}\n\n/**\n * @method isControlSizing\n *\n * returns whether node is `note-control-sizing` or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction isControlSizing(node) {\n  return node && $(node).hasClass('note-control-sizing');\n}\n\n/**\n * @method makePredByNodeName\n *\n * returns predicate which judge whether nodeName is same\n *\n * @param {String} nodeName\n * @return {Function}\n */\nfunction makePredByNodeName(nodeName) {\n  nodeName = nodeName.toUpperCase();\n  return function(node) {\n    return node && node.nodeName.toUpperCase() === nodeName;\n  };\n}\n\n/**\n * @method isText\n *\n *\n *\n * @param {Node} node\n * @return {Boolean} true if node's type is text(3)\n */\nfunction isText(node) {\n  return node && node.nodeType === 3;\n}\n\n/**\n * @method isElement\n *\n *\n *\n * @param {Node} node\n * @return {Boolean} true if node's type is element(1)\n */\nfunction isElement(node) {\n  return node && node.nodeType === 1;\n}\n\n/**\n * ex) br, col, embed, hr, img, input, ...\n * @see http://www.w3.org/html/wg/drafts/html/master/syntax.html#void-elements\n */\nfunction isVoid(node) {\n  return node && /^BR|^IMG|^HR|^IFRAME|^BUTTON|^INPUT|^VIDEO|^EMBED/.test(node.nodeName.toUpperCase());\n}\n\nfunction isPara(node) {\n  if (isEditable(node)) {\n    return false;\n  }\n\n  // Chrome(v31.0), FF(v25.0.1) use DIV for paragraph\n  return node && /^DIV|^P|^LI|^H[1-7]/.test(node.nodeName.toUpperCase());\n}\n\nfunction isHeading(node) {\n  return node && /^H[1-7]/.test(node.nodeName.toUpperCase());\n}\n\nconst isPre = makePredByNodeName('PRE');\n\nconst isLi = makePredByNodeName('LI');\n\nfunction isPurePara(node) {\n  return isPara(node) && !isLi(node);\n}\n\nconst isTable = makePredByNodeName('TABLE');\n\nconst isData = makePredByNodeName('DATA');\n\nfunction isInline(node) {\n  return !isBodyContainer(node) &&\n         !isList(node) &&\n         !isHr(node) &&\n         !isPara(node) &&\n         !isTable(node) &&\n         !isBlockquote(node) &&\n         !isData(node);\n}\n\nfunction isList(node) {\n  return node && /^UL|^OL/.test(node.nodeName.toUpperCase());\n}\n\nconst isHr = makePredByNodeName('HR');\n\nfunction isCell(node) {\n  return node && /^TD|^TH/.test(node.nodeName.toUpperCase());\n}\n\nconst isBlockquote = makePredByNodeName('BLOCKQUOTE');\n\nfunction isBodyContainer(node) {\n  return isCell(node) || isBlockquote(node) || isEditable(node);\n}\n\nconst isAnchor = makePredByNodeName('A');\n\nfunction isParaInline(node) {\n  return isInline(node) && !!ancestor(node, isPara);\n}\n\nfunction isBodyInline(node) {\n  return isInline(node) && !ancestor(node, isPara);\n}\n\nconst isBody = makePredByNodeName('BODY');\n\n/**\n * returns whether nodeB is closest sibling of nodeA\n *\n * @param {Node} nodeA\n * @param {Node} nodeB\n * @return {Boolean}\n */\nfunction isClosestSibling(nodeA, nodeB) {\n  return nodeA.nextSibling === nodeB ||\n         nodeA.previousSibling === nodeB;\n}\n\n/**\n * returns array of closest siblings with node\n *\n * @param {Node} node\n * @param {function} [pred] - predicate function\n * @return {Node[]}\n */\nfunction withClosestSiblings(node, pred) {\n  pred = pred || func.ok;\n\n  const siblings = [];\n  if (node.previousSibling && pred(node.previousSibling)) {\n    siblings.push(node.previousSibling);\n  }\n  siblings.push(node);\n  if (node.nextSibling && pred(node.nextSibling)) {\n    siblings.push(node.nextSibling);\n  }\n  return siblings;\n}\n\n/**\n * blank HTML for cursor position\n * - [workaround] old IE only works with &nbsp;\n * - [workaround] IE11 and other browser works with bogus br\n */\nconst blankHTML = env.isMSIE && env.browserVersion < 11 ? '&nbsp;' : '<br>';\n\n/**\n * @method nodeLength\n *\n * returns #text's text size or element's childNodes size\n *\n * @param {Node} node\n */\nfunction nodeLength(node) {\n  if (isText(node)) {\n    return node.nodeValue.length;\n  }\n\n  if (node) {\n    return node.childNodes.length;\n  }\n\n  return 0;\n}\n\n/**\n * returns whether node is empty or not.\n *\n * @param {Node} node\n * @return {Boolean}\n */\nfunction isEmpty(node) {\n  const len = nodeLength(node);\n\n  if (len === 0) {\n    return true;\n  } else if (!isText(node) && len === 1 && node.innerHTML === blankHTML) {\n    // ex) <p><br></p>, <span><br></span>\n    return true;\n  } else if (lists.all(node.childNodes, isText) && node.innerHTML === '') {\n    // ex) <p></p>, <span></span>\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * padding blankHTML if node is empty (for cursor position)\n */\nfunction paddingBlankHTML(node) {\n  if (!isVoid(node) && !nodeLength(node)) {\n    node.innerHTML = blankHTML;\n  }\n}\n\n/**\n * find nearest ancestor predicate hit\n *\n * @param {Node} node\n * @param {Function} pred - predicate function\n */\nfunction ancestor(node, pred) {\n  while (node) {\n    if (pred(node)) { return node; }\n    if (isEditable(node)) { break; }\n\n    node = node.parentNode;\n  }\n  return null;\n}\n\n/**\n * find nearest ancestor only single child blood line and predicate hit\n *\n * @param {Node} node\n * @param {Function} pred - predicate function\n */\nfunction singleChildAncestor(node, pred) {\n  node = node.parentNode;\n\n  while (node) {\n    if (nodeLength(node) !== 1) { break; }\n    if (pred(node)) { return node; }\n    if (isEditable(node)) { break; }\n\n    node = node.parentNode;\n  }\n  return null;\n}\n\n/**\n * returns new array of ancestor nodes (until predicate hit).\n *\n * @param {Node} node\n * @param {Function} [optional] pred - predicate function\n */\nfunction listAncestor(node, pred) {\n  pred = pred || func.fail;\n\n  const ancestors = [];\n  ancestor(node, function(el) {\n    if (!isEditable(el)) {\n      ancestors.push(el);\n    }\n\n    return pred(el);\n  });\n  return ancestors;\n}\n\n/**\n * find farthest ancestor predicate hit\n */\nfunction lastAncestor(node, pred) {\n  const ancestors = listAncestor(node);\n  return lists.last(ancestors.filter(pred));\n}\n\n/**\n * returns common ancestor node between two nodes.\n *\n * @param {Node} nodeA\n * @param {Node} nodeB\n */\nfunction commonAncestor(nodeA, nodeB) {\n  const ancestors = listAncestor(nodeA);\n  for (let n = nodeB; n; n = n.parentNode) {\n    if ($.inArray(n, ancestors) > -1) { return n; }\n  }\n  return null; // difference document area\n}\n\n/**\n * listing all previous siblings (until predicate hit).\n *\n * @param {Node} node\n * @param {Function} [optional] pred - predicate function\n */\nfunction listPrev(node, pred) {\n  pred = pred || func.fail;\n\n  const nodes = [];\n  while (node) {\n    if (pred(node)) { break; }\n    nodes.push(node);\n    node = node.previousSibling;\n  }\n  return nodes;\n}\n\n/**\n * listing next siblings (until predicate hit).\n *\n * @param {Node} node\n * @param {Function} [pred] - predicate function\n */\nfunction listNext(node, pred) {\n  pred = pred || func.fail;\n\n  const nodes = [];\n  while (node) {\n    if (pred(node)) { break; }\n    nodes.push(node);\n    node = node.nextSibling;\n  }\n  return nodes;\n}\n\n/**\n * listing descendant nodes\n *\n * @param {Node} node\n * @param {Function} [pred] - predicate function\n */\nfunction listDescendant(node, pred) {\n  const descendants = [];\n  pred = pred || func.ok;\n\n  // start DFS(depth first search) with node\n  (function fnWalk(current) {\n    if (node !== current && pred(current)) {\n      descendants.push(current);\n    }\n    for (let idx = 0, len = current.childNodes.length; idx < len; idx++) {\n      fnWalk(current.childNodes[idx]);\n    }\n  })(node);\n\n  return descendants;\n}\n\n/**\n * wrap node with new tag.\n *\n * @param {Node} node\n * @param {Node} tagName of wrapper\n * @return {Node} - wrapper\n */\nfunction wrap(node, wrapperName) {\n  const parent = node.parentNode;\n  const wrapper = $('<' + wrapperName + '>')[0];\n\n  parent.insertBefore(wrapper, node);\n  wrapper.appendChild(node);\n\n  return wrapper;\n}\n\n/**\n * insert node after preceding\n *\n * @param {Node} node\n * @param {Node} preceding - predicate function\n */\nfunction insertAfter(node, preceding) {\n  const next = preceding.nextSibling;\n  let parent = preceding.parentNode;\n  if (next) {\n    parent.insertBefore(node, next);\n  } else {\n    parent.appendChild(node);\n  }\n  return node;\n}\n\n/**\n * append elements.\n *\n * @param {Node} node\n * @param {Collection} aChild\n */\nfunction appendChildNodes(node, aChild) {\n  $.each(aChild, function(idx, child) {\n    node.appendChild(child);\n  });\n  return node;\n}\n\n/**\n * returns whether boundaryPoint is left edge or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isLeftEdgePoint(point) {\n  return point.offset === 0;\n}\n\n/**\n * returns whether boundaryPoint is right edge or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isRightEdgePoint(point) {\n  return point.offset === nodeLength(point.node);\n}\n\n/**\n * returns whether boundaryPoint is edge or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isEdgePoint(point) {\n  return isLeftEdgePoint(point) || isRightEdgePoint(point);\n}\n\n/**\n * returns whether node is left edge of ancestor or not.\n *\n * @param {Node} node\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isLeftEdgeOf(node, ancestor) {\n  while (node && node !== ancestor) {\n    if (position(node) !== 0) {\n      return false;\n    }\n    node = node.parentNode;\n  }\n\n  return true;\n}\n\n/**\n * returns whether node is right edge of ancestor or not.\n *\n * @param {Node} node\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isRightEdgeOf(node, ancestor) {\n  if (!ancestor) {\n    return false;\n  }\n  while (node && node !== ancestor) {\n    if (position(node) !== nodeLength(node.parentNode) - 1) {\n      return false;\n    }\n    node = node.parentNode;\n  }\n\n  return true;\n}\n\n/**\n * returns whether point is left edge of ancestor or not.\n * @param {BoundaryPoint} point\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isLeftEdgePointOf(point, ancestor) {\n  return isLeftEdgePoint(point) && isLeftEdgeOf(point.node, ancestor);\n}\n\n/**\n * returns whether point is right edge of ancestor or not.\n * @param {BoundaryPoint} point\n * @param {Node} ancestor\n * @return {Boolean}\n */\nfunction isRightEdgePointOf(point, ancestor) {\n  return isRightEdgePoint(point) && isRightEdgeOf(point.node, ancestor);\n}\n\n/**\n * returns offset from parent.\n *\n * @param {Node} node\n */\nfunction position(node) {\n  let offset = 0;\n  while ((node = node.previousSibling)) {\n    offset += 1;\n  }\n  return offset;\n}\n\nfunction hasChildren(node) {\n  return !!(node && node.childNodes && node.childNodes.length);\n}\n\n/**\n * returns previous boundaryPoint\n *\n * @param {BoundaryPoint} point\n * @param {Boolean} isSkipInnerOffset\n * @return {BoundaryPoint}\n */\nfunction prevPoint(point, isSkipInnerOffset) {\n  let node;\n  let offset;\n\n  if (point.offset === 0) {\n    if (isEditable(point.node)) {\n      return null;\n    }\n\n    node = point.node.parentNode;\n    offset = position(point.node);\n  } else if (hasChildren(point.node)) {\n    node = point.node.childNodes[point.offset - 1];\n    offset = nodeLength(node);\n  } else {\n    node = point.node;\n    offset = isSkipInnerOffset ? 0 : point.offset - 1;\n  }\n\n  return {\n    node: node,\n    offset: offset\n  };\n}\n\n/**\n * returns next boundaryPoint\n *\n * @param {BoundaryPoint} point\n * @param {Boolean} isSkipInnerOffset\n * @return {BoundaryPoint}\n */\nfunction nextPoint(point, isSkipInnerOffset) {\n  let node, offset;\n\n  if (nodeLength(point.node) === point.offset) {\n    if (isEditable(point.node)) {\n      return null;\n    }\n\n    node = point.node.parentNode;\n    offset = position(point.node) + 1;\n  } else if (hasChildren(point.node)) {\n    node = point.node.childNodes[point.offset];\n    offset = 0;\n  } else {\n    node = point.node;\n    offset = isSkipInnerOffset ? nodeLength(point.node) : point.offset + 1;\n  }\n\n  return {\n    node: node,\n    offset: offset\n  };\n}\n\n/**\n * returns whether pointA and pointB is same or not.\n *\n * @param {BoundaryPoint} pointA\n * @param {BoundaryPoint} pointB\n * @return {Boolean}\n */\nfunction isSamePoint(pointA, pointB) {\n  return pointA.node === pointB.node && pointA.offset === pointB.offset;\n}\n\n/**\n * returns whether point is visible (can set cursor) or not.\n *\n * @param {BoundaryPoint} point\n * @return {Boolean}\n */\nfunction isVisiblePoint(point) {\n  if (isText(point.node) || !hasChildren(point.node) || isEmpty(point.node)) {\n    return true;\n  }\n\n  const leftNode = point.node.childNodes[point.offset - 1];\n  const rightNode = point.node.childNodes[point.offset];\n  if ((!leftNode || isVoid(leftNode)) && (!rightNode || isVoid(rightNode))) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * @method prevPointUtil\n *\n * @param {BoundaryPoint} point\n * @param {Function} pred\n * @return {BoundaryPoint}\n */\nfunction prevPointUntil(point, pred) {\n  while (point) {\n    if (pred(point)) {\n      return point;\n    }\n\n    point = prevPoint(point);\n  }\n\n  return null;\n}\n\n/**\n * @method nextPointUntil\n *\n * @param {BoundaryPoint} point\n * @param {Function} pred\n * @return {BoundaryPoint}\n */\nfunction nextPointUntil(point, pred) {\n  while (point) {\n    if (pred(point)) {\n      return point;\n    }\n\n    point = nextPoint(point);\n  }\n\n  return null;\n}\n\n/**\n * returns whether point has character or not.\n *\n * @param {Point} point\n * @return {Boolean}\n */\nfunction isCharPoint(point) {\n  if (!isText(point.node)) {\n    return false;\n  }\n\n  const ch = point.node.nodeValue.charAt(point.offset - 1);\n  return ch && (ch !== ' ' && ch !== NBSP_CHAR);\n}\n\n/**\n * @method walkPoint\n *\n * @param {BoundaryPoint} startPoint\n * @param {BoundaryPoint} endPoint\n * @param {Function} handler\n * @param {Boolean} isSkipInnerOffset\n */\nfunction walkPoint(startPoint, endPoint, handler, isSkipInnerOffset) {\n  let point = startPoint;\n\n  while (point) {\n    handler(point);\n\n    if (isSamePoint(point, endPoint)) {\n      break;\n    }\n\n    const isSkipOffset = isSkipInnerOffset &&\n                       startPoint.node !== point.node &&\n                       endPoint.node !== point.node;\n    point = nextPoint(point, isSkipOffset);\n  }\n}\n\n/**\n * @method makeOffsetPath\n *\n * return offsetPath(array of offset) from ancestor\n *\n * @param {Node} ancestor - ancestor node\n * @param {Node} node\n */\nfunction makeOffsetPath(ancestor, node) {\n  const ancestors = listAncestor(node, func.eq(ancestor));\n  return ancestors.map(position).reverse();\n}\n\n/**\n * @method fromOffsetPath\n *\n * return element from offsetPath(array of offset)\n *\n * @param {Node} ancestor - ancestor node\n * @param {array} offsets - offsetPath\n */\nfunction fromOffsetPath(ancestor, offsets) {\n  let current = ancestor;\n  for (let i = 0, len = offsets.length; i < len; i++) {\n    if (current.childNodes.length <= offsets[i]) {\n      current = current.childNodes[current.childNodes.length - 1];\n    } else {\n      current = current.childNodes[offsets[i]];\n    }\n  }\n  return current;\n}\n\n/**\n * @method splitNode\n *\n * split element or #text\n *\n * @param {BoundaryPoint} point\n * @param {Object} [options]\n * @param {Boolean} [options.isSkipPaddingBlankHTML] - default: false\n * @param {Boolean} [options.isNotSplitEdgePoint] - default: false\n * @param {Boolean} [options.isDiscardEmptySplits] - default: false\n * @return {Node} right node of boundaryPoint\n */\nfunction splitNode(point, options) {\n  let isSkipPaddingBlankHTML = options && options.isSkipPaddingBlankHTML;\n  const isNotSplitEdgePoint = options && options.isNotSplitEdgePoint;\n  const isDiscardEmptySplits = options && options.isDiscardEmptySplits;\n\n  if (isDiscardEmptySplits) {\n    isSkipPaddingBlankHTML = true;\n  }\n\n  // edge case\n  if (isEdgePoint(point) && (isText(point.node) || isNotSplitEdgePoint)) {\n    if (isLeftEdgePoint(point)) {\n      return point.node;\n    } else if (isRightEdgePoint(point)) {\n      return point.node.nextSibling;\n    }\n  }\n\n  // split #text\n  if (isText(point.node)) {\n    return point.node.splitText(point.offset);\n  } else {\n    const childNode = point.node.childNodes[point.offset];\n    const clone = insertAfter(point.node.cloneNode(false), point.node);\n    appendChildNodes(clone, listNext(childNode));\n\n    if (!isSkipPaddingBlankHTML) {\n      paddingBlankHTML(point.node);\n      paddingBlankHTML(clone);\n    }\n\n    if (isDiscardEmptySplits) {\n      if (isEmpty(point.node)) {\n        remove(point.node);\n      }\n      if (isEmpty(clone)) {\n        remove(clone);\n        return point.node.nextSibling;\n      }\n    }\n\n    return clone;\n  }\n}\n\n/**\n * @method splitTree\n *\n * split tree by point\n *\n * @param {Node} root - split root\n * @param {BoundaryPoint} point\n * @param {Object} [options]\n * @param {Boolean} [options.isSkipPaddingBlankHTML] - default: false\n * @param {Boolean} [options.isNotSplitEdgePoint] - default: false\n * @return {Node} right node of boundaryPoint\n */\nfunction splitTree(root, point, options) {\n  // ex) [#text, <span>, <p>]\n  const ancestors = listAncestor(point.node, func.eq(root));\n\n  if (!ancestors.length) {\n    return null;\n  } else if (ancestors.length === 1) {\n    return splitNode(point, options);\n  }\n\n  return ancestors.reduce(function(node, parent) {\n    if (node === point.node) {\n      node = splitNode(point, options);\n    }\n\n    return splitNode({\n      node: parent,\n      offset: node ? position(node) : nodeLength(parent)\n    }, options);\n  });\n}\n\n/**\n * split point\n *\n * @param {Point} point\n * @param {Boolean} isInline\n * @return {Object}\n */\nfunction splitPoint(point, isInline) {\n  // find splitRoot, container\n  //  - inline: splitRoot is a child of paragraph\n  //  - block: splitRoot is a child of bodyContainer\n  const pred = isInline ? isPara : isBodyContainer;\n  const ancestors = listAncestor(point.node, pred);\n  const topAncestor = lists.last(ancestors) || point.node;\n\n  let splitRoot, container;\n  if (pred(topAncestor)) {\n    splitRoot = ancestors[ancestors.length - 2];\n    container = topAncestor;\n  } else {\n    splitRoot = topAncestor;\n    container = splitRoot.parentNode;\n  }\n\n  // if splitRoot is exists, split with splitTree\n  let pivot = splitRoot && splitTree(splitRoot, point, {\n    isSkipPaddingBlankHTML: isInline,\n    isNotSplitEdgePoint: isInline\n  });\n\n  // if container is point.node, find pivot with point.offset\n  if (!pivot && container === point.node) {\n    pivot = point.node.childNodes[point.offset];\n  }\n\n  return {\n    rightNode: pivot,\n    container: container\n  };\n}\n\nfunction create(nodeName) {\n  return document.createElement(nodeName);\n}\n\nfunction createText(text) {\n  return document.createTextNode(text);\n}\n\n/**\n * @method remove\n *\n * remove node, (isRemoveChild: remove child or not)\n *\n * @param {Node} node\n * @param {Boolean} isRemoveChild\n */\nfunction remove(node, isRemoveChild) {\n  if (!node || !node.parentNode) { return; }\n  if (node.removeNode) { return node.removeNode(isRemoveChild); }\n\n  const parent = node.parentNode;\n  if (!isRemoveChild) {\n    const nodes = [];\n    for (let i = 0, len = node.childNodes.length; i < len; i++) {\n      nodes.push(node.childNodes[i]);\n    }\n\n    for (let i = 0, len = nodes.length; i < len; i++) {\n      parent.insertBefore(nodes[i], node);\n    }\n  }\n\n  parent.removeChild(node);\n}\n\n/**\n * @method removeWhile\n *\n * @param {Node} node\n * @param {Function} pred\n */\nfunction removeWhile(node, pred) {\n  while (node) {\n    if (isEditable(node) || !pred(node)) {\n      break;\n    }\n\n    const parent = node.parentNode;\n    remove(node);\n    node = parent;\n  }\n}\n\n/**\n * @method replace\n *\n * replace node with provided nodeName\n *\n * @param {Node} node\n * @param {String} nodeName\n * @return {Node} - new node\n */\nfunction replace(node, nodeName) {\n  if (node.nodeName.toUpperCase() === nodeName.toUpperCase()) {\n    return node;\n  }\n\n  const newNode = create(nodeName);\n\n  if (node.style.cssText) {\n    newNode.style.cssText = node.style.cssText;\n  }\n\n  appendChildNodes(newNode, lists.from(node.childNodes));\n  insertAfter(newNode, node);\n  remove(node);\n\n  return newNode;\n}\n\nconst isTextarea = makePredByNodeName('TEXTAREA');\n\n/**\n * @param {jQuery} $node\n * @param {Boolean} [stripLinebreaks] - default: false\n */\nfunction value($node, stripLinebreaks) {\n  const val = isTextarea($node[0]) ? $node.val() : $node.html();\n  if (stripLinebreaks) {\n    return val.replace(/[\\n\\r]/g, '');\n  }\n  return val;\n}\n\n/**\n * @method html\n *\n * get the HTML contents of node\n *\n * @param {jQuery} $node\n * @param {Boolean} [isNewlineOnBlock]\n */\nfunction html($node, isNewlineOnBlock) {\n  let markup = value($node);\n\n  if (isNewlineOnBlock) {\n    const regexTag = /<(\\/?)(\\b(?!!)[^>\\s]*)(.*?)(\\s*\\/?>)/g;\n    markup = markup.replace(regexTag, function(match, endSlash, name) {\n      name = name.toUpperCase();\n      const isEndOfInlineContainer = /^DIV|^TD|^TH|^P|^LI|^H[1-7]/.test(name) &&\n                                   !!endSlash;\n      const isBlockNode = /^BLOCKQUOTE|^TABLE|^TBODY|^TR|^HR|^UL|^OL/.test(name);\n\n      return match + ((isEndOfInlineContainer || isBlockNode) ? '\\n' : '');\n    });\n    markup = $.trim(markup);\n  }\n\n  return markup;\n}\n\nfunction posFromPlaceholder(placeholder) {\n  const $placeholder = $(placeholder);\n  const pos = $placeholder.offset();\n  const height = $placeholder.outerHeight(true); // include margin\n\n  return {\n    left: pos.left,\n    top: pos.top + height\n  };\n}\n\nfunction attachEvents($node, events) {\n  Object.keys(events).forEach(function(key) {\n    $node.on(key, events[key]);\n  });\n}\n\nfunction detachEvents($node, events) {\n  Object.keys(events).forEach(function(key) {\n    $node.off(key, events[key]);\n  });\n}\n\n/**\n * @method isCustomStyleTag\n *\n * assert if a node contains a \"note-styletag\" class,\n * which implies that's a custom-made style tag node\n *\n * @param {Node} an HTML DOM node\n */\nfunction isCustomStyleTag(node) {\n  return node && !isText(node) && lists.contains(node.classList, 'note-styletag');\n}\n\nexport default {\n  /** @property {String} NBSP_CHAR */\n  NBSP_CHAR,\n  /** @property {String} ZERO_WIDTH_NBSP_CHAR */\n  ZERO_WIDTH_NBSP_CHAR,\n  /** @property {String} blank */\n  blank: blankHTML,\n  /** @property {String} emptyPara */\n  emptyPara: `<p>${blankHTML}</p>`,\n  makePredByNodeName,\n  isEditable,\n  isControlSizing,\n  isText,\n  isElement,\n  isVoid,\n  isPara,\n  isPurePara,\n  isHeading,\n  isInline,\n  isBlock: func.not(isInline),\n  isBodyInline,\n  isBody,\n  isParaInline,\n  isPre,\n  isList,\n  isTable,\n  isData,\n  isCell,\n  isBlockquote,\n  isBodyContainer,\n  isAnchor,\n  isDiv: makePredByNodeName('DIV'),\n  isLi,\n  isBR: makePredByNodeName('BR'),\n  isSpan: makePredByNodeName('SPAN'),\n  isB: makePredByNodeName('B'),\n  isU: makePredByNodeName('U'),\n  isS: makePredByNodeName('S'),\n  isI: makePredByNodeName('I'),\n  isImg: makePredByNodeName('IMG'),\n  isTextarea,\n  isEmpty,\n  isEmptyAnchor: func.and(isAnchor, isEmpty),\n  isClosestSibling,\n  withClosestSiblings,\n  nodeLength,\n  isLeftEdgePoint,\n  isRightEdgePoint,\n  isEdgePoint,\n  isLeftEdgeOf,\n  isRightEdgeOf,\n  isLeftEdgePointOf,\n  isRightEdgePointOf,\n  prevPoint,\n  nextPoint,\n  isSamePoint,\n  isVisiblePoint,\n  prevPointUntil,\n  nextPointUntil,\n  isCharPoint,\n  walkPoint,\n  ancestor,\n  singleChildAncestor,\n  listAncestor,\n  lastAncestor,\n  listNext,\n  listPrev,\n  listDescendant,\n  commonAncestor,\n  wrap,\n  insertAfter,\n  appendChildNodes,\n  position,\n  hasChildren,\n  makeOffsetPath,\n  fromOffsetPath,\n  splitTree,\n  splitPoint,\n  create,\n  createText,\n  remove,\n  removeWhile,\n  replace,\n  html,\n  value,\n  posFromPlaceholder,\n  attachEvents,\n  detachEvents,\n  isCustomStyleTag\n};\n", "import $ from 'jquery';\nimport env from './env';\nimport func from './func';\nimport lists from './lists';\nimport dom from './dom';\n\n/**\n * return boundaryPoint from TextRange, inspired by <PERSON>'s HuskyRange.js\n *\n * @param {TextRange} textRange\n * @param {Boolean} isStart\n * @return {BoundaryPoint}\n *\n * @see http://msdn.microsoft.com/en-us/library/ie/ms535872(v=vs.85).aspx\n */\nfunction textRangeToPoint(textRange, isStart) {\n  let container = textRange.parentElement();\n  let offset;\n\n  const tester = document.body.createTextRange();\n  let prevContainer;\n  const childNodes = lists.from(container.childNodes);\n  for (offset = 0; offset < childNodes.length; offset++) {\n    if (dom.isText(childNodes[offset])) {\n      continue;\n    }\n    tester.moveToElementText(childNodes[offset]);\n    if (tester.compareEndPoints('StartToStart', textRange) >= 0) {\n      break;\n    }\n    prevContainer = childNodes[offset];\n  }\n\n  if (offset !== 0 && dom.isText(childNodes[offset - 1])) {\n    const textRangeStart = document.body.createTextRange();\n    let curTextNode = null;\n    textRangeStart.moveToElementText(prevContainer || container);\n    textRangeStart.collapse(!prevContainer);\n    curTextNode = prevContainer ? prevContainer.nextSibling : container.firstChild;\n\n    const pointTester = textRange.duplicate();\n    pointTester.setEndPoint('StartToStart', textRangeStart);\n    let textCount = pointTester.text.replace(/[\\r\\n]/g, '').length;\n\n    while (textCount > curTextNode.nodeValue.length && curTextNode.nextSibling) {\n      textCount -= curTextNode.nodeValue.length;\n      curTextNode = curTextNode.nextSibling;\n    }\n\n    // [workaround] enforce IE to re-reference curTextNode, hack\n    const dummy = curTextNode.nodeValue; // eslint-disable-line\n\n    if (isStart && curTextNode.nextSibling && dom.isText(curTextNode.nextSibling) &&\n      textCount === curTextNode.nodeValue.length) {\n      textCount -= curTextNode.nodeValue.length;\n      curTextNode = curTextNode.nextSibling;\n    }\n\n    container = curTextNode;\n    offset = textCount;\n  }\n\n  return {\n    cont: container,\n    offset: offset\n  };\n}\n\n/**\n * return TextRange from boundary point (inspired by google closure-library)\n * @param {BoundaryPoint} point\n * @return {TextRange}\n */\nfunction pointToTextRange(point) {\n  const textRangeInfo = function(container, offset) {\n    let node, isCollapseToStart;\n\n    if (dom.isText(container)) {\n      const prevTextNodes = dom.listPrev(container, func.not(dom.isText));\n      const prevContainer = lists.last(prevTextNodes).previousSibling;\n      node = prevContainer || container.parentNode;\n      offset += lists.sum(lists.tail(prevTextNodes), dom.nodeLength);\n      isCollapseToStart = !prevContainer;\n    } else {\n      node = container.childNodes[offset] || container;\n      if (dom.isText(node)) {\n        return textRangeInfo(node, 0);\n      }\n\n      offset = 0;\n      isCollapseToStart = false;\n    }\n\n    return {\n      node: node,\n      collapseToStart: isCollapseToStart,\n      offset: offset\n    };\n  };\n\n  const textRange = document.body.createTextRange();\n  const info = textRangeInfo(point.node, point.offset);\n\n  textRange.moveToElementText(info.node);\n  textRange.collapse(info.collapseToStart);\n  textRange.moveStart('character', info.offset);\n  return textRange;\n}\n\n/**\n   * Wrapped Range\n   *\n   * @constructor\n   * @param {Node} sc - start container\n   * @param {Number} so - start offset\n   * @param {Node} ec - end container\n   * @param {Number} eo - end offset\n   */\nclass WrappedRange {\n  constructor(sc, so, ec, eo) {\n    this.sc = sc;\n    this.so = so;\n    this.ec = ec;\n    this.eo = eo;\n\n    // isOnEditable: judge whether range is on editable or not\n    this.isOnEditable = this.makeIsOn(dom.isEditable);\n    // isOnList: judge whether range is on list node or not\n    this.isOnList = this.makeIsOn(dom.isList);\n    // isOnAnchor: judge whether range is on anchor node or not\n    this.isOnAnchor = this.makeIsOn(dom.isAnchor);\n    // isOnCell: judge whether range is on cell node or not\n    this.isOnCell = this.makeIsOn(dom.isCell);\n    // isOnData: judge whether range is on data node or not\n    this.isOnData = this.makeIsOn(dom.isData);\n  }\n\n  // nativeRange: get nativeRange from sc, so, ec, eo\n  nativeRange() {\n    if (env.isW3CRangeSupport) {\n      const w3cRange = document.createRange();\n      w3cRange.setStart(this.sc, this.so);\n      w3cRange.setEnd(this.ec, this.eo);\n\n      return w3cRange;\n    } else {\n      const textRange = pointToTextRange({\n        node: this.sc,\n        offset: this.so\n      });\n\n      textRange.setEndPoint('EndToEnd', pointToTextRange({\n        node: this.ec,\n        offset: this.eo\n      }));\n\n      return textRange;\n    }\n  }\n\n  getPoints() {\n    return {\n      sc: this.sc,\n      so: this.so,\n      ec: this.ec,\n      eo: this.eo\n    };\n  }\n\n  getStartPoint() {\n    return {\n      node: this.sc,\n      offset: this.so\n    };\n  }\n\n  getEndPoint() {\n    return {\n      node: this.ec,\n      offset: this.eo\n    };\n  }\n\n  /**\n   * select update visible range\n   */\n  select() {\n    const nativeRng = this.nativeRange();\n    if (env.isW3CRangeSupport) {\n      const selection = document.getSelection();\n      if (selection.rangeCount > 0) {\n        selection.removeAllRanges();\n      }\n      selection.addRange(nativeRng);\n    } else {\n      nativeRng.select();\n    }\n\n    return this;\n  }\n\n  /**\n   * Moves the scrollbar to start container(sc) of current range\n   *\n   * @return {WrappedRange}\n   */\n  scrollIntoView(container) {\n    const height = $(container).height();\n    if (container.scrollTop + height < this.sc.offsetTop) {\n      container.scrollTop += Math.abs(container.scrollTop + height - this.sc.offsetTop);\n    }\n\n    return this;\n  }\n\n  /**\n   * @return {WrappedRange}\n   */\n  normalize() {\n    /**\n     * @param {BoundaryPoint} point\n     * @param {Boolean} isLeftToRight\n     * @return {BoundaryPoint}\n     */\n    const getVisiblePoint = function(point, isLeftToRight) {\n      if ((dom.isVisiblePoint(point) && !dom.isEdgePoint(point)) ||\n          (dom.isVisiblePoint(point) && dom.isRightEdgePoint(point) && !isLeftToRight) ||\n          (dom.isVisiblePoint(point) && dom.isLeftEdgePoint(point) && isLeftToRight) ||\n          (dom.isVisiblePoint(point) && dom.isBlock(point.node) && dom.isEmpty(point.node))) {\n        return point;\n      }\n\n      // point on block's edge\n      const block = dom.ancestor(point.node, dom.isBlock);\n      if (((dom.isLeftEdgePointOf(point, block) || dom.isVoid(dom.prevPoint(point).node)) && !isLeftToRight) ||\n          ((dom.isRightEdgePointOf(point, block) || dom.isVoid(dom.nextPoint(point).node)) && isLeftToRight)) {\n        // returns point already on visible point\n        if (dom.isVisiblePoint(point)) {\n          return point;\n        }\n        // reverse direction\n        isLeftToRight = !isLeftToRight;\n      }\n\n      const nextPoint = isLeftToRight ? dom.nextPointUntil(dom.nextPoint(point), dom.isVisiblePoint)\n        : dom.prevPointUntil(dom.prevPoint(point), dom.isVisiblePoint);\n      return nextPoint || point;\n    };\n\n    const endPoint = getVisiblePoint(this.getEndPoint(), false);\n    const startPoint = this.isCollapsed() ? endPoint : getVisiblePoint(this.getStartPoint(), true);\n\n    return new WrappedRange(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n  }\n\n  /**\n   * returns matched nodes on range\n   *\n   * @param {Function} [pred] - predicate function\n   * @param {Object} [options]\n   * @param {Boolean} [options.includeAncestor]\n   * @param {Boolean} [options.fullyContains]\n   * @return {Node[]}\n   */\n  nodes(pred, options) {\n    pred = pred || func.ok;\n\n    const includeAncestor = options && options.includeAncestor;\n    const fullyContains = options && options.fullyContains;\n\n    // TODO compare points and sort\n    const startPoint = this.getStartPoint();\n    const endPoint = this.getEndPoint();\n\n    const nodes = [];\n    const leftEdgeNodes = [];\n\n    dom.walkPoint(startPoint, endPoint, function(point) {\n      if (dom.isEditable(point.node)) {\n        return;\n      }\n\n      let node;\n      if (fullyContains) {\n        if (dom.isLeftEdgePoint(point)) {\n          leftEdgeNodes.push(point.node);\n        }\n        if (dom.isRightEdgePoint(point) && lists.contains(leftEdgeNodes, point.node)) {\n          node = point.node;\n        }\n      } else if (includeAncestor) {\n        node = dom.ancestor(point.node, pred);\n      } else {\n        node = point.node;\n      }\n\n      if (node && pred(node)) {\n        nodes.push(node);\n      }\n    }, true);\n\n    return lists.unique(nodes);\n  }\n\n  /**\n   * returns commonAncestor of range\n   * @return {Element} - commonAncestor\n   */\n  commonAncestor() {\n    return dom.commonAncestor(this.sc, this.ec);\n  }\n\n  /**\n   * returns expanded range by pred\n   *\n   * @param {Function} pred - predicate function\n   * @return {WrappedRange}\n   */\n  expand(pred) {\n    const startAncestor = dom.ancestor(this.sc, pred);\n    const endAncestor = dom.ancestor(this.ec, pred);\n\n    if (!startAncestor && !endAncestor) {\n      return new WrappedRange(this.sc, this.so, this.ec, this.eo);\n    }\n\n    const boundaryPoints = this.getPoints();\n\n    if (startAncestor) {\n      boundaryPoints.sc = startAncestor;\n      boundaryPoints.so = 0;\n    }\n\n    if (endAncestor) {\n      boundaryPoints.ec = endAncestor;\n      boundaryPoints.eo = dom.nodeLength(endAncestor);\n    }\n\n    return new WrappedRange(\n      boundaryPoints.sc,\n      boundaryPoints.so,\n      boundaryPoints.ec,\n      boundaryPoints.eo\n    );\n  }\n\n  /**\n   * @param {Boolean} isCollapseToStart\n   * @return {WrappedRange}\n   */\n  collapse(isCollapseToStart) {\n    if (isCollapseToStart) {\n      return new WrappedRange(this.sc, this.so, this.sc, this.so);\n    } else {\n      return new WrappedRange(this.ec, this.eo, this.ec, this.eo);\n    }\n  }\n\n  /**\n   * splitText on range\n   */\n  splitText() {\n    const isSameContainer = this.sc === this.ec;\n    const boundaryPoints = this.getPoints();\n\n    if (dom.isText(this.ec) && !dom.isEdgePoint(this.getEndPoint())) {\n      this.ec.splitText(this.eo);\n    }\n\n    if (dom.isText(this.sc) && !dom.isEdgePoint(this.getStartPoint())) {\n      boundaryPoints.sc = this.sc.splitText(this.so);\n      boundaryPoints.so = 0;\n\n      if (isSameContainer) {\n        boundaryPoints.ec = boundaryPoints.sc;\n        boundaryPoints.eo = this.eo - this.so;\n      }\n    }\n\n    return new WrappedRange(\n      boundaryPoints.sc,\n      boundaryPoints.so,\n      boundaryPoints.ec,\n      boundaryPoints.eo\n    );\n  }\n\n  /**\n   * delete contents on range\n   * @return {WrappedRange}\n   */\n  deleteContents() {\n    if (this.isCollapsed()) {\n      return this;\n    }\n\n    const rng = this.splitText();\n    const nodes = rng.nodes(null, {\n      fullyContains: true\n    });\n\n    // find new cursor point\n    const point = dom.prevPointUntil(rng.getStartPoint(), function(point) {\n      return !lists.contains(nodes, point.node);\n    });\n\n    const emptyParents = [];\n    $.each(nodes, function(idx, node) {\n      // find empty parents\n      const parent = node.parentNode;\n      if (point.node !== parent && dom.nodeLength(parent) === 1) {\n        emptyParents.push(parent);\n      }\n      dom.remove(node, false);\n    });\n\n    // remove empty parents\n    $.each(emptyParents, function(idx, node) {\n      dom.remove(node, false);\n    });\n\n    return new WrappedRange(\n      point.node,\n      point.offset,\n      point.node,\n      point.offset\n    ).normalize();\n  }\n\n  /**\n   * makeIsOn: return isOn(pred) function\n   */\n  makeIsOn(pred) {\n    return function() {\n      const ancestor = dom.ancestor(this.sc, pred);\n      return !!ancestor && (ancestor === dom.ancestor(this.ec, pred));\n    };\n  }\n\n  /**\n   * @param {Function} pred\n   * @return {Boolean}\n   */\n  isLeftEdgeOf(pred) {\n    if (!dom.isLeftEdgePoint(this.getStartPoint())) {\n      return false;\n    }\n\n    const node = dom.ancestor(this.sc, pred);\n    return node && dom.isLeftEdgeOf(this.sc, node);\n  }\n\n  /**\n   * returns whether range was collapsed or not\n   */\n  isCollapsed() {\n    return this.sc === this.ec && this.so === this.eo;\n  }\n\n  /**\n   * wrap inline nodes which children of body with paragraph\n   *\n   * @return {WrappedRange}\n   */\n  wrapBodyInlineWithPara() {\n    if (dom.isBodyContainer(this.sc) && dom.isEmpty(this.sc)) {\n      this.sc.innerHTML = dom.emptyPara;\n      return new WrappedRange(this.sc.firstChild, 0, this.sc.firstChild, 0);\n    }\n\n    /**\n     * [workaround] firefox often create range on not visible point. so normalize here.\n     *  - firefox: |<p>text</p>|\n     *  - chrome: <p>|text|</p>\n     */\n    const rng = this.normalize();\n    if (dom.isParaInline(this.sc) || dom.isPara(this.sc)) {\n      return rng;\n    }\n\n    // find inline top ancestor\n    let topAncestor;\n    if (dom.isInline(rng.sc)) {\n      const ancestors = dom.listAncestor(rng.sc, func.not(dom.isInline));\n      topAncestor = lists.last(ancestors);\n      if (!dom.isInline(topAncestor)) {\n        topAncestor = ancestors[ancestors.length - 2] || rng.sc.childNodes[rng.so];\n      }\n    } else {\n      topAncestor = rng.sc.childNodes[rng.so > 0 ? rng.so - 1 : 0];\n    }\n\n    // siblings not in paragraph\n    let inlineSiblings = dom.listPrev(topAncestor, dom.isParaInline).reverse();\n    inlineSiblings = inlineSiblings.concat(dom.listNext(topAncestor.nextSibling, dom.isParaInline));\n\n    // wrap with paragraph\n    if (inlineSiblings.length) {\n      const para = dom.wrap(lists.head(inlineSiblings), 'p');\n      dom.appendChildNodes(para, lists.tail(inlineSiblings));\n    }\n\n    return this.normalize();\n  }\n\n  /**\n   * insert node at current cursor\n   *\n   * @param {Node} node\n   * @return {Node}\n   */\n  insertNode(node) {\n    const rng = this.wrapBodyInlineWithPara().deleteContents();\n    const info = dom.splitPoint(rng.getStartPoint(), dom.isInline(node));\n\n    if (info.rightNode) {\n      info.rightNode.parentNode.insertBefore(node, info.rightNode);\n    } else {\n      info.container.appendChild(node);\n    }\n\n    return node;\n  }\n\n  /**\n   * insert html at current cursor\n   */\n  pasteHTML(markup) {\n    const contentsContainer = $('<div></div>').html(markup)[0];\n    let childNodes = lists.from(contentsContainer.childNodes);\n    const rng = this.wrapBodyInlineWithPara().deleteContents();\n\n    if (rng.so > 0) {\n      childNodes = childNodes.reverse();\n    }\n    childNodes = childNodes.map(function(childNode) {\n      return rng.insertNode(childNode);\n    });\n    if (rng.so > 0) {\n      childNodes = childNodes.reverse();\n    }\n    return childNodes;\n  }\n\n  /**\n   * returns text in range\n   *\n   * @return {String}\n   */\n  toString() {\n    const nativeRng = this.nativeRange();\n    return env.isW3CRangeSupport ? nativeRng.toString() : nativeRng.text;\n  }\n\n  /**\n   * returns range for word before cursor\n   *\n   * @param {Boolean} [findAfter] - find after cursor, default: false\n   * @return {WrappedRange}\n   */\n  getWordRange(findAfter) {\n    let endPoint = this.getEndPoint();\n\n    if (!dom.isCharPoint(endPoint)) {\n      return this;\n    }\n\n    const startPoint = dom.prevPointUntil(endPoint, function(point) {\n      return !dom.isCharPoint(point);\n    });\n\n    if (findAfter) {\n      endPoint = dom.nextPointUntil(endPoint, function(point) {\n        return !dom.isCharPoint(point);\n      });\n    }\n\n    return new WrappedRange(\n      startPoint.node,\n      startPoint.offset,\n      endPoint.node,\n      endPoint.offset\n    );\n  }\n\n  /**\n   * create offsetPath bookmark\n   *\n   * @param {Node} editable\n   */\n  bookmark(editable) {\n    return {\n      s: {\n        path: dom.makeOffsetPath(editable, this.sc),\n        offset: this.so\n      },\n      e: {\n        path: dom.makeOffsetPath(editable, this.ec),\n        offset: this.eo\n      }\n    };\n  }\n\n  /**\n   * create offsetPath bookmark base on paragraph\n   *\n   * @param {Node[]} paras\n   */\n  paraBookmark(paras) {\n    return {\n      s: {\n        path: lists.tail(dom.makeOffsetPath(lists.head(paras), this.sc)),\n        offset: this.so\n      },\n      e: {\n        path: lists.tail(dom.makeOffsetPath(lists.last(paras), this.ec)),\n        offset: this.eo\n      }\n    };\n  }\n\n  /**\n   * getClientRects\n   * @return {Rect[]}\n   */\n  getClientRects() {\n    const nativeRng = this.nativeRange();\n    return nativeRng.getClientRects();\n  }\n}\n\n/**\n * Data structure\n *  * BoundaryPoint: a point of dom tree\n *  * BoundaryPoints: two boundaryPoints corresponding to the start and the end of the Range\n *\n * See to http://www.w3.org/TR/DOM-Level-2-Traversal-Range/ranges.html#Level-2-Range-Position\n */\nexport default {\n  /**\n   * create Range Object From arguments or Browser Selection\n   *\n   * @param {Node} sc - start container\n   * @param {Number} so - start offset\n   * @param {Node} ec - end container\n   * @param {Number} eo - end offset\n   * @return {WrappedRange}\n   */\n  create: function(sc, so, ec, eo) {\n    if (arguments.length === 4) {\n      return new WrappedRange(sc, so, ec, eo);\n    } else if (arguments.length === 2) { // collapsed\n      ec = sc;\n      eo = so;\n      return new WrappedRange(sc, so, ec, eo);\n    } else {\n      let wrappedRange = this.createFromSelection();\n      if (!wrappedRange && arguments.length === 1) {\n        wrappedRange = this.createFromNode(arguments[0]);\n        return wrappedRange.collapse(dom.emptyPara === arguments[0].innerHTML);\n      }\n      return wrappedRange;\n    }\n  },\n\n  createFromSelection: function() {\n    let sc, so, ec, eo;\n    if (env.isW3CRangeSupport) {\n      const selection = document.getSelection();\n      if (!selection || selection.rangeCount === 0) {\n        return null;\n      } else if (dom.isBody(selection.anchorNode)) {\n        // Firefox: returns entire body as range on initialization.\n        // We won't never need it.\n        return null;\n      }\n\n      const nativeRng = selection.getRangeAt(0);\n      sc = nativeRng.startContainer;\n      so = nativeRng.startOffset;\n      ec = nativeRng.endContainer;\n      eo = nativeRng.endOffset;\n    } else { // IE8: TextRange\n      const textRange = document.selection.createRange();\n      const textRangeEnd = textRange.duplicate();\n      textRangeEnd.collapse(false);\n      const textRangeStart = textRange;\n      textRangeStart.collapse(true);\n\n      let startPoint = textRangeToPoint(textRangeStart, true);\n      let endPoint = textRangeToPoint(textRangeEnd, false);\n\n      // same visible point case: range was collapsed.\n      if (dom.isText(startPoint.node) && dom.isLeftEdgePoint(startPoint) &&\n          dom.isTextNode(endPoint.node) && dom.isRightEdgePoint(endPoint) &&\n          endPoint.node.nextSibling === startPoint.node) {\n        startPoint = endPoint;\n      }\n\n      sc = startPoint.cont;\n      so = startPoint.offset;\n      ec = endPoint.cont;\n      eo = endPoint.offset;\n    }\n\n    return new WrappedRange(sc, so, ec, eo);\n  },\n\n  /**\n   * @method\n   *\n   * create WrappedRange from node\n   *\n   * @param {Node} node\n   * @return {WrappedRange}\n   */\n  createFromNode: function(node) {\n    let sc = node;\n    let so = 0;\n    let ec = node;\n    let eo = dom.nodeLength(ec);\n\n    // browsers can't target a picture or void node\n    if (dom.isVoid(sc)) {\n      so = dom.listPrev(sc).length - 1;\n      sc = sc.parentNode;\n    }\n    if (dom.isBR(ec)) {\n      eo = dom.listPrev(ec).length - 1;\n      ec = ec.parentNode;\n    } else if (dom.isVoid(ec)) {\n      eo = dom.listPrev(ec).length;\n      ec = ec.parentNode;\n    }\n\n    return this.create(sc, so, ec, eo);\n  },\n\n  /**\n   * create WrappedRange from node after position\n   *\n   * @param {Node} node\n   * @return {WrappedRange}\n   */\n  createFromNodeBefore: function(node) {\n    return this.createFromNode(node).collapse(true);\n  },\n\n  /**\n   * create WrappedRange from node after position\n   *\n   * @param {Node} node\n   * @return {WrappedRange}\n   */\n  createFromNodeAfter: function(node) {\n    return this.createFromNode(node).collapse();\n  },\n\n  /**\n   * @method\n   *\n   * create WrappedRange from bookmark\n   *\n   * @param {Node} editable\n   * @param {Object} bookmark\n   * @return {WrappedRange}\n   */\n  createFromBookmark: function(editable, bookmark) {\n    const sc = dom.fromOffsetPath(editable, bookmark.s.path);\n    const so = bookmark.s.offset;\n    const ec = dom.fromOffsetPath(editable, bookmark.e.path);\n    const eo = bookmark.e.offset;\n    return new WrappedRange(sc, so, ec, eo);\n  },\n\n  /**\n   * @method\n   *\n   * create WrappedRange from paraBookmark\n   *\n   * @param {Object} bookmark\n   * @param {Node[]} paras\n   * @return {WrappedRange}\n   */\n  createFromParaBookmark: function(bookmark, paras) {\n    const so = bookmark.s.offset;\n    const eo = bookmark.e.offset;\n    const sc = dom.fromOffsetPath(lists.head(paras), bookmark.s.path);\n    const ec = dom.fromOffsetPath(lists.last(paras), bookmark.e.path);\n\n    return new WrappedRange(sc, so, ec, eo);\n  }\n};\n", "import $ from 'jquery';\n\n/**\n * @method readFileAsDataURL\n *\n * read contents of file as representing URL\n *\n * @param {File} file\n * @return {Promise} - then: dataUrl\n */\nexport function readFileAsDataURL(file) {\n  return $.Deferred((deferred) => {\n    $.extend(new FileReader(), {\n      onload: (e) => {\n        const dataURL = e.target.result;\n        deferred.resolve(dataURL);\n      },\n      onerror: (err) => {\n        deferred.reject(err);\n      }\n    }).readAsDataURL(file);\n  }).promise();\n}\n\n/**\n * @method createImage\n *\n * create `<image>` from url string\n *\n * @param {String} url\n * @return {Promise} - then: $image\n */\nexport function createImage(url) {\n  return $.Deferred((deferred) => {\n    const $img = $('<img>');\n\n    $img.one('load', () => {\n      $img.off('error abort');\n      deferred.resolve($img);\n    }).one('error abort', () => {\n      $img.off('load').detach();\n      deferred.reject($img);\n    }).css({\n      display: 'none'\n    }).appendTo(document.body).attr('src', url);\n  }).promise();\n}\n", "import range from '../core/range';\n\nexport default class History {\n  constructor($editable) {\n    this.stack = [];\n    this.stackOffset = -1;\n    this.$editable = $editable;\n    this.editable = $editable[0];\n  }\n\n  makeSnapshot() {\n    const rng = range.create(this.editable);\n    const emptyBookmark = { s: { path: [], offset: 0 }, e: { path: [], offset: 0 } };\n\n    return {\n      contents: this.$editable.html(),\n      bookmark: (rng ? rng.bookmark(this.editable) : emptyBookmark)\n    };\n  }\n\n  applySnapshot(snapshot) {\n    if (snapshot.contents !== null) {\n      this.$editable.html(snapshot.contents);\n    }\n    if (snapshot.bookmark !== null) {\n      range.createFromBookmark(this.editable, snapshot.bookmark).select();\n    }\n  }\n\n  /**\n  * @method rewind\n  * Rewinds the history stack back to the first snapshot taken.\n  * Leaves the stack intact, so that \"Redo\" can still be used.\n  */\n  rewind() {\n    // Create snap shot if not yet recorded\n    if (this.$editable.html() !== this.stack[this.stackOffset].contents) {\n      this.recordUndo();\n    }\n\n    // Return to the first available snapshot.\n    this.stackOffset = 0;\n\n    // Apply that snapshot.\n    this.applySnapshot(this.stack[this.stackOffset]);\n  }\n\n  /**\n  *  @method commit\n  *  Resets history stack, but keeps current editor's content.\n  */\n  commit() {\n    // Clear the stack.\n    this.stack = [];\n\n    // Restore stackOffset to its original value.\n    this.stackOffset = -1;\n\n    // Record our first snapshot (of nothing).\n    this.recordUndo();\n  }\n\n  /**\n  * @method reset\n  * Resets the history stack completely; reverting to an empty editor.\n  */\n  reset() {\n    // Clear the stack.\n    this.stack = [];\n\n    // Restore stackOffset to its original value.\n    this.stackOffset = -1;\n\n    // Clear the editable area.\n    this.$editable.html('');\n\n    // Record our first snapshot (of nothing).\n    this.recordUndo();\n  }\n\n  /**\n   * undo\n   */\n  undo() {\n    // Create snap shot if not yet recorded\n    if (this.$editable.html() !== this.stack[this.stackOffset].contents) {\n      this.recordUndo();\n    }\n\n    if (this.stackOffset > 0) {\n      this.stackOffset--;\n      this.applySnapshot(this.stack[this.stackOffset]);\n    }\n  }\n\n  /**\n   * redo\n   */\n  redo() {\n    if (this.stack.length - 1 > this.stackOffset) {\n      this.stackOffset++;\n      this.applySnapshot(this.stack[this.stackOffset]);\n    }\n  }\n\n  /**\n   * recorded undo\n   */\n  recordUndo() {\n    this.stackOffset++;\n\n    // Wash out stack after stackOffset\n    if (this.stack.length > this.stackOffset) {\n      this.stack = this.stack.slice(0, this.stackOffset);\n    }\n\n    // Create new snapshot and push it to the end\n    this.stack.push(this.makeSnapshot());\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nexport default class Style {\n  /**\n   * @method jQueryCSS\n   *\n   * [workaround] for old jQuery\n   * passing an array of style properties to .css()\n   * will result in an object of property-value pairs.\n   * (compability with version < 1.9)\n   *\n   * @private\n   * @param  {jQuery} $obj\n   * @param  {Array} propertyNames - An array of one or more CSS properties.\n   * @return {Object}\n   */\n  jQueryCSS($obj, propertyNames) {\n    if (env.jqueryVersion < 1.9) {\n      const result = {};\n      $.each(propertyNames, (idx, propertyName) => {\n        result[propertyName] = $obj.css(propertyName);\n      });\n      return result;\n    }\n    return $obj.css(propertyNames);\n  }\n\n  /**\n   * returns style object from node\n   *\n   * @param {jQuery} $node\n   * @return {Object}\n   */\n  fromNode($node) {\n    const properties = ['font-family', 'font-size', 'text-align', 'list-style-type', 'line-height'];\n    const styleInfo = this.jQueryCSS($node, properties) || {};\n    styleInfo['font-size'] = parseInt(styleInfo['font-size'], 10);\n    return styleInfo;\n  }\n\n  /**\n   * paragraph level style\n   *\n   * @param {WrappedRange} rng\n   * @param {Object} styleInfo\n   */\n  stylePara(rng, styleInfo) {\n    $.each(rng.nodes(dom.isPara, {\n      includeAncestor: true\n    }), (idx, para) => {\n      $(para).css(styleInfo);\n    });\n  }\n\n  /**\n   * insert and returns styleNodes on range.\n   *\n   * @param {WrappedRange} rng\n   * @param {Object} [options] - options for styleNodes\n   * @param {String} [options.nodeName] - default: `SPAN`\n   * @param {Boolean} [options.expandClosestSibling] - default: `false`\n   * @param {Boolean} [options.onlyPartialContains] - default: `false`\n   * @return {Node[]}\n   */\n  styleNodes(rng, options) {\n    rng = rng.splitText();\n\n    const nodeName = (options && options.nodeName) || 'SPAN';\n    const expandClosestSibling = !!(options && options.expandClosestSibling);\n    const onlyPartialContains = !!(options && options.onlyPartialContains);\n\n    if (rng.isCollapsed()) {\n      return [rng.insertNode(dom.create(nodeName))];\n    }\n\n    let pred = dom.makePredByNodeName(nodeName);\n    const nodes = rng.nodes(dom.isText, {\n      fullyContains: true\n    }).map((text) => {\n      return dom.singleChildAncestor(text, pred) || dom.wrap(text, nodeName);\n    });\n\n    if (expandClosestSibling) {\n      if (onlyPartialContains) {\n        const nodesInRange = rng.nodes();\n        // compose with partial contains predication\n        pred = func.and(pred, (node) => {\n          return lists.contains(nodesInRange, node);\n        });\n      }\n\n      return nodes.map((node) => {\n        const siblings = dom.withClosestSiblings(node, pred);\n        const head = lists.head(siblings);\n        const tails = lists.tail(siblings);\n        $.each(tails, (idx, elem) => {\n          dom.appendChildNodes(head, elem.childNodes);\n          dom.remove(elem);\n        });\n        return lists.head(siblings);\n      });\n    } else {\n      return nodes;\n    }\n  }\n\n  /**\n   * get current style on cursor\n   *\n   * @param {WrappedRange} rng\n   * @return {Object} - object contains style properties.\n   */\n  current(rng) {\n    const $cont = $(!dom.isElement(rng.sc) ? rng.sc.parentNode : rng.sc);\n    let styleInfo = this.fromNode($cont);\n\n    // document.queryCommandState for toggle state\n    // [workaround] prevent Firefox nsresult: \"0x80004005 (NS_ERROR_FAILURE)\"\n    try {\n      styleInfo = $.extend(styleInfo, {\n        'font-bold': document.queryCommandState('bold') ? 'bold' : 'normal',\n        'font-italic': document.queryCommandState('italic') ? 'italic' : 'normal',\n        'font-underline': document.queryCommandState('underline') ? 'underline' : 'normal',\n        'font-subscript': document.queryCommandState('subscript') ? 'subscript' : 'normal',\n        'font-superscript': document.queryCommandState('superscript') ? 'superscript' : 'normal',\n        'font-strikethrough': document.queryCommandState('strikethrough') ? 'strikethrough' : 'normal',\n        'font-family': document.queryCommandValue('fontname') || styleInfo['font-family']\n      });\n    } catch (e) {}\n\n    // list-style-type to list-style(unordered, ordered)\n    if (!rng.isOnList()) {\n      styleInfo['list-style'] = 'none';\n    } else {\n      const orderedTypes = ['circle', 'disc', 'disc-leading-zero', 'square'];\n      const isUnordered = $.inArray(styleInfo['list-style-type'], orderedTypes) > -1;\n      styleInfo['list-style'] = isUnordered ? 'unordered' : 'ordered';\n    }\n\n    const para = dom.ancestor(rng.sc, dom.isPara);\n    if (para && para.style['line-height']) {\n      styleInfo['line-height'] = para.style.lineHeight;\n    } else {\n      const lineHeight = parseInt(styleInfo['line-height'], 10) / parseInt(styleInfo['font-size'], 10);\n      styleInfo['line-height'] = lineHeight.toFixed(1);\n    }\n\n    styleInfo.anchor = rng.isOnAnchor() && dom.ancestor(rng.sc, dom.isAnchor);\n    styleInfo.ancestors = dom.listAncestor(rng.sc, dom.isEditable);\n    styleInfo.range = rng;\n\n    return styleInfo;\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport func from '../core/func';\nimport dom from '../core/dom';\nimport range from '../core/range';\n\nexport default class Bullet {\n  /**\n   * toggle ordered list\n   */\n  insertOrderedList(editable) {\n    this.toggleList('OL', editable);\n  }\n\n  /**\n   * toggle unordered list\n   */\n  insertUnorderedList(editable) {\n    this.toggleList('UL', editable);\n  }\n\n  /**\n   * indent\n   */\n  indent(editable) {\n    const rng = range.create(editable).wrapBodyInlineWithPara();\n\n    const paras = rng.nodes(dom.isPara, { includeAncestor: true });\n    const clustereds = lists.clusterBy(paras, func.peq2('parentNode'));\n\n    $.each(clustereds, (idx, paras) => {\n      const head = lists.head(paras);\n      if (dom.isLi(head)) {\n        const previousList = this.findList(head.previousSibling);\n        if (previousList) {\n          paras\n            .map(para => previousList.appendChild(para));\n        } else {\n          this.wrapList(paras, head.parentNode.nodeName);\n          paras\n            .map((para) => para.parentNode)\n            .map((para) => this.appendToPrevious(para));\n        }\n      } else {\n        $.each(paras, (idx, para) => {\n          $(para).css('marginLeft', (idx, val) => {\n            return (parseInt(val, 10) || 0) + 25;\n          });\n        });\n      }\n    });\n\n    rng.select();\n  }\n\n  /**\n   * outdent\n   */\n  outdent(editable) {\n    const rng = range.create(editable).wrapBodyInlineWithPara();\n\n    const paras = rng.nodes(dom.isPara, { includeAncestor: true });\n    const clustereds = lists.clusterBy(paras, func.peq2('parentNode'));\n\n    $.each(clustereds, (idx, paras) => {\n      const head = lists.head(paras);\n      if (dom.isLi(head)) {\n        this.releaseList([paras]);\n      } else {\n        $.each(paras, (idx, para) => {\n          $(para).css('marginLeft', (idx, val) => {\n            val = (parseInt(val, 10) || 0);\n            return val > 25 ? val - 25 : '';\n          });\n        });\n      }\n    });\n\n    rng.select();\n  }\n\n  /**\n   * toggle list\n   *\n   * @param {String} listName - OL or UL\n   */\n  toggleList(listName, editable) {\n    const rng = range.create(editable).wrapBodyInlineWithPara();\n\n    let paras = rng.nodes(dom.isPara, { includeAncestor: true });\n    const bookmark = rng.paraBookmark(paras);\n    const clustereds = lists.clusterBy(paras, func.peq2('parentNode'));\n\n    // paragraph to list\n    if (lists.find(paras, dom.isPurePara)) {\n      let wrappedParas = [];\n      $.each(clustereds, (idx, paras) => {\n        wrappedParas = wrappedParas.concat(this.wrapList(paras, listName));\n      });\n      paras = wrappedParas;\n    // list to paragraph or change list style\n    } else {\n      const diffLists = rng.nodes(dom.isList, {\n        includeAncestor: true\n      }).filter((listNode) => {\n        return !$.nodeName(listNode, listName);\n      });\n\n      if (diffLists.length) {\n        $.each(diffLists, (idx, listNode) => {\n          dom.replace(listNode, listName);\n        });\n      } else {\n        paras = this.releaseList(clustereds, true);\n      }\n    }\n\n    range.createFromParaBookmark(bookmark, paras).select();\n  }\n\n  /**\n   * @param {Node[]} paras\n   * @param {String} listName\n   * @return {Node[]}\n   */\n  wrapList(paras, listName) {\n    const head = lists.head(paras);\n    const last = lists.last(paras);\n\n    const prevList = dom.isList(head.previousSibling) && head.previousSibling;\n    const nextList = dom.isList(last.nextSibling) && last.nextSibling;\n\n    const listNode = prevList || dom.insertAfter(dom.create(listName || 'UL'), last);\n\n    // P to LI\n    paras = paras.map((para) => {\n      return dom.isPurePara(para) ? dom.replace(para, 'LI') : para;\n    });\n\n    // append to list(<ul>, <ol>)\n    dom.appendChildNodes(listNode, paras);\n\n    if (nextList) {\n      dom.appendChildNodes(listNode, lists.from(nextList.childNodes));\n      dom.remove(nextList);\n    }\n\n    return paras;\n  }\n\n  /**\n   * @method releaseList\n   *\n   * @param {Array[]} clustereds\n   * @param {Boolean} isEscapseToBody\n   * @return {Node[]}\n   */\n  releaseList(clustereds, isEscapseToBody) {\n    let releasedParas = [];\n\n    $.each(clustereds, (idx, paras) => {\n      const head = lists.head(paras);\n      const last = lists.last(paras);\n\n      const headList = isEscapseToBody ? dom.lastAncestor(head, dom.isList) : head.parentNode;\n      const parentItem = headList.parentNode;\n\n      if (headList.parentNode.nodeName === 'LI') {\n        paras.map(para => {\n          const newList = this.findNextSiblings(para);\n\n          if (parentItem.nextSibling) {\n            parentItem.parentNode.insertBefore(\n              para,\n              parentItem.nextSibling\n            );\n          } else {\n            parentItem.parentNode.appendChild(para);\n          }\n\n          if (newList.length) {\n            this.wrapList(newList, headList.nodeName);\n            para.appendChild(newList[0].parentNode);\n          }\n        });\n\n        if (headList.children.length === 0) {\n          parentItem.removeChild(headList);\n        }\n\n        if (parentItem.childNodes.length === 0) {\n          parentItem.parentNode.removeChild(parentItem);\n        }\n      } else {\n        const lastList = headList.childNodes.length > 1 ? dom.splitTree(headList, {\n          node: last.parentNode,\n          offset: dom.position(last) + 1\n        }, {\n          isSkipPaddingBlankHTML: true\n        }) : null;\n\n        const middleList = dom.splitTree(headList, {\n          node: head.parentNode,\n          offset: dom.position(head)\n        }, {\n          isSkipPaddingBlankHTML: true\n        });\n\n        paras = isEscapseToBody ? dom.listDescendant(middleList, dom.isLi)\n          : lists.from(middleList.childNodes).filter(dom.isLi);\n\n        // LI to P\n        if (isEscapseToBody || !dom.isList(headList.parentNode)) {\n          paras = paras.map((para) => {\n            return dom.replace(para, 'P');\n          });\n        }\n\n        $.each(lists.from(paras).reverse(), (idx, para) => {\n          dom.insertAfter(para, headList);\n        });\n\n        // remove empty lists\n        const rootLists = lists.compact([headList, middleList, lastList]);\n        $.each(rootLists, (idx, rootList) => {\n          const listNodes = [rootList].concat(dom.listDescendant(rootList, dom.isList));\n          $.each(listNodes.reverse(), (idx, listNode) => {\n            if (!dom.nodeLength(listNode)) {\n              dom.remove(listNode, true);\n            }\n          });\n        });\n      }\n\n      releasedParas = releasedParas.concat(paras);\n    });\n\n    return releasedParas;\n  }\n\n  /**\n   * @method appendToPrevious\n   *\n   * Appends list to previous list item, if\n   * none exist it wraps the list in a new list item.\n   *\n   * @param {HTMLNode} ListItem\n   * @return {HTMLNode}\n   */\n  appendToPrevious(node) {\n    return node.previousSibling\n      ? dom.appendChildNodes(node.previousSibling, [node])\n      : this.wrapList([node], 'LI');\n  }\n\n  /**\n   * @method findList\n   *\n   * Finds an existing list in list item\n   *\n   * @param {HTMLNode} ListItem\n   * @return {Array[]}\n   */\n  findList(node) {\n    return node\n      ? lists.find(node.children, child => ['OL', 'UL'].indexOf(child.nodeName) > -1)\n      : null;\n  }\n\n  /**\n   * @method findNextSiblings\n   *\n   * Finds all list item siblings that follow it\n   *\n   * @param {HTMLNode} ListItem\n   * @return {HTMLNode}\n   */\n  findNextSiblings(node) {\n    const siblings = [];\n    while (node.nextSibling) {\n      siblings.push(node.nextSibling);\n      node = node.nextSibling;\n    }\n    return siblings;\n  }\n}\n", "import $ from 'jquery';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport Bullet from '../editing/Bullet';\n\n/**\n * @class editing.Typing\n *\n * Typing\n *\n */\nexport default class Typing {\n  constructor(context) {\n    // a Bullet instance to toggle lists off\n    this.bullet = new Bullet();\n    this.options = context.options;\n  }\n\n  /**\n   * insert tab\n   *\n   * @param {WrappedRange} rng\n   * @param {Number} tabsize\n   */\n  insertTab(rng, tabsize) {\n    const tab = dom.createText(new Array(tabsize + 1).join(dom.NBSP_CHAR));\n    rng = rng.deleteContents();\n    rng.insertNode(tab, true);\n\n    rng = range.create(tab, tabsize);\n    rng.select();\n  }\n\n  /**\n   * insert paragraph\n   *\n   * @param {jQuery} $editable\n   * @param {WrappedRange} rng Can be used in unit tests to \"mock\" the range\n   *\n   * blockquoteBreakingLevel\n   *   0 - No break, the new paragraph remains inside the quote\n   *   1 - Break the first blockquote in the ancestors list\n   *   2 - Break all blockquotes, so that the new paragraph is not quoted (this is the default)\n   */\n  insertParagraph(editable, rng) {\n    rng = rng || range.create(editable);\n\n    // deleteContents on range.\n    rng = rng.deleteContents();\n\n    // Wrap range if it needs to be wrapped by paragraph\n    rng = rng.wrapBodyInlineWithPara();\n\n    // finding paragraph\n    const splitRoot = dom.ancestor(rng.sc, dom.isPara);\n\n    let nextPara;\n    // on paragraph: split paragraph\n    if (splitRoot) {\n      // if it is an empty line with li\n      if (dom.isEmpty(splitRoot) && dom.isLi(splitRoot)) {\n        // toogle UL/OL and escape\n        this.bullet.toggleList(splitRoot.parentNode.nodeName);\n        return;\n      } else {\n        let blockquote = null;\n        if (this.options.blockquoteBreakingLevel === 1) {\n          blockquote = dom.ancestor(splitRoot, dom.isBlockquote);\n        } else if (this.options.blockquoteBreakingLevel === 2) {\n          blockquote = dom.lastAncestor(splitRoot, dom.isBlockquote);\n        }\n\n        if (blockquote) {\n          // We're inside a blockquote and options ask us to break it\n          nextPara = $(dom.emptyPara)[0];\n          // If the split is right before a <br>, remove it so that there's no \"empty line\"\n          // after the split in the new blockquote created\n          if (dom.isRightEdgePoint(rng.getStartPoint()) && dom.isBR(rng.sc.nextSibling)) {\n            $(rng.sc.nextSibling).remove();\n          }\n          const split = dom.splitTree(blockquote, rng.getStartPoint(), { isDiscardEmptySplits: true });\n          if (split) {\n            split.parentNode.insertBefore(nextPara, split);\n          } else {\n            dom.insertAfter(nextPara, blockquote); // There's no split if we were at the end of the blockquote\n          }\n        } else {\n          nextPara = dom.splitTree(splitRoot, rng.getStartPoint());\n\n          // not a blockquote, just insert the paragraph\n          let emptyAnchors = dom.listDescendant(splitRoot, dom.isEmptyAnchor);\n          emptyAnchors = emptyAnchors.concat(dom.listDescendant(nextPara, dom.isEmptyAnchor));\n\n          $.each(emptyAnchors, (idx, anchor) => {\n            dom.remove(anchor);\n          });\n\n          // replace empty heading, pre or custom-made styleTag with P tag\n          if ((dom.isHeading(nextPara) || dom.isPre(nextPara) || dom.isCustomStyleTag(nextPara)) && dom.isEmpty(nextPara)) {\n            nextPara = dom.replace(nextPara, 'p');\n          }\n        }\n      }\n    // no paragraph: insert empty paragraph\n    } else {\n      const next = rng.sc.childNodes[rng.so];\n      nextPara = $(dom.emptyPara)[0];\n      if (next) {\n        rng.sc.insertBefore(nextPara, next);\n      } else {\n        rng.sc.appendChild(nextPara);\n      }\n    }\n\n    range.create(nextPara, 0).normalize().select().scrollIntoView(editable);\n  }\n}\n", "import $ from 'jquery';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport lists from '../core/lists';\n\n/**\n * @class Create a virtual table to create what actions to do in change.\n * @param {object} startPoint Cell selected to apply change.\n * @param {enum} where  Where change will be applied Row or Col. Use enum: TableResultAction.where\n * @param {enum} action Action to be applied. Use enum: TableResultAction.requestAction\n * @param {object} domTable Dom element of table to make changes.\n */\nconst TableResultAction = function(startPoint, where, action, domTable) {\n  const _startPoint = { 'colPos': 0, 'rowPos': 0 };\n  const _virtualTable = [];\n  const _actionCellList = [];\n\n  /// ///////////////////////////////////////////\n  // Private functions\n  /// ///////////////////////////////////////////\n\n  /**\n   * Set the startPoint of action.\n   */\n  function setStartPoint() {\n    if (!startPoint || !startPoint.tagName || (startPoint.tagName.toLowerCase() !== 'td' && startPoint.tagName.toLowerCase() !== 'th')) {\n      console.error('Impossible to identify start Cell point.', startPoint);\n      return;\n    }\n    _startPoint.colPos = startPoint.cellIndex;\n    if (!startPoint.parentElement || !startPoint.parentElement.tagName || startPoint.parentElement.tagName.toLowerCase() !== 'tr') {\n      console.error('Impossible to identify start Row point.', startPoint);\n      return;\n    }\n    _startPoint.rowPos = startPoint.parentElement.rowIndex;\n  }\n\n  /**\n   * Define virtual table position info object.\n   *\n   * @param {int} rowIndex Index position in line of virtual table.\n   * @param {int} cellIndex Index position in column of virtual table.\n   * @param {object} baseRow Row affected by this position.\n   * @param {object} baseCell Cell affected by this position.\n   * @param {bool} isSpan Inform if it is an span cell/row.\n   */\n  function setVirtualTablePosition(rowIndex, cellIndex, baseRow, baseCell, isRowSpan, isColSpan, isVirtualCell) {\n    const objPosition = {\n      'baseRow': baseRow,\n      'baseCell': baseCell,\n      'isRowSpan': isRowSpan,\n      'isColSpan': isColSpan,\n      'isVirtual': isVirtualCell\n    };\n    if (!_virtualTable[rowIndex]) {\n      _virtualTable[rowIndex] = [];\n    }\n    _virtualTable[rowIndex][cellIndex] = objPosition;\n  }\n\n  /**\n   * Create action cell object.\n   *\n   * @param {object} virtualTableCellObj Object of specific position on virtual table.\n   * @param {enum} resultAction Action to be applied in that item.\n   */\n  function getActionCell(virtualTableCellObj, resultAction, virtualRowPosition, virtualColPosition) {\n    return {\n      'baseCell': virtualTableCellObj.baseCell,\n      'action': resultAction,\n      'virtualTable': {\n        'rowIndex': virtualRowPosition,\n        'cellIndex': virtualColPosition\n      }\n    };\n  }\n\n  /**\n   * Recover free index of row to append Cell.\n   *\n   * @param {int} rowIndex Index of row to find free space.\n   * @param {int} cellIndex Index of cell to find free space in table.\n   */\n  function recoverCellIndex(rowIndex, cellIndex) {\n    if (!_virtualTable[rowIndex]) {\n      return cellIndex;\n    }\n    if (!_virtualTable[rowIndex][cellIndex]) {\n      return cellIndex;\n    }\n\n    let newCellIndex = cellIndex;\n    while (_virtualTable[rowIndex][newCellIndex]) {\n      newCellIndex++;\n      if (!_virtualTable[rowIndex][newCellIndex]) {\n        return newCellIndex;\n      }\n    }\n  }\n\n  /**\n   * Recover info about row and cell and add information to virtual table.\n   *\n   * @param {object} row Row to recover information.\n   * @param {object} cell Cell to recover information.\n   */\n  function addCellInfoToVirtual(row, cell) {\n    const cellIndex = recoverCellIndex(row.rowIndex, cell.cellIndex);\n    const cellHasColspan = (cell.colSpan > 1);\n    const cellHasRowspan = (cell.rowSpan > 1);\n    const isThisSelectedCell = (row.rowIndex === _startPoint.rowPos && cell.cellIndex === _startPoint.colPos);\n    setVirtualTablePosition(row.rowIndex, cellIndex, row, cell, cellHasRowspan, cellHasColspan, false);\n\n    // Add span rows to virtual Table.\n    const rowspanNumber = cell.attributes.rowSpan ? parseInt(cell.attributes.rowSpan.value, 10) : 0;\n    if (rowspanNumber > 1) {\n      for (let rp = 1; rp < rowspanNumber; rp++) {\n        const rowspanIndex = row.rowIndex + rp;\n        adjustStartPoint(rowspanIndex, cellIndex, cell, isThisSelectedCell);\n        setVirtualTablePosition(rowspanIndex, cellIndex, row, cell, true, cellHasColspan, true);\n      }\n    }\n\n    // Add span cols to virtual table.\n    const colspanNumber = cell.attributes.colSpan ? parseInt(cell.attributes.colSpan.value, 10) : 0;\n    if (colspanNumber > 1) {\n      for (let cp = 1; cp < colspanNumber; cp++) {\n        const cellspanIndex = recoverCellIndex(row.rowIndex, (cellIndex + cp));\n        adjustStartPoint(row.rowIndex, cellspanIndex, cell, isThisSelectedCell);\n        setVirtualTablePosition(row.rowIndex, cellspanIndex, row, cell, cellHasRowspan, true, true);\n      }\n    }\n  }\n\n  /**\n   * Process validation and adjust of start point if needed\n   *\n   * @param {int} rowIndex\n   * @param {int} cellIndex\n   * @param {object} cell\n   * @param {bool} isSelectedCell\n   */\n  function adjustStartPoint(rowIndex, cellIndex, cell, isSelectedCell) {\n    if (rowIndex === _startPoint.rowPos && _startPoint.colPos >= cell.cellIndex && cell.cellIndex <= cellIndex && !isSelectedCell) {\n      _startPoint.colPos++;\n    }\n  }\n\n  /**\n   * Create virtual table of cells with all cells, including span cells.\n   */\n  function createVirtualTable() {\n    const rows = domTable.rows;\n    for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {\n      const cells = rows[rowIndex].cells;\n      for (let cellIndex = 0; cellIndex < cells.length; cellIndex++) {\n        addCellInfoToVirtual(rows[rowIndex], cells[cellIndex]);\n      }\n    }\n  }\n\n  /**\n   * Get action to be applied on the cell.\n   *\n   * @param {object} cell virtual table cell to apply action\n   */\n  function getDeleteResultActionToCell(cell) {\n    switch (where) {\n      case TableResultAction.where.Column:\n        if (cell.isColSpan) {\n          return TableResultAction.resultAction.SubtractSpanCount;\n        }\n        break;\n      case TableResultAction.where.Row:\n        if (!cell.isVirtual && cell.isRowSpan) {\n          return TableResultAction.resultAction.AddCell;\n        } else if (cell.isRowSpan) {\n          return TableResultAction.resultAction.SubtractSpanCount;\n        }\n        break;\n    }\n    return TableResultAction.resultAction.RemoveCell;\n  }\n\n  /**\n   * Get action to be applied on the cell.\n   *\n   * @param {object} cell virtual table cell to apply action\n   */\n  function getAddResultActionToCell(cell) {\n    switch (where) {\n      case TableResultAction.where.Column:\n        if (cell.isColSpan) {\n          return TableResultAction.resultAction.SumSpanCount;\n        } else if (cell.isRowSpan && cell.isVirtual) {\n          return TableResultAction.resultAction.Ignore;\n        }\n        break;\n      case TableResultAction.where.Row:\n        if (cell.isRowSpan) {\n          return TableResultAction.resultAction.SumSpanCount;\n        } else if (cell.isColSpan && cell.isVirtual) {\n          return TableResultAction.resultAction.Ignore;\n        }\n        break;\n    }\n    return TableResultAction.resultAction.AddCell;\n  }\n\n  function init() {\n    setStartPoint();\n    createVirtualTable();\n  }\n\n  /// ///////////////////////////////////////////\n  // Public functions\n  /// ///////////////////////////////////////////\n\n  /**\n   * Recover array os what to do in table.\n   */\n  this.getActionList = function() {\n    const fixedRow = (where === TableResultAction.where.Row) ? _startPoint.rowPos : -1;\n    const fixedCol = (where === TableResultAction.where.Column) ? _startPoint.colPos : -1;\n\n    let actualPosition = 0;\n    let canContinue = true;\n    while (canContinue) {\n      const rowPosition = (fixedRow >= 0) ? fixedRow : actualPosition;\n      const colPosition = (fixedCol >= 0) ? fixedCol : actualPosition;\n      const row = _virtualTable[rowPosition];\n      if (!row) {\n        canContinue = false;\n        return _actionCellList;\n      }\n      const cell = row[colPosition];\n      if (!cell) {\n        canContinue = false;\n        return _actionCellList;\n      }\n\n      // Define action to be applied in this cell\n      let resultAction = TableResultAction.resultAction.Ignore;\n      switch (action) {\n        case TableResultAction.requestAction.Add:\n          resultAction = getAddResultActionToCell(cell);\n          break;\n        case TableResultAction.requestAction.Delete:\n          resultAction = getDeleteResultActionToCell(cell);\n          break;\n      }\n      _actionCellList.push(getActionCell(cell, resultAction, rowPosition, colPosition));\n      actualPosition++;\n    }\n\n    return _actionCellList;\n  };\n\n  init();\n};\n/**\n*\n* Where action occours enum.\n*/\nTableResultAction.where = { 'Row': 0, 'Column': 1 };\n/**\n*\n* Requested action to apply enum.\n*/\nTableResultAction.requestAction = { 'Add': 0, 'Delete': 1 };\n/**\n*\n* Result action to be executed enum.\n*/\nTableResultAction.resultAction = { 'Ignore': 0, 'SubtractSpanCount': 1, 'RemoveCell': 2, 'AddCell': 3, 'SumSpanCount': 4 };\n\n/**\n *\n * @class editing.Table\n *\n * Table\n *\n */\nexport default class Table {\n  /**\n   * handle tab key\n   *\n   * @param {WrappedRange} rng\n   * @param {Boolean} isShift\n   */\n  tab(rng, isShift) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const table = dom.ancestor(cell, dom.isTable);\n    const cells = dom.listDescendant(table, dom.isCell);\n\n    const nextCell = lists[isShift ? 'prev' : 'next'](cells, cell);\n    if (nextCell) {\n      range.create(nextCell, 0).select();\n    }\n  }\n\n  /**\n   * Add a new row\n   *\n   * @param {WrappedRange} rng\n   * @param {String} position (top/bottom)\n   * @return {Node}\n   */\n  addRow(rng, position) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n\n    const currentTr = $(cell).closest('tr');\n    const trAttributes = this.recoverAttributes(currentTr);\n    const html = $('<tr' + trAttributes + '></tr>');\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Row,\n      TableResultAction.requestAction.Add, $(currentTr).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let idCell = 0; idCell < actions.length; idCell++) {\n      const currentCell = actions[idCell];\n      const tdAttributes = this.recoverAttributes(currentCell.baseCell);\n      switch (currentCell.action) {\n        case TableResultAction.resultAction.AddCell:\n          html.append('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          break;\n        case TableResultAction.resultAction.SumSpanCount:\n          if (position === 'top') {\n            const baseCellTr = currentCell.baseCell.parent;\n            const isTopFromRowSpan = (!baseCellTr ? 0 : currentCell.baseCell.closest('tr').rowIndex) <= currentTr[0].rowIndex;\n            if (isTopFromRowSpan) {\n              const newTd = $('<div></div>').append($('<td' + tdAttributes + '>' + dom.blank + '</td>').removeAttr('rowspan')).html();\n              html.append(newTd);\n              break;\n            }\n          }\n          let rowspanNumber = parseInt(currentCell.baseCell.rowSpan, 10);\n          rowspanNumber++;\n          currentCell.baseCell.setAttribute('rowSpan', rowspanNumber);\n          break;\n      }\n    }\n\n    if (position === 'top') {\n      currentTr.before(html);\n    } else {\n      const cellHasRowspan = (cell.rowSpan > 1);\n      if (cellHasRowspan) {\n        const lastTrIndex = currentTr[0].rowIndex + (cell.rowSpan - 2);\n        $($(currentTr).parent().find('tr')[lastTrIndex]).after($(html));\n        return;\n      }\n      currentTr.after(html);\n    }\n  }\n\n  /**\n   * Add a new col\n   *\n   * @param {WrappedRange} rng\n   * @param {String} position (left/right)\n   * @return {Node}\n   */\n  addCol(rng, position) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const row = $(cell).closest('tr');\n    const rowsGroup = $(row).siblings();\n    rowsGroup.push(row);\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Column,\n      TableResultAction.requestAction.Add, $(row).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {\n      const currentCell = actions[actionIndex];\n      const tdAttributes = this.recoverAttributes(currentCell.baseCell);\n      switch (currentCell.action) {\n        case TableResultAction.resultAction.AddCell:\n          if (position === 'right') {\n            $(currentCell.baseCell).after('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          } else {\n            $(currentCell.baseCell).before('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          }\n          break;\n        case TableResultAction.resultAction.SumSpanCount:\n          if (position === 'right') {\n            let colspanNumber = parseInt(currentCell.baseCell.colSpan, 10);\n            colspanNumber++;\n            currentCell.baseCell.setAttribute('colSpan', colspanNumber);\n          } else {\n            $(currentCell.baseCell).before('<td' + tdAttributes + '>' + dom.blank + '</td>');\n          }\n          break;\n      }\n    }\n  }\n\n  /*\n  * Copy attributes from element.\n  *\n  * @param {object} Element to recover attributes.\n  * @return {string} Copied string elements.\n  */\n  recoverAttributes(el) {\n    let resultStr = '';\n\n    if (!el) {\n      return resultStr;\n    }\n\n    const attrList = el.attributes || [];\n\n    for (let i = 0; i < attrList.length; i++) {\n      if (attrList[i].name.toLowerCase() === 'id') {\n        continue;\n      }\n\n      if (attrList[i].specified) {\n        resultStr += ' ' + attrList[i].name + '=\\'' + attrList[i].value + '\\'';\n      }\n    }\n\n    return resultStr;\n  }\n\n  /**\n   * Delete current row\n   *\n   * @param {WrappedRange} rng\n   * @return {Node}\n   */\n  deleteRow(rng) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const row = $(cell).closest('tr');\n    const cellPos = row.children('td, th').index($(cell));\n    const rowPos = row[0].rowIndex;\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Row,\n      TableResultAction.requestAction.Delete, $(row).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {\n      if (!actions[actionIndex]) {\n        continue;\n      }\n\n      const baseCell = actions[actionIndex].baseCell;\n      const virtualPosition = actions[actionIndex].virtualTable;\n      const hasRowspan = (baseCell.rowSpan && baseCell.rowSpan > 1);\n      let rowspanNumber = (hasRowspan) ? parseInt(baseCell.rowSpan, 10) : 0;\n      switch (actions[actionIndex].action) {\n        case TableResultAction.resultAction.Ignore:\n          continue;\n        case TableResultAction.resultAction.AddCell:\n          const nextRow = row.next('tr')[0];\n          if (!nextRow) { continue; }\n          const cloneRow = row[0].cells[cellPos];\n          if (hasRowspan) {\n            if (rowspanNumber > 2) {\n              rowspanNumber--;\n              nextRow.insertBefore(cloneRow, nextRow.cells[cellPos]);\n              nextRow.cells[cellPos].setAttribute('rowSpan', rowspanNumber);\n              nextRow.cells[cellPos].innerHTML = '';\n            } else if (rowspanNumber === 2) {\n              nextRow.insertBefore(cloneRow, nextRow.cells[cellPos]);\n              nextRow.cells[cellPos].removeAttribute('rowSpan');\n              nextRow.cells[cellPos].innerHTML = '';\n            }\n          }\n          continue;\n        case TableResultAction.resultAction.SubtractSpanCount:\n          if (hasRowspan) {\n            if (rowspanNumber > 2) {\n              rowspanNumber--;\n              baseCell.setAttribute('rowSpan', rowspanNumber);\n              if (virtualPosition.rowIndex !== rowPos && baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            } else if (rowspanNumber === 2) {\n              baseCell.removeAttribute('rowSpan');\n              if (virtualPosition.rowIndex !== rowPos && baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            }\n          }\n          continue;\n        case TableResultAction.resultAction.RemoveCell:\n          // Do not need remove cell because row will be deleted.\n          continue;\n      }\n    }\n    row.remove();\n  }\n\n  /**\n   * Delete current col\n   *\n   * @param {WrappedRange} rng\n   * @return {Node}\n   */\n  deleteCol(rng) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    const row = $(cell).closest('tr');\n    const cellPos = row.children('td, th').index($(cell));\n\n    const vTable = new TableResultAction(cell, TableResultAction.where.Column,\n      TableResultAction.requestAction.Delete, $(row).closest('table')[0]);\n    const actions = vTable.getActionList();\n\n    for (let actionIndex = 0; actionIndex < actions.length; actionIndex++) {\n      if (!actions[actionIndex]) {\n        continue;\n      }\n      switch (actions[actionIndex].action) {\n        case TableResultAction.resultAction.Ignore:\n          continue;\n        case TableResultAction.resultAction.SubtractSpanCount:\n          const baseCell = actions[actionIndex].baseCell;\n          const hasColspan = (baseCell.colSpan && baseCell.colSpan > 1);\n          if (hasColspan) {\n            let colspanNumber = (baseCell.colSpan) ? parseInt(baseCell.colSpan, 10) : 0;\n            if (colspanNumber > 2) {\n              colspanNumber--;\n              baseCell.setAttribute('colSpan', colspanNumber);\n              if (baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            } else if (colspanNumber === 2) {\n              baseCell.removeAttribute('colSpan');\n              if (baseCell.cellIndex === cellPos) { baseCell.innerHTML = ''; }\n            }\n          }\n          continue;\n        case TableResultAction.resultAction.RemoveCell:\n          dom.remove(actions[actionIndex].baseCell, true);\n          continue;\n      }\n    }\n  }\n\n  /**\n   * create empty table element\n   *\n   * @param {Number} rowCount\n   * @param {Number} colCount\n   * @return {Node}\n   */\n  createTable(colCount, rowCount, options) {\n    const tds = [];\n    let tdHTML;\n    for (let idxCol = 0; idxCol < colCount; idxCol++) {\n      tds.push('<td>' + dom.blank + '</td>');\n    }\n    tdHTML = tds.join('');\n\n    const trs = [];\n    let trHTML;\n    for (let idxRow = 0; idxRow < rowCount; idxRow++) {\n      trs.push('<tr>' + tdHTML + '</tr>');\n    }\n    trHTML = trs.join('');\n    const $table = $('<table>' + trHTML + '</table>');\n    if (options && options.tableClassName) {\n      $table.addClass(options.tableClassName);\n    }\n\n    return $table[0];\n  }\n\n  /**\n   * Delete current table\n   *\n   * @param {WrappedRange} rng\n   * @return {Node}\n   */\n  deleteTable(rng) {\n    const cell = dom.ancestor(rng.commonAncestor(), dom.isCell);\n    $(cell).closest('table').remove();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport { readFileAsDataURL, createImage } from '../core/async';\nimport History from '../editing/History';\nimport Style from '../editing/Style';\nimport Typing from '../editing/Typing';\nimport Table from '../editing/Table';\nimport Bullet from '../editing/Bullet';\n\nconst KEY_BOGUS = 'bogus';\n\n/**\n * @class Editor\n */\nexport default class Editor {\n  constructor(context) {\n    this.context = context;\n\n    this.$note = context.layoutInfo.note;\n    this.$editor = context.layoutInfo.editor;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n\n    this.editable = this.$editable[0];\n    this.lastRange = null;\n\n    this.style = new Style();\n    this.table = new Table();\n    this.typing = new Typing(context);\n    this.bullet = new Bullet();\n    this.history = new History(this.$editable);\n\n    this.context.memo('help.undo', this.lang.help.undo);\n    this.context.memo('help.redo', this.lang.help.redo);\n    this.context.memo('help.tab', this.lang.help.tab);\n    this.context.memo('help.untab', this.lang.help.untab);\n    this.context.memo('help.insertParagraph', this.lang.help.insertParagraph);\n    this.context.memo('help.insertOrderedList', this.lang.help.insertOrderedList);\n    this.context.memo('help.insertUnorderedList', this.lang.help.insertUnorderedList);\n    this.context.memo('help.indent', this.lang.help.indent);\n    this.context.memo('help.outdent', this.lang.help.outdent);\n    this.context.memo('help.formatPara', this.lang.help.formatPara);\n    this.context.memo('help.insertHorizontalRule', this.lang.help.insertHorizontalRule);\n    this.context.memo('help.fontName', this.lang.help.fontName);\n\n    // native commands(with execCommand), generate function for execCommand\n    const commands = [\n      'bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript',\n      'justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull',\n      'formatBlock', 'removeFormat', 'backColor'\n    ];\n\n    for (let idx = 0, len = commands.length; idx < len; idx++) {\n      this[commands[idx]] = ((sCmd) => {\n        return (value) => {\n          this.beforeCommand();\n          document.execCommand(sCmd, false, value);\n          this.afterCommand(true);\n        };\n      })(commands[idx]);\n      this.context.memo('help.' + commands[idx], this.lang.help[commands[idx]]);\n    }\n\n    this.fontName = this.wrapCommand((value) => {\n      return this.fontStyling('font-family', \"\\'\" + value + \"\\'\");\n    });\n\n    this.fontSize = this.wrapCommand((value) => {\n      return this.fontStyling('font-size', value + 'px');\n    });\n\n    for (let idx = 1; idx <= 6; idx++) {\n      this['formatH' + idx] = ((idx) => {\n        return () => {\n          this.formatBlock('H' + idx);\n        };\n      })(idx);\n      this.context.memo('help.formatH' + idx, this.lang.help['formatH' + idx]);\n    };\n\n    this.insertParagraph = this.wrapCommand(() => {\n      this.typing.insertParagraph(this.editable);\n    });\n\n    this.insertOrderedList = this.wrapCommand(() => {\n      this.bullet.insertOrderedList(this.editable);\n    });\n\n    this.insertUnorderedList = this.wrapCommand(() => {\n      this.bullet.insertUnorderedList(this.editable);\n    });\n\n    this.indent = this.wrapCommand(() => {\n      this.bullet.indent(this.editable);\n    });\n\n    this.outdent = this.wrapCommand(() => {\n      this.bullet.outdent(this.editable);\n    });\n\n    /**\n     * insertNode\n     * insert node\n     * @param {Node} node\n     */\n    this.insertNode = this.wrapCommand((node) => {\n      if (this.isLimited($(node).text().length)) {\n        return;\n      }\n      const rng = this.createRange();\n      rng.insertNode(node);\n      range.createFromNodeAfter(node).select();\n    });\n\n    /**\n     * insert text\n     * @param {String} text\n     */\n    this.insertText = this.wrapCommand((text) => {\n      if (this.isLimited(text.length)) {\n        return;\n      }\n      const rng = this.createRange();\n      const textNode = rng.insertNode(dom.createText(text));\n      range.create(textNode, dom.nodeLength(textNode)).select();\n    });\n    /**\n     * paste HTML\n     * @param {String} markup\n     */\n    this.pasteHTML = this.wrapCommand((markup) => {\n      if (this.isLimited(markup.length)) {\n        return;\n      }\n      const contents = this.createRange().pasteHTML(markup);\n      range.createFromNodeAfter(lists.last(contents)).select();\n    });\n\n    /**\n     * formatBlock\n     *\n     * @param {String} tagName\n     */\n    this.formatBlock = this.wrapCommand((tagName, $target) => {\n      const onApplyCustomStyle = this.options.callbacks.onApplyCustomStyle;\n      if (onApplyCustomStyle) {\n        onApplyCustomStyle.call(this, $target, this.context, this.onFormatBlock);\n      } else {\n        this.onFormatBlock(tagName, $target);\n      }\n    });\n\n    /**\n     * insert horizontal rule\n     */\n    this.insertHorizontalRule = this.wrapCommand(() => {\n      const hrNode = this.createRange().insertNode(dom.create('HR'));\n      if (hrNode.nextSibling) {\n        range.create(hrNode.nextSibling, 0).normalize().select();\n      }\n    });\n\n    /**\n     * lineHeight\n     * @param {String} value\n     */\n    this.lineHeight = this.wrapCommand((value) => {\n      this.style.stylePara(this.createRange(), {\n        lineHeight: value\n      });\n    });\n\n    /**\n     * create link (command)\n     *\n     * @param {Object} linkInfo\n     */\n    this.createLink = this.wrapCommand((linkInfo) => {\n      let linkUrl = linkInfo.url;\n      const linkText = linkInfo.text;\n      const isNewWindow = linkInfo.isNewWindow;\n      let rng = linkInfo.range || this.createRange();\n      const additionalTextLength = linkText.length - rng.toString().length;\n      if (additionalTextLength > 0 && this.isLimited(additionalTextLength)) {\n        return;\n      }\n      const isTextChanged = rng.toString() !== linkText;\n\n      // handle spaced urls from input\n      if (typeof linkUrl === 'string') {\n        linkUrl = linkUrl.trim();\n      }\n\n      if (this.options.onCreateLink) {\n        linkUrl = this.options.onCreateLink(linkUrl);\n      } else {\n        // if url is not relative,\n        if (!/^\\.?\\/(.*)/.test(linkUrl)) {\n          // if url doesn't match an URL schema, set http:// as default\n          linkUrl = /^[A-Za-z][A-Za-z0-9+-.]*\\:[\\/\\/]?/.test(linkUrl)\n            ? linkUrl : 'http://' + linkUrl;\n        }\n      }\n\n      let anchors = [];\n      if (isTextChanged) {\n        rng = rng.deleteContents();\n        const anchor = rng.insertNode($('<A>' + linkText + '</A>')[0]);\n        anchors.push(anchor);\n      } else {\n        anchors = this.style.styleNodes(rng, {\n          nodeName: 'A',\n          expandClosestSibling: true,\n          onlyPartialContains: true\n        });\n      }\n\n      $.each(anchors, (idx, anchor) => {\n        $(anchor).attr('href', linkUrl);\n        if (isNewWindow) {\n          $(anchor).attr('target', '_blank');\n        } else {\n          $(anchor).removeAttr('target');\n        }\n      });\n\n      const startRange = range.createFromNodeBefore(lists.head(anchors));\n      const startPoint = startRange.getStartPoint();\n      const endRange = range.createFromNodeAfter(lists.last(anchors));\n      const endPoint = endRange.getEndPoint();\n\n      range.create(\n        startPoint.node,\n        startPoint.offset,\n        endPoint.node,\n        endPoint.offset\n      ).select();\n    });\n\n    /**\n     * setting color\n     *\n     * @param {Object} sObjColor  color code\n     * @param {String} sObjColor.foreColor foreground color\n     * @param {String} sObjColor.backColor background color\n     */\n    this.color = this.wrapCommand((colorInfo) => {\n      const foreColor = colorInfo.foreColor;\n      const backColor = colorInfo.backColor;\n\n      if (foreColor) { document.execCommand('foreColor', false, foreColor); }\n      if (backColor) { document.execCommand('backColor', false, backColor); }\n    });\n\n    /**\n     * Set foreground color\n     *\n     * @param {String} colorCode foreground color code\n     */\n    this.foreColor = this.wrapCommand((colorInfo) => {\n      document.execCommand('styleWithCSS', false, true);\n      document.execCommand('foreColor', false, colorInfo);\n    });\n\n    /**\n     * insert Table\n     *\n     * @param {String} dimension of table (ex : \"5x5\")\n     */\n    this.insertTable = this.wrapCommand((dim) => {\n      const dimension = dim.split('x');\n\n      const rng = this.createRange().deleteContents();\n      rng.insertNode(this.table.createTable(dimension[0], dimension[1], this.options));\n    });\n\n    /**\n     * remove media object and Figure Elements if media object is img with Figure.\n     */\n    this.removeMedia = this.wrapCommand(() => {\n      let $target = $(this.restoreTarget()).parent();\n      if ($target.parent('figure').length) {\n        $target.parent('figure').remove();\n      } else {\n        $target = $(this.restoreTarget()).detach();\n      }\n      this.context.triggerEvent('media.delete', $target, this.$editable);\n    });\n\n    /**\n     * float me\n     *\n     * @param {String} value\n     */\n    this.floatMe = this.wrapCommand((value) => {\n      const $target = $(this.restoreTarget());\n      $target.toggleClass('note-float-left', value === 'left');\n      $target.toggleClass('note-float-right', value === 'right');\n      $target.css('float', value);\n    });\n\n    /**\n     * resize overlay element\n     * @param {String} value\n     */\n    this.resize = this.wrapCommand((value) => {\n      const $target = $(this.restoreTarget());\n      $target.css({\n        width: value * 100 + '%',\n        height: ''\n      });\n    });\n  }\n\n  initialize() {\n    // bind custom events\n    this.$editable.on('keydown', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        this.context.triggerEvent('enter', event);\n      }\n      this.context.triggerEvent('keydown', event);\n\n      if (!event.isDefaultPrevented()) {\n        if (this.options.shortcuts) {\n          this.handleKeyMap(event);\n        } else {\n          this.preventDefaultEditableShortCuts(event);\n        }\n      }\n      if (this.isLimited(1, event)) {\n        return false;\n      }\n    }).on('keyup', (event) => {\n      this.context.triggerEvent('keyup', event);\n    }).on('focus', (event) => {\n      this.context.triggerEvent('focus', event);\n    }).on('blur', (event) => {\n      this.context.triggerEvent('blur', event);\n    }).on('mousedown', (event) => {\n      this.context.triggerEvent('mousedown', event);\n    }).on('mouseup', (event) => {\n      this.context.triggerEvent('mouseup', event);\n    }).on('scroll', (event) => {\n      this.context.triggerEvent('scroll', event);\n    }).on('paste', (event) => {\n      this.context.triggerEvent('paste', event);\n    });\n\n    // init content before set event\n    this.$editable.html(dom.html(this.$note) || dom.emptyPara);\n\n    this.$editable.on(env.inputEventName, func.debounce(() => {\n      this.context.triggerEvent('change', this.$editable.html());\n    }, 10));\n\n    this.$editor.on('focusin', (event) => {\n      this.context.triggerEvent('focusin', event);\n    }).on('focusout', (event) => {\n      this.context.triggerEvent('focusout', event);\n    });\n\n    if (!this.options.airMode) {\n      if (this.options.width) {\n        this.$editor.outerWidth(this.options.width);\n      }\n      if (this.options.height) {\n        this.$editable.outerHeight(this.options.height);\n      }\n      if (this.options.maxHeight) {\n        this.$editable.css('max-height', this.options.maxHeight);\n      }\n      if (this.options.minHeight) {\n        this.$editable.css('min-height', this.options.minHeight);\n      }\n    }\n\n    this.history.recordUndo();\n  }\n\n  destroy() {\n    this.$editable.off();\n  }\n\n  handleKeyMap(event) {\n    const keyMap = this.options.keyMap[env.isMac ? 'mac' : 'pc'];\n    const keys = [];\n\n    if (event.metaKey) { keys.push('CMD'); }\n    if (event.ctrlKey && !event.altKey) { keys.push('CTRL'); }\n    if (event.shiftKey) { keys.push('SHIFT'); }\n\n    const keyName = key.nameFromCode[event.keyCode];\n    if (keyName) {\n      keys.push(keyName);\n    }\n\n    const eventName = keyMap[keys.join('+')];\n    if (eventName) {\n      if (this.context.invoke(eventName) !== false) {\n        event.preventDefault();\n      }\n    } else if (key.isEdit(event.keyCode)) {\n      this.afterCommand();\n    }\n  }\n\n  preventDefaultEditableShortCuts(event) {\n    // B(Bold, 66) / I(Italic, 73) / U(Underline, 85)\n    if ((event.ctrlKey || event.metaKey) &&\n      lists.contains([66, 73, 85], event.keyCode)) {\n      event.preventDefault();\n    }\n  }\n\n  isLimited(pad, event) {\n    pad = pad || 0;\n\n    if (typeof event !== 'undefined') {\n      if (key.isMove(event.keyCode) ||\n          (event.ctrlKey || event.metaKey) ||\n          lists.contains([key.code.BACKSPACE, key.code.DELETE], event.keyCode)) {\n        return false;\n      }\n    }\n\n    if (this.options.maxTextLength > 0) {\n      if ((this.$editable.text().length + pad) >= this.options.maxTextLength) {\n        return true;\n      }\n    }\n    return false;\n  }\n  /**\n   * create range\n   * @return {WrappedRange}\n   */\n  createRange() {\n    this.focus();\n    return range.create(this.editable);\n  }\n\n  /**\n   * saveRange\n   *\n   * save current range\n   *\n   * @param {Boolean} [thenCollapse=false]\n   */\n  saveRange(thenCollapse) {\n    this.lastRange = this.createRange();\n    if (thenCollapse) {\n      this.lastRange.collapse().select();\n    }\n  }\n\n  /**\n   * restoreRange\n   *\n   * restore lately range\n   */\n  restoreRange() {\n    if (this.lastRange) {\n      this.lastRange.select();\n      this.focus();\n    }\n  }\n\n  saveTarget(node) {\n    this.$editable.data('target', node);\n  }\n\n  clearTarget() {\n    this.$editable.removeData('target');\n  }\n\n  restoreTarget() {\n    return this.$editable.data('target');\n  }\n\n  /**\n   * currentStyle\n   *\n   * current style\n   * @return {Object|Boolean} unfocus\n   */\n  currentStyle() {\n    let rng = range.create();\n    if (rng) {\n      rng = rng.normalize();\n    }\n    return rng ? this.style.current(rng) : this.style.fromNode(this.$editable);\n  }\n\n  /**\n   * style from node\n   *\n   * @param {jQuery} $node\n   * @return {Object}\n   */\n  styleFromNode($node) {\n    return this.style.fromNode($node);\n  }\n\n  /**\n   * undo\n   */\n  undo() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    this.history.undo();\n    this.context.triggerEvent('change', this.$editable.html());\n  }\n\n  /*\n  * commit\n  */\n  commit() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    this.history.commit();\n    this.context.triggerEvent('change', this.$editable.html());\n  }\n\n  /**\n   * redo\n   */\n  redo() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    this.history.redo();\n    this.context.triggerEvent('change', this.$editable.html());\n  }\n\n  /**\n   * before command\n   */\n  beforeCommand() {\n    this.context.triggerEvent('before.command', this.$editable.html());\n    // keep focus on editable before command execution\n    this.focus();\n  }\n\n  /**\n   * after command\n   * @param {Boolean} isPreventTrigger\n   */\n  afterCommand(isPreventTrigger) {\n    this.normalizeContent();\n    this.history.recordUndo();\n    if (!isPreventTrigger) {\n      this.context.triggerEvent('change', this.$editable.html());\n    }\n  }\n\n  /**\n   * handle tab key\n   */\n  tab() {\n    const rng = this.createRange();\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.table.tab(rng);\n    } else {\n      if (this.options.tabSize === 0) {\n        return false;\n      }\n\n      if (!this.isLimited(this.options.tabSize)) {\n        this.beforeCommand();\n        this.typing.insertTab(rng, this.options.tabSize);\n        this.afterCommand();\n      }\n    }\n  }\n\n  /**\n   * handle shift+tab key\n   */\n  untab() {\n    const rng = this.createRange();\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.table.tab(rng, true);\n    } else {\n      if (this.options.tabSize === 0) {\n        return false;\n      }\n    }\n  }\n\n  /**\n   * run given function between beforeCommand and afterCommand\n   */\n  wrapCommand(fn) {\n    return function() {\n      this.beforeCommand();\n      fn.apply(this, arguments);\n      this.afterCommand();\n    };\n  }\n\n  /**\n   * insert image\n   *\n   * @param {String} src\n   * @param {String|Function} param\n   * @return {Promise}\n   */\n  insertImage(src, param) {\n    return createImage(src, param).then(($image) => {\n      this.beforeCommand();\n\n      if (typeof param === 'function') {\n        param($image);\n      } else {\n        if (typeof param === 'string') {\n          $image.attr('data-filename', param);\n        }\n        $image.css('width', Math.min(this.$editable.width(), $image.width()));\n      }\n\n      $image.show();\n      range.create(this.editable).insertNode($image[0]);\n      range.createFromNodeAfter($image[0]).select();\n      this.afterCommand();\n    }).fail((e) => {\n      this.context.triggerEvent('image.upload.error', e);\n    });\n  }\n\n  /**\n   * insertImages\n   * @param {File[]} files\n   */\n  insertImagesAsDataURL(files) {\n    $.each(files, (idx, file) => {\n      const filename = file.name;\n      if (this.options.maximumImageFileSize && this.options.maximumImageFileSize < file.size) {\n        this.context.triggerEvent('image.upload.error', this.lang.image.maximumFileSizeError);\n      } else {\n        readFileAsDataURL(file).then((dataURL) => {\n          return this.insertImage(dataURL, filename);\n        }).fail(() => {\n          this.context.triggerEvent('image.upload.error');\n        });\n      }\n    });\n  }\n\n  /**\n   * return selected plain text\n   * @return {String} text\n   */\n  getSelectedText() {\n    let rng = this.createRange();\n\n    // if range on anchor, expand range with anchor\n    if (rng.isOnAnchor()) {\n      rng = range.createFromNode(dom.ancestor(rng.sc, dom.isAnchor));\n    }\n\n    return rng.toString();\n  }\n\n  onFormatBlock(tagName, $target) {\n    // [workaround] for MSIE, IE need `<`\n    tagName = env.isMSIE ? '<' + tagName + '>' : tagName;\n    document.execCommand('FormatBlock', false, tagName);\n\n    // support custom class\n    if ($target && $target.length) {\n      const className = $target[0].className || '';\n      if (className) {\n        const currentRange = this.createRange();\n\n        const $parent = $([currentRange.sc, currentRange.ec]).closest(tagName);\n        $parent.addClass(className);\n      }\n    }\n  }\n\n  formatPara() {\n    this.formatBlock('P');\n  }\n\n  fontStyling(target, value) {\n    const rng = this.createRange();\n\n    if (rng) {\n      const spans = this.style.styleNodes(rng);\n      $(spans).css(target, value);\n\n      // [workaround] added styled bogus span for style\n      //  - also bogus character needed for cursor position\n      if (rng.isCollapsed()) {\n        const firstSpan = lists.head(spans);\n        if (firstSpan && !dom.nodeLength(firstSpan)) {\n          firstSpan.innerHTML = dom.ZERO_WIDTH_NBSP_CHAR;\n          range.createFromNodeAfter(firstSpan.firstChild).select();\n          this.$editable.data(KEY_BOGUS, firstSpan);\n        }\n      }\n    }\n  }\n\n  /**\n   * unlink\n   *\n   * @type command\n   */\n  unlink() {\n    let rng = this.createRange();\n    if (rng.isOnAnchor()) {\n      const anchor = dom.ancestor(rng.sc, dom.isAnchor);\n      rng = range.createFromNode(anchor);\n      rng.select();\n\n      this.beforeCommand();\n      document.execCommand('unlink');\n      this.afterCommand();\n    }\n  }\n\n  /**\n   * returns link info\n   *\n   * @return {Object}\n   * @return {WrappedRange} return.range\n   * @return {String} return.text\n   * @return {Boolean} [return.isNewWindow=true]\n   * @return {String} [return.url=\"\"]\n   */\n  getLinkInfo() {\n    const rng = this.createRange().expand(dom.isAnchor);\n\n    // Get the first anchor on range(for edit).\n    const $anchor = $(lists.head(rng.nodes(dom.isAnchor)));\n    const linkInfo = {\n      range: rng,\n      text: rng.toString(),\n      url: $anchor.length ? $anchor.attr('href') : ''\n    };\n\n    // When anchor exists,\n    if ($anchor.length) {\n      // Set isNewWindow by checking its target.\n      linkInfo.isNewWindow = $anchor.attr('target') === '_blank';\n    }\n\n    return linkInfo;\n  }\n\n  addRow(position) {\n    const rng = this.createRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.addRow(rng, position);\n      this.afterCommand();\n    }\n  }\n\n  addCol(position) {\n    const rng = this.createRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.addCol(rng, position);\n      this.afterCommand();\n    }\n  }\n\n  deleteRow() {\n    const rng = this.createRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.deleteRow(rng);\n      this.afterCommand();\n    }\n  }\n\n  deleteCol() {\n    const rng = this.createRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.deleteCol(rng);\n      this.afterCommand();\n    }\n  }\n\n  deleteTable() {\n    const rng = this.createRange(this.$editable);\n    if (rng.isCollapsed() && rng.isOnCell()) {\n      this.beforeCommand();\n      this.table.deleteTable(rng);\n      this.afterCommand();\n    }\n  }\n\n  /**\n   * @param {Position} pos\n   * @param {jQuery} $target - target element\n   * @param {Boolean} [bKeepRatio] - keep ratio\n   */\n  resizeTo(pos, $target, bKeepRatio) {\n    let imageSize;\n    if (bKeepRatio) {\n      const newRatio = pos.y / pos.x;\n      const ratio = $target.data('ratio');\n      imageSize = {\n        width: ratio > newRatio ? pos.x : pos.y / ratio,\n        height: ratio > newRatio ? pos.x * ratio : pos.y\n      };\n    } else {\n      imageSize = {\n        width: pos.x,\n        height: pos.y\n      };\n    }\n\n    $target.css(imageSize);\n  }\n\n  /**\n   * returns whether editable area has focus or not.\n   */\n  hasFocus() {\n    return this.$editable.is(':focus');\n  }\n\n  /**\n   * set focus\n   */\n  focus() {\n    // [workaround] Screen will move when page is scolled in IE.\n    //  - do focus when not focused\n    if (!this.hasFocus()) {\n      this.$editable.focus();\n    }\n  }\n\n  /**\n   * returns whether contents is empty or not.\n   * @return {Boolean}\n   */\n  isEmpty() {\n    return dom.isEmpty(this.$editable[0]) || dom.emptyPara === this.$editable.html();\n  }\n\n  /**\n   * Removes all contents and restores the editable instance to an _emptyPara_.\n   */\n  empty() {\n    this.context.invoke('code', dom.emptyPara);\n  }\n\n  /**\n   * normalize content\n   */\n  normalizeContent() {\n    this.$editable[0].normalize();\n  }\n}\n", "import lists from '../core/lists';\n\nexport default class Clipboard {\n  constructor(context) {\n    this.context = context;\n    this.$editable = context.layoutInfo.editable;\n  }\n\n  initialize() {\n    this.$editable.on('paste', this.pasteByEvent.bind(this));\n  }\n\n  /**\n   * paste by clipboard event\n   *\n   * @param {Event} event\n   */\n  pasteByEvent(event) {\n    const clipboardData = event.originalEvent.clipboardData;\n    if (clipboardData && clipboardData.items && clipboardData.items.length) {\n      // paste img file\n      const item = clipboardData.items.length > 1 ? clipboardData.items[1] : lists.head(clipboardData.items);\n      if (item.kind === 'file' && item.type.indexOf('image/') !== -1) {\n        this.context.invoke('editor.insertImagesOrCallback', [item.getAsFile()]);\n      }\n      this.context.invoke('editor.afterCommand');\n    }\n  }\n}\n", "import $ from 'jquery';\n\nexport default class Dropzone {\n  constructor(context) {\n    this.context = context;\n    this.$eventListener = $(document);\n    this.$editor = context.layoutInfo.editor;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n    this.documentEventHandlers = {};\n\n    this.$dropzone = $([\n      '<div class=\"note-dropzone\">',\n      '  <div class=\"note-dropzone-message\"/>',\n      '</div>'\n    ].join('')).prependTo(this.$editor);\n  }\n\n  /**\n   * attach Drag and Drop Events\n   */\n  initialize() {\n    if (this.options.disableDragAndDrop) {\n      // prevent default drop event\n      this.documentEventHandlers.onDrop = (e) => {\n        e.preventDefault();\n      };\n      // do not consider outside of dropzone\n      this.$eventListener = this.$dropzone;\n      this.$eventListener.on('drop', this.documentEventHandlers.onDrop);\n    } else {\n      this.attachDragAndDropEvent();\n    }\n  }\n\n  /**\n   * attach Drag and Drop Events\n   */\n  attachDragAndDropEvent() {\n    let collection = $();\n    const $dropzoneMessage = this.$dropzone.find('.note-dropzone-message');\n\n    this.documentEventHandlers.onDragenter = (e) => {\n      const isCodeview = this.context.invoke('codeview.isActivated');\n      const hasEditorSize = this.$editor.width() > 0 && this.$editor.height() > 0;\n      if (!isCodeview && !collection.length && hasEditorSize) {\n        this.$editor.addClass('dragover');\n        this.$dropzone.width(this.$editor.width());\n        this.$dropzone.height(this.$editor.height());\n        $dropzoneMessage.text(this.lang.image.dragImageHere);\n      }\n      collection = collection.add(e.target);\n    };\n\n    this.documentEventHandlers.onDragleave = (e) => {\n      collection = collection.not(e.target);\n      if (!collection.length) {\n        this.$editor.removeClass('dragover');\n      }\n    };\n\n    this.documentEventHandlers.onDrop = () => {\n      collection = $();\n      this.$editor.removeClass('dragover');\n    };\n\n    // show dropzone on dragenter when dragging a object to document\n    // -but only if the editor is visible, i.e. has a positive width and height\n    this.$eventListener.on('dragenter', this.documentEventHandlers.onDragenter)\n      .on('dragleave', this.documentEventHandlers.onDragleave)\n      .on('drop', this.documentEventHandlers.onDrop);\n\n    // change dropzone's message on hover.\n    this.$dropzone.on('dragenter', () => {\n      this.$dropzone.addClass('hover');\n      $dropzoneMessage.text(this.lang.image.dropImage);\n    }).on('dragleave', () => {\n      this.$dropzone.removeClass('hover');\n      $dropzoneMessage.text(this.lang.image.dragImageHere);\n    });\n\n    // attach dropImage\n    this.$dropzone.on('drop', (event) => {\n      const dataTransfer = event.originalEvent.dataTransfer;\n\n      // stop the browser from opening the dropped content\n      event.preventDefault();\n\n      if (dataTransfer && dataTransfer.files && dataTransfer.files.length) {\n        this.$editable.focus();\n        this.context.invoke('editor.insertImagesOrCallback', dataTransfer.files);\n      } else {\n        $.each(dataTransfer.types, (idx, type) => {\n          const content = dataTransfer.getData(type);\n\n          if (type.toLowerCase().indexOf('text') > -1) {\n            this.context.invoke('editor.pasteHTML', content);\n          } else {\n            $(content).each((idx, item) => {\n              this.context.invoke('editor.insertNode', item);\n            });\n          }\n        });\n      }\n    }).on('dragover', false); // prevent default dragover event\n  }\n\n  destroy() {\n    Object.keys(this.documentEventHandlers).forEach((key) => {\n      this.$eventListener.off(key.substr(2).toLowerCase(), this.documentEventHandlers[key]);\n    });\n    this.documentEventHandlers = {};\n  }\n}\n", "import env from '../core/env';\nimport dom from '../core/dom';\n\nlet CodeMirror;\nif (env.hasCodeMirror) {\n  if (env.isSupportAmd) {\n    require(['codemirror'], function(cm) {\n      CodeMirror = cm;\n    });\n  } else {\n    CodeMirror = window.CodeMirror;\n  }\n}\n\n/**\n * @class Codeview\n */\nexport default class CodeView {\n  constructor(context) {\n    this.context = context;\n    this.$editor = context.layoutInfo.editor;\n    this.$editable = context.layoutInfo.editable;\n    this.$codable = context.layoutInfo.codable;\n    this.options = context.options;\n  }\n\n  sync() {\n    const isCodeview = this.isActivated();\n    if (isCodeview && env.hasCodeMirror) {\n      this.$codable.data('cmEditor').save();\n    }\n  }\n\n  /**\n   * @return {Boolean}\n   */\n  isActivated() {\n    return this.$editor.hasClass('codeview');\n  }\n\n  /**\n   * toggle codeview\n   */\n  toggle() {\n    if (this.isActivated()) {\n      this.deactivate();\n    } else {\n      this.activate();\n    }\n    this.context.triggerEvent('codeview.toggled');\n  }\n\n  /**\n   * activate code view\n   */\n  activate() {\n    this.$codable.val(dom.html(this.$editable, this.options.prettifyHtml));\n    this.$codable.height(this.$editable.height());\n\n    this.context.invoke('toolbar.updateCodeview', true);\n    this.$editor.addClass('codeview');\n    this.$codable.focus();\n\n    // activate CodeMirror as codable\n    if (env.hasCodeMirror) {\n      const cmEditor = CodeMirror.fromTextArea(this.$codable[0], this.options.codemirror);\n\n      // CodeMirror TernServer\n      if (this.options.codemirror.tern) {\n        const server = new CodeMirror.TernServer(this.options.codemirror.tern);\n        cmEditor.ternServer = server;\n        cmEditor.on('cursorActivity', (cm) => {\n          server.updateArgHints(cm);\n        });\n      }\n\n      cmEditor.on('blur', (event) => {\n        this.context.triggerEvent('blur.codeview', cmEditor.getValue(), event);\n      });\n\n      // CodeMirror hasn't Padding.\n      cmEditor.setSize(null, this.$editable.outerHeight());\n      this.$codable.data('cmEditor', cmEditor);\n    } else {\n      this.$codable.on('blur', (event) => {\n        this.context.triggerEvent('blur.codeview', this.$codable.val(), event);\n      });\n    }\n  }\n\n  /**\n   * deactivate code view\n   */\n  deactivate() {\n    // deactivate CodeMirror as codable\n    if (env.hasCodeMirror) {\n      const cmEditor = this.$codable.data('cmEditor');\n      this.$codable.val(cmEditor.getValue());\n      cmEditor.toTextArea();\n    }\n\n    const value = dom.value(this.$codable, this.options.prettifyHtml) || dom.emptyPara;\n    const isChange = this.$editable.html() !== value;\n\n    this.$editable.html(value);\n    this.$editable.height(this.options.height ? this.$codable.height() : 'auto');\n    this.$editor.removeClass('codeview');\n\n    if (isChange) {\n      this.context.triggerEvent('change', this.$editable.html(), this.$editable);\n    }\n\n    this.$editable.focus();\n\n    this.context.invoke('toolbar.updateCodeview', false);\n  }\n\n  destroy() {\n    if (this.isActivated()) {\n      this.deactivate();\n    }\n  }\n}\n", "import $ from 'jquery';\nconst EDITABLE_PADDING = 24;\n\nexport default class Statusbar {\n  constructor(context) {\n    this.$document = $(document);\n    this.$statusbar = context.layoutInfo.statusbar;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n  }\n\n  initialize() {\n    if (this.options.airMode || this.options.disableResizeEditor) {\n      this.destroy();\n      return;\n    }\n\n    this.$statusbar.on('mousedown', (event) => {\n      event.preventDefault();\n      event.stopPropagation();\n\n      const editableTop = this.$editable.offset().top - this.$document.scrollTop();\n      const onMouseMove = (event) => {\n        let height = event.clientY - (editableTop + EDITABLE_PADDING);\n\n        height = (this.options.minheight > 0) ? Math.max(height, this.options.minheight) : height;\n        height = (this.options.maxHeight > 0) ? Math.min(height, this.options.maxHeight) : height;\n\n        this.$editable.height(height);\n      };\n\n      this.$document.on('mousemove', onMouseMove).one('mouseup', () => {\n        this.$document.off('mousemove', onMouseMove);\n      });\n    });\n  }\n\n  destroy() {\n    this.$statusbar.off();\n    this.$statusbar.addClass('locked');\n  }\n}\n", "import $ from 'jquery';\n\nexport default class Fullscreen {\n  constructor(context) {\n    this.context = context;\n\n    this.$editor = context.layoutInfo.editor;\n    this.$toolbar = context.layoutInfo.toolbar;\n    this.$editable = context.layoutInfo.editable;\n    this.$codable = context.layoutInfo.codable;\n\n    this.$window = $(window);\n    this.$scrollbar = $('html, body');\n\n    this.onResize = () => {\n      this.resizeTo({\n        h: this.$window.height() - this.$toolbar.outerHeight()\n      });\n    };\n  }\n\n  resizeTo(size) {\n    this.$editable.css('height', size.h);\n    this.$codable.css('height', size.h);\n    if (this.$codable.data('cmeditor')) {\n      this.$codable.data('cmeditor').setsize(null, size.h);\n    }\n  }\n\n  /**\n   * toggle fullscreen\n   */\n  toggle() {\n    this.$editor.toggleClass('fullscreen');\n    if (this.isFullscreen()) {\n      this.$editable.data('orgHeight', this.$editable.css('height'));\n      this.$editable.data('orgMaxHeight', this.$editable.css('maxHeight'));\n      this.$editable.css('maxHeight', '');\n      this.$window.on('resize', this.onResize).trigger('resize');\n      this.$scrollbar.css('overflow', 'hidden');\n    } else {\n      this.$window.off('resize', this.onResize);\n      this.resizeTo({ h: this.$editable.data('orgHeight') });\n      this.$editable.css('maxHeight', this.$editable.css('orgMaxHeight'));\n      this.$scrollbar.css('overflow', 'visible');\n    }\n\n    this.context.invoke('toolbar.updateFullscreen', this.isFullscreen());\n  }\n\n  isFullscreen() {\n    return this.$editor.hasClass('fullscreen');\n  }\n}\n", "import $ from 'jquery';\nimport dom from '../core/dom';\n\nexport default class Handle {\n  constructor(context) {\n    this.context = context;\n    this.$document = $(document);\n    this.$editingArea = context.layoutInfo.editingArea;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n\n    this.events = {\n      'summernote.mousedown': (we, e) => {\n        if (this.update(e.target)) {\n          e.preventDefault();\n        }\n      },\n      'summernote.keyup summernote.scroll summernote.change summernote.dialog.shown': () => {\n        this.update();\n      },\n      'summernote.disable': () => {\n        this.hide();\n      },\n      'summernote.codeview.toggled': () => {\n        this.update();\n      }\n    };\n  }\n\n  initialize() {\n    this.$handle = $([\n      '<div class=\"note-handle\">',\n      '<div class=\"note-control-selection\">',\n      '<div class=\"note-control-selection-bg\"></div>',\n      '<div class=\"note-control-holder note-control-nw\"></div>',\n      '<div class=\"note-control-holder note-control-ne\"></div>',\n      '<div class=\"note-control-holder note-control-sw\"></div>',\n      '<div class=\"',\n      (this.options.disableResizeImage ? 'note-control-holder' : 'note-control-sizing'),\n      ' note-control-se\"></div>',\n      (this.options.disableResizeImage ? '' : '<div class=\"note-control-selection-info\"></div>'),\n      '</div>',\n      '</div>'\n    ].join('')).prependTo(this.$editingArea);\n\n    this.$handle.on('mousedown', (event) => {\n      if (dom.isControlSizing(event.target)) {\n        event.preventDefault();\n        event.stopPropagation();\n\n        const $target = this.$handle.find('.note-control-selection').data('target');\n        const posStart = $target.offset();\n        const scrollTop = this.$document.scrollTop();\n\n        const onMouseMove = (event) => {\n          this.context.invoke('editor.resizeTo', {\n            x: event.clientX - posStart.left,\n            y: event.clientY - (posStart.top - scrollTop)\n          }, $target, !event.shiftKey);\n\n          this.update($target[0]);\n        };\n\n        this.$document\n          .on('mousemove', onMouseMove)\n          .one('mouseup', (e) => {\n            e.preventDefault();\n            this.$document.off('mousemove', onMouseMove);\n            this.context.invoke('editor.afterCommand');\n          });\n\n        if (!$target.data('ratio')) { // original ratio.\n          $target.data('ratio', $target.height() / $target.width());\n        }\n      }\n    });\n\n    // Listen for scrolling on the handle overlay.\n    this.$handle.on('wheel', (e) => {\n      e.preventDefault();\n      this.update();\n    });\n  }\n\n  destroy() {\n    this.$handle.remove();\n  }\n\n  update(target) {\n    if (this.context.isDisabled()) {\n      return false;\n    }\n\n    const isImage = dom.isImg(target);\n    const $selection = this.$handle.find('.note-control-selection');\n\n    this.context.invoke('imagePopover.update', target);\n\n    if (isImage) {\n      const $image = $(target);\n      const position = $image.position();\n      const pos = {\n        left: position.left + parseInt($image.css('marginLeft'), 10),\n        top: position.top + parseInt($image.css('marginTop'), 10)\n      };\n\n      // exclude margin\n      const imageSize = {\n        w: $image.outerWidth(false),\n        h: $image.outerHeight(false)\n      };\n\n      $selection.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top,\n        width: imageSize.w,\n        height: imageSize.h\n      }).data('target', $image); // save current image element.\n\n      const origImageObj = new Image();\n      origImageObj.src = $image.attr('src');\n\n      const sizingText = imageSize.w + 'x' + imageSize.h + ' (' + this.lang.image.original + ': ' + origImageObj.width + 'x' + origImageObj.height + ')';\n      $selection.find('.note-control-selection-info').text(sizingText);\n      this.context.invoke('editor.saveTarget', target);\n    } else {\n      this.hide();\n    }\n\n    return isImage;\n  }\n\n  /**\n   * hide\n   *\n   * @param {jQuery} $handle\n   */\n  hide() {\n    this.context.invoke('editor.clearTarget');\n    this.$handle.children().hide();\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport key from '../core/key';\n\nconst defaultScheme = 'http://';\nconst linkPattern = /^([A-Za-z][A-Za-z0-9+-.]*\\:[\\/]{2}|mailto:[A-Z0-9._%+-]+@)?(www\\.)?(.+)$/i;\n\nexport default class AutoLink {\n  constructor(context) {\n    this.context = context;\n    this.events = {\n      'summernote.keyup': (we, e) => {\n        if (!e.isDefaultPrevented()) {\n          this.handleKeyup(e);\n        }\n      },\n      'summernote.keydown': (we, e) => {\n        this.handleKeydown(e);\n      }\n    };\n  }\n\n  initialize() {\n    this.lastWordRange = null;\n  }\n\n  destroy() {\n    this.lastWordRange = null;\n  }\n\n  replace() {\n    if (!this.lastWordRange) {\n      return;\n    }\n\n    const keyword = this.lastWordRange.toString();\n    const match = keyword.match(linkPattern);\n\n    if (match && (match[1] || match[2])) {\n      const link = match[1] ? keyword : defaultScheme + keyword;\n      const node = $('<a />').html(keyword).attr('href', link)[0];\n      if (this.context.options.linkTargetBlank) {\n        $(node).attr('target', '_blank');\n      }\n\n      this.lastWordRange.insertNode(node);\n      this.lastWordRange = null;\n      this.context.invoke('editor.focus');\n    }\n  }\n\n  handleKeydown(e) {\n    if (lists.contains([key.code.ENTER, key.code.SPACE], e.keyCode)) {\n      const wordRange = this.context.invoke('editor.createRange').getWordRange();\n      this.lastWordRange = wordRange;\n    }\n  }\n\n  handleKeyup(e) {\n    if (lists.contains([key.code.ENTER, key.code.SPACE], e.keyCode)) {\n      this.replace();\n    }\n  }\n}\n", "import dom from '../core/dom';\n\n/**\n * textarea auto sync.\n */\nexport default class AutoSync {\n  constructor(context) {\n    this.$note = context.layoutInfo.note;\n    this.events = {\n      'summernote.change': () => {\n        this.$note.val(context.invoke('code'));\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return dom.isTextarea(this.$note[0]);\n  }\n}\n", "import $ from 'jquery';\nexport default class Placeholder {\n  constructor(context) {\n    this.context = context;\n\n    this.$editingArea = context.layoutInfo.editingArea;\n    this.options = context.options;\n    this.events = {\n      'summernote.init summernote.change': () => {\n        this.update();\n      },\n      'summernote.codeview.toggled': () => {\n        this.update();\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return !!this.options.placeholder;\n  }\n\n  initialize() {\n    this.$placeholder = $('<div class=\"note-placeholder\">');\n    this.$placeholder.on('click', () => {\n      this.context.invoke('focus');\n    }).html(this.options.placeholder).prependTo(this.$editingArea);\n\n    this.update();\n  }\n\n  destroy() {\n    this.$placeholder.remove();\n  }\n\n  update() {\n    const isShow = !this.context.invoke('codeview.isActivated') && this.context.invoke('editor.isEmpty');\n    this.$placeholder.toggle(isShow);\n  }\n}\n", "import $ from 'jquery';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport env from '../core/env';\n\nexport default class Buttons {\n  constructor(context) {\n    this.ui = $.summernote.ui;\n    this.context = context;\n    this.$toolbar = context.layoutInfo.toolbar;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n    this.invertedKeyMap = func.invertObject(\n      this.options.keyMap[env.isMac ? 'mac' : 'pc']\n    );\n  }\n\n  representShortcut(editorMethod) {\n    let shortcut = this.invertedKeyMap[editorMethod];\n    if (!this.options.shortcuts || !shortcut) {\n      return '';\n    }\n\n    if (env.isMac) {\n      shortcut = shortcut.replace('CMD', '⌘').replace('SHIFT', '⇧');\n    }\n\n    shortcut = shortcut.replace('BACKSLASH', '\\\\')\n      .replace('SLASH', '/')\n      .replace('LEFTBRACKET', '[')\n      .replace('RIGHTBRACKET', ']');\n\n    return ' (' + shortcut + ')';\n  }\n\n  button(o) {\n    if (!this.options.tooltip && o.tooltip) {\n      delete o.tooltip;\n    }\n    o.container = this.options.container;\n    return this.ui.button(o);\n  }\n\n  initialize() {\n    this.addToolbarButtons();\n    this.addImagePopoverButtons();\n    this.addLinkPopoverButtons();\n    this.addTablePopoverButtons();\n    this.fontInstalledMap = {};\n  }\n\n  destroy() {\n    delete this.fontInstalledMap;\n  }\n\n  isFontInstalled(name) {\n    if (!this.fontInstalledMap.hasOwnProperty(name)) {\n      this.fontInstalledMap[name] = env.isFontInstalled(name) ||\n        lists.contains(this.options.fontNamesIgnoreCheck, name);\n    }\n\n    return this.fontInstalledMap[name];\n  }\n\n  isFontDeservedToAdd(name) {\n    const genericFamilies = ['sans-serif', 'serif', 'monospace', 'cursive', 'fantasy'];\n    name = name.toLowerCase();\n\n    return ((name !== '') && this.isFontInstalled(name) && ($.inArray(name, genericFamilies) === -1));\n  }\n\n  colorPalette(className, tooltip, backColor, foreColor) {\n    return this.ui.buttonGroup({\n      className: 'note-color ' + className,\n      children: [\n        this.button({\n          className: 'note-current-color-button',\n          contents: this.ui.icon(this.options.icons.font + ' note-recent-color'),\n          tooltip: tooltip,\n          click: (e) => {\n            const $button = $(e.currentTarget);\n            if (backColor && foreColor) {\n              this.context.invoke('editor.color', {\n                backColor: $button.attr('data-backColor'),\n                foreColor: $button.attr('data-foreColor')\n              });\n            } else if (backColor) {\n              this.context.invoke('editor.color', {\n                backColor: $button.attr('data-backColor')\n              });\n            } else if (foreColor) {\n              this.context.invoke('editor.color', {\n                foreColor: $button.attr('data-foreColor')\n              });\n            }\n          },\n          callback: ($button) => {\n            const $recentColor = $button.find('.note-recent-color');\n            if (backColor) {\n              $recentColor.css('background-color', '#FFFF00');\n              $button.attr('data-backColor', '#FFFF00');\n            }\n            if (!foreColor) {\n              $recentColor.css('color', 'transparent');\n            }\n          }\n        }),\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents('', this.options),\n          tooltip: this.lang.color.more,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdown({\n          items: (backColor ? [\n            '<div class=\"note-palette\">',\n            '  <div class=\"note-palette-title\">' + this.lang.color.background + '</div>',\n            '  <div>',\n            '    <button type=\"button\" class=\"note-color-reset btn btn-light\" data-event=\"backColor\" data-value=\"inherit\">',\n            this.lang.color.transparent,\n            '    </button>',\n            '  </div>',\n            '  <div class=\"note-holder\" data-event=\"backColor\"/>',\n            '  <div>',\n            '    <button type=\"button\" class=\"note-color-select btn\" data-event=\"openPalette\" data-value=\"backColorPicker\">',\n            this.lang.color.cpSelect,\n            '    </button>',\n            '    <input type=\"color\" id=\"backColorPicker\" class=\"note-btn note-color-select-btn\" value=\"#FFFF00\" data-event=\"backColorPalette\">',\n            '  </div>',\n            '  <div class=\"note-holder-custom\" id=\"backColorPalette\" data-event=\"backColor\"/>',\n            '</div>'\n          ].join('') : '') +\n          (foreColor ? [\n            '<div class=\"note-palette\">',\n            '  <div class=\"note-palette-title\">' + this.lang.color.foreground + '</div>',\n            '  <div>',\n            '    <button type=\"button\" class=\"note-color-reset btn btn-light\" data-event=\"removeFormat\" data-value=\"foreColor\">',\n            this.lang.color.resetToDefault,\n            '    </button>',\n            '  </div>',\n            '  <div class=\"note-holder\" data-event=\"foreColor\"/>',\n            '  <div>',\n            '    <button type=\"button\" class=\"note-color-select btn\" data-event=\"openPalette\" data-value=\"foreColorPicker\">',\n            this.lang.color.cpSelect,\n            '    </button>',\n            '    <input type=\"color\" id=\"foreColorPicker\" class=\"note-btn note-color-select-btn\" value=\"#000000\" data-event=\"foreColorPalette\">',\n            '  <div class=\"note-holder-custom\" id=\"foreColorPalette\" data-event=\"foreColor\"/>',\n            '</div>'\n          ].join('') : ''),\n          callback: ($dropdown) => {\n            $dropdown.find('.note-holder').each((idx, item) => {\n              const $holder = $(item);\n              $holder.append(this.ui.palette({\n                colors: this.options.colors,\n                colorsName: this.options.colorsName,\n                eventName: $holder.data('event'),\n                container: this.options.container,\n                tooltip: this.options.tooltip\n              }).render());\n            });\n            /* TODO: do we have to record recent custom colors within cookies? */\n            var customColors = [\n              ['#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF', '#FFFFFF']\n            ];\n            $dropdown.find('.note-holder-custom').each((idx, item) => {\n              const $holder = $(item);\n              $holder.append(this.ui.palette({\n                colors: customColors,\n                colorsName: customColors,\n                eventName: $holder.data('event'),\n                container: this.options.container,\n                tooltip: this.options.tooltip\n              }).render());\n            });\n            $dropdown.find('input[type=color]').each((idx, item) => {\n              $(item).change(function() {\n                const $chip = $dropdown.find('#' + $(this).data('event')).find('.note-color-btn').first();\n                const color = this.value.toUpperCase();\n                $chip.css('background-color', color)\n                  .attr('aria-label', color)\n                  .attr('data-value', color)\n                  .attr('data-original-title', color);\n                $chip.click();\n              });\n            });\n          },\n          click: (event) => {\n            event.stopPropagation();\n\n            const $parent = $('.' + className);\n            const $button = $(event.target);\n            const eventName = $button.data('event');\n            let value = $button.attr('data-value');\n\n            if (eventName === 'openPalette') {\n              const $picker = $parent.find('#' + value);\n              const $palette = $($parent.find('#' + $picker.data('event')).find('.note-color-row')[0]);\n\n              // Shift palette chips\n              const $chip = $palette.find('.note-color-btn').last().detach();\n\n              // Set chip attributes\n              const color = $picker.val();\n              $chip.css('background-color', color)\n                .attr('aria-label', color)\n                .attr('data-value', color)\n                .attr('data-original-title', color);\n              $palette.prepend($chip);\n              $picker.click();\n            } else if (lists.contains(['backColor', 'foreColor'], eventName)) {\n              const key = eventName === 'backColor' ? 'background-color' : 'color';\n              const $color = $button.closest('.note-color').find('.note-recent-color');\n              const $currentButton = $button.closest('.note-color').find('.note-current-color-button');\n\n              $color.css(key, value);\n              $currentButton.attr('data-' + eventName, value);\n              this.context.invoke('editor.' + eventName, value);\n            }\n          }\n        })\n      ]\n    }).render();\n  }\n\n  addToolbarButtons() {\n    this.context.memo('button.style', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(\n            this.ui.icon(this.options.icons.magic), this.options\n          ),\n          tooltip: this.lang.style.style,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdown({\n          className: 'dropdown-style',\n          items: this.options.styleTags,\n          title: this.lang.style.style,\n          template: (item) => {\n            if (typeof item === 'string') {\n              item = { tag: item, title: (this.lang.style.hasOwnProperty(item) ? this.lang.style[item] : item) };\n            }\n\n            const tag = item.tag;\n            const title = item.title;\n            const style = item.style ? ' style=\"' + item.style + '\" ' : '';\n            const className = item.className ? ' class=\"' + item.className + '\"' : '';\n\n            return '<' + tag + style + className + '>' + title + '</' + tag + '>';\n          },\n          click: this.context.createInvokeHandler('editor.formatBlock')\n        })\n      ]).render();\n    });\n\n    for (let styleIdx = 0, styleLen = this.options.styleTags.length; styleIdx < styleLen; styleIdx++) {\n      const item = this.options.styleTags[styleIdx];\n\n      this.context.memo('button.style.' + item, () => {\n        return this.button({\n          className: 'note-btn-style-' + item,\n          contents: '<div data-value=\"' + item + '\">' + item.toUpperCase() + '</div>',\n          tooltip: this.lang.style[item],\n          click: this.context.createInvokeHandler('editor.formatBlock')\n        }).render();\n      });\n    }\n\n    this.context.memo('button.bold', () => {\n      return this.button({\n        className: 'note-btn-bold',\n        contents: this.ui.icon(this.options.icons.bold),\n        tooltip: this.lang.font.bold + this.representShortcut('bold'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.bold')\n      }).render();\n    });\n\n    this.context.memo('button.italic', () => {\n      return this.button({\n        className: 'note-btn-italic',\n        contents: this.ui.icon(this.options.icons.italic),\n        tooltip: this.lang.font.italic + this.representShortcut('italic'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.italic')\n      }).render();\n    });\n\n    this.context.memo('button.underline', () => {\n      return this.button({\n        className: 'note-btn-underline',\n        contents: this.ui.icon(this.options.icons.underline),\n        tooltip: this.lang.font.underline + this.representShortcut('underline'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.underline')\n      }).render();\n    });\n\n    this.context.memo('button.clear', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.eraser),\n        tooltip: this.lang.font.clear + this.representShortcut('removeFormat'),\n        click: this.context.createInvokeHandler('editor.removeFormat')\n      }).render();\n    });\n\n    this.context.memo('button.strikethrough', () => {\n      return this.button({\n        className: 'note-btn-strikethrough',\n        contents: this.ui.icon(this.options.icons.strikethrough),\n        tooltip: this.lang.font.strikethrough + this.representShortcut('strikethrough'),\n        click: this.context.createInvokeHandlerAndUpdateState('editor.strikethrough')\n      }).render();\n    });\n\n    this.context.memo('button.superscript', () => {\n      return this.button({\n        className: 'note-btn-superscript',\n        contents: this.ui.icon(this.options.icons.superscript),\n        tooltip: this.lang.font.superscript,\n        click: this.context.createInvokeHandlerAndUpdateState('editor.superscript')\n      }).render();\n    });\n\n    this.context.memo('button.subscript', () => {\n      return this.button({\n        className: 'note-btn-subscript',\n        contents: this.ui.icon(this.options.icons.subscript),\n        tooltip: this.lang.font.subscript,\n        click: this.context.createInvokeHandlerAndUpdateState('editor.subscript')\n      }).render();\n    });\n\n    this.context.memo('button.fontname', () => {\n      const styleInfo = this.context.invoke('editor.currentStyle');\n\n      // Add 'default' fonts into the fontnames array if not exist\n      $.each(styleInfo['font-family'].split(','), (idx, fontname) => {\n        fontname = fontname.trim().replace(/['\"]+/g, '');\n        if (this.isFontDeservedToAdd(fontname)) {\n          if ($.inArray(fontname, this.options.fontNames) === -1) {\n            this.options.fontNames.push(fontname);\n          }\n        }\n      });\n\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(\n            '<span class=\"note-current-fontname\"/>', this.options\n          ),\n          tooltip: this.lang.font.name,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdownCheck({\n          className: 'dropdown-fontname',\n          checkClassName: this.options.icons.menuCheck,\n          items: this.options.fontNames.filter(this.isFontInstalled.bind(this)),\n          title: this.lang.font.name,\n          template: (item) => {\n            return '<span style=\"font-family: \\'' + item + '\\'\">' + item + '</span>';\n          },\n          click: this.context.createInvokeHandlerAndUpdateState('editor.fontName')\n        })\n      ]).render();\n    });\n\n    this.context.memo('button.fontsize', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents('<span class=\"note-current-fontsize\"/>', this.options),\n          tooltip: this.lang.font.size,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdownCheck({\n          className: 'dropdown-fontsize',\n          checkClassName: this.options.icons.menuCheck,\n          items: this.options.fontSizes,\n          title: this.lang.font.size,\n          click: this.context.createInvokeHandlerAndUpdateState('editor.fontSize')\n        })\n      ]).render();\n    });\n\n    this.context.memo('button.color', () => {\n      return this.colorPalette('note-color-all', this.lang.color.recent, true, true);\n    });\n\n    this.context.memo('button.forecolor', () => {\n      return this.colorPalette('note-color-fore', this.lang.color.foreground, false, true);\n    });\n\n    this.context.memo('button.backcolor', () => {\n      return this.colorPalette('note-color-back', this.lang.color.background, true, false);\n    });\n\n    this.context.memo('button.ul', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.unorderedlist),\n        tooltip: this.lang.lists.unordered + this.representShortcut('insertUnorderedList'),\n        click: this.context.createInvokeHandler('editor.insertUnorderedList')\n      }).render();\n    });\n\n    this.context.memo('button.ol', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.orderedlist),\n        tooltip: this.lang.lists.ordered + this.representShortcut('insertOrderedList'),\n        click: this.context.createInvokeHandler('editor.insertOrderedList')\n      }).render();\n    });\n\n    const justifyLeft = this.button({\n      contents: this.ui.icon(this.options.icons.alignLeft),\n      tooltip: this.lang.paragraph.left + this.representShortcut('justifyLeft'),\n      click: this.context.createInvokeHandler('editor.justifyLeft')\n    });\n\n    const justifyCenter = this.button({\n      contents: this.ui.icon(this.options.icons.alignCenter),\n      tooltip: this.lang.paragraph.center + this.representShortcut('justifyCenter'),\n      click: this.context.createInvokeHandler('editor.justifyCenter')\n    });\n\n    const justifyRight = this.button({\n      contents: this.ui.icon(this.options.icons.alignRight),\n      tooltip: this.lang.paragraph.right + this.representShortcut('justifyRight'),\n      click: this.context.createInvokeHandler('editor.justifyRight')\n    });\n\n    const justifyFull = this.button({\n      contents: this.ui.icon(this.options.icons.alignJustify),\n      tooltip: this.lang.paragraph.justify + this.representShortcut('justifyFull'),\n      click: this.context.createInvokeHandler('editor.justifyFull')\n    });\n\n    const outdent = this.button({\n      contents: this.ui.icon(this.options.icons.outdent),\n      tooltip: this.lang.paragraph.outdent + this.representShortcut('outdent'),\n      click: this.context.createInvokeHandler('editor.outdent')\n    });\n\n    const indent = this.button({\n      contents: this.ui.icon(this.options.icons.indent),\n      tooltip: this.lang.paragraph.indent + this.representShortcut('indent'),\n      click: this.context.createInvokeHandler('editor.indent')\n    });\n\n    this.context.memo('button.justifyLeft', func.invoke(justifyLeft, 'render'));\n    this.context.memo('button.justifyCenter', func.invoke(justifyCenter, 'render'));\n    this.context.memo('button.justifyRight', func.invoke(justifyRight, 'render'));\n    this.context.memo('button.justifyFull', func.invoke(justifyFull, 'render'));\n    this.context.memo('button.outdent', func.invoke(outdent, 'render'));\n    this.context.memo('button.indent', func.invoke(indent, 'render'));\n\n    this.context.memo('button.paragraph', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.alignLeft), this.options),\n          tooltip: this.lang.paragraph.paragraph,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdown([\n          this.ui.buttonGroup({\n            className: 'note-align',\n            children: [justifyLeft, justifyCenter, justifyRight, justifyFull]\n          }),\n          this.ui.buttonGroup({\n            className: 'note-list',\n            children: [outdent, indent]\n          })\n        ])\n      ]).render();\n    });\n\n    this.context.memo('button.height', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.textHeight), this.options),\n          tooltip: this.lang.font.height,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdownCheck({\n          items: this.options.lineHeights,\n          checkClassName: this.options.icons.menuCheck,\n          className: 'dropdown-line-height',\n          title: this.lang.font.height,\n          click: this.context.createInvokeHandler('editor.lineHeight')\n        })\n      ]).render();\n    });\n\n    this.context.memo('button.table', () => {\n      return this.ui.buttonGroup([\n        this.button({\n          className: 'dropdown-toggle',\n          contents: this.ui.dropdownButtonContents(this.ui.icon(this.options.icons.table), this.options),\n          tooltip: this.lang.table.table,\n          data: {\n            toggle: 'dropdown'\n          }\n        }),\n        this.ui.dropdown({\n          title: this.lang.table.table,\n          className: 'note-table',\n          items: [\n            '<div class=\"note-dimension-picker\">',\n            '  <div class=\"note-dimension-picker-mousecatcher\" data-event=\"insertTable\" data-value=\"1x1\"/>',\n            '  <div class=\"note-dimension-picker-highlighted\"/>',\n            '  <div class=\"note-dimension-picker-unhighlighted\"/>',\n            '</div>',\n            '<div class=\"note-dimension-display\">1 x 1</div>'\n          ].join('')\n        })\n      ], {\n        callback: ($node) => {\n          const $catcher = $node.find('.note-dimension-picker-mousecatcher');\n          $catcher.css({\n            width: this.options.insertTableMaxSize.col + 'em',\n            height: this.options.insertTableMaxSize.row + 'em'\n          }).mousedown(this.context.createInvokeHandler('editor.insertTable'))\n            .on('mousemove', this.tableMoveHandler.bind(this));\n        }\n      }).render();\n    });\n\n    this.context.memo('button.link', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.link),\n        tooltip: this.lang.link.link + this.representShortcut('linkDialog.show'),\n        click: this.context.createInvokeHandler('linkDialog.show')\n      }).render();\n    });\n\n    this.context.memo('button.picture', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.picture),\n        tooltip: this.lang.image.image,\n        click: this.context.createInvokeHandler('imageDialog.show')\n      }).render();\n    });\n\n    this.context.memo('button.video', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.video),\n        tooltip: this.lang.video.video,\n        click: this.context.createInvokeHandler('videoDialog.show')\n      }).render();\n    });\n\n    this.context.memo('button.hr', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.minus),\n        tooltip: this.lang.hr.insert + this.representShortcut('insertHorizontalRule'),\n        click: this.context.createInvokeHandler('editor.insertHorizontalRule')\n      }).render();\n    });\n\n    this.context.memo('button.fullscreen', () => {\n      return this.button({\n        className: 'btn-fullscreen',\n        contents: this.ui.icon(this.options.icons.arrowsAlt),\n        tooltip: this.lang.options.fullscreen,\n        click: this.context.createInvokeHandler('fullscreen.toggle')\n      }).render();\n    });\n\n    this.context.memo('button.codeview', () => {\n      return this.button({\n        className: 'btn-codeview',\n        contents: this.ui.icon(this.options.icons.code),\n        tooltip: this.lang.options.codeview,\n        click: this.context.createInvokeHandler('codeview.toggle')\n      }).render();\n    });\n\n    this.context.memo('button.redo', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.redo),\n        tooltip: this.lang.history.redo + this.representShortcut('redo'),\n        click: this.context.createInvokeHandler('editor.redo')\n      }).render();\n    });\n\n    this.context.memo('button.undo', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.undo),\n        tooltip: this.lang.history.undo + this.representShortcut('undo'),\n        click: this.context.createInvokeHandler('editor.undo')\n      }).render();\n    });\n\n    this.context.memo('button.help', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.question),\n        tooltip: this.lang.options.help,\n        click: this.context.createInvokeHandler('helpDialog.show')\n      }).render();\n    });\n  }\n\n  /**\n   * image : [\n   *   ['imagesize', ['imageSize100', 'imageSize50', 'imageSize25']],\n   *   ['float', ['floatLeft', 'floatRight', 'floatNone' ]],\n   *   ['remove', ['removeMedia']]\n   * ],\n   */\n  addImagePopoverButtons() {\n    // Image Size Buttons\n    this.context.memo('button.imageSize100', () => {\n      return this.button({\n        contents: '<span class=\"note-fontsize-10\">100%</span>',\n        tooltip: this.lang.image.resizeFull,\n        click: this.context.createInvokeHandler('editor.resize', '1')\n      }).render();\n    });\n    this.context.memo('button.imageSize50', () => {\n      return this.button({\n        contents: '<span class=\"note-fontsize-10\">50%</span>',\n        tooltip: this.lang.image.resizeHalf,\n        click: this.context.createInvokeHandler('editor.resize', '0.5')\n      }).render();\n    });\n    this.context.memo('button.imageSize25', () => {\n      return this.button({\n        contents: '<span class=\"note-fontsize-10\">25%</span>',\n        tooltip: this.lang.image.resizeQuarter,\n        click: this.context.createInvokeHandler('editor.resize', '0.25')\n      }).render();\n    });\n\n    // Float Buttons\n    this.context.memo('button.floatLeft', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.alignLeft),\n        tooltip: this.lang.image.floatLeft,\n        click: this.context.createInvokeHandler('editor.floatMe', 'left')\n      }).render();\n    });\n\n    this.context.memo('button.floatRight', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.alignRight),\n        tooltip: this.lang.image.floatRight,\n        click: this.context.createInvokeHandler('editor.floatMe', 'right')\n      }).render();\n    });\n\n    this.context.memo('button.floatNone', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.alignJustify),\n        tooltip: this.lang.image.floatNone,\n        click: this.context.createInvokeHandler('editor.floatMe', 'none')\n      }).render();\n    });\n\n    // Remove Buttons\n    this.context.memo('button.removeMedia', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.trash),\n        tooltip: this.lang.image.remove,\n        click: this.context.createInvokeHandler('editor.removeMedia')\n      }).render();\n    });\n  }\n\n  addLinkPopoverButtons() {\n    this.context.memo('button.linkDialogShow', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.link),\n        tooltip: this.lang.link.edit,\n        click: this.context.createInvokeHandler('linkDialog.show')\n      }).render();\n    });\n\n    this.context.memo('button.unlink', () => {\n      return this.button({\n        contents: this.ui.icon(this.options.icons.unlink),\n        tooltip: this.lang.link.unlink,\n        click: this.context.createInvokeHandler('editor.unlink')\n      }).render();\n    });\n  }\n\n  /**\n   * table : [\n   *  ['add', ['addRowDown', 'addRowUp', 'addColLeft', 'addColRight']],\n   *  ['delete', ['deleteRow', 'deleteCol', 'deleteTable']]\n   * ],\n   */\n  addTablePopoverButtons() {\n    this.context.memo('button.addRowUp', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.rowAbove),\n        tooltip: this.lang.table.addRowAbove,\n        click: this.context.createInvokeHandler('editor.addRow', 'top')\n      }).render();\n    });\n    this.context.memo('button.addRowDown', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.rowBelow),\n        tooltip: this.lang.table.addRowBelow,\n        click: this.context.createInvokeHandler('editor.addRow', 'bottom')\n      }).render();\n    });\n    this.context.memo('button.addColLeft', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.colBefore),\n        tooltip: this.lang.table.addColLeft,\n        click: this.context.createInvokeHandler('editor.addCol', 'left')\n      }).render();\n    });\n    this.context.memo('button.addColRight', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.colAfter),\n        tooltip: this.lang.table.addColRight,\n        click: this.context.createInvokeHandler('editor.addCol', 'right')\n      }).render();\n    });\n    this.context.memo('button.deleteRow', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.rowRemove),\n        tooltip: this.lang.table.delRow,\n        click: this.context.createInvokeHandler('editor.deleteRow')\n      }).render();\n    });\n    this.context.memo('button.deleteCol', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.colRemove),\n        tooltip: this.lang.table.delCol,\n        click: this.context.createInvokeHandler('editor.deleteCol')\n      }).render();\n    });\n    this.context.memo('button.deleteTable', () => {\n      return this.button({\n        className: 'btn-md',\n        contents: this.ui.icon(this.options.icons.trash),\n        tooltip: this.lang.table.delTable,\n        click: this.context.createInvokeHandler('editor.deleteTable')\n      }).render();\n    });\n  }\n\n  build($container, groups) {\n    for (let groupIdx = 0, groupLen = groups.length; groupIdx < groupLen; groupIdx++) {\n      const group = groups[groupIdx];\n      const groupName = $.isArray(group) ? group[0] : group;\n      const buttons = $.isArray(group) ? ((group.length === 1) ? [group[0]] : group[1]) : [group];\n\n      const $group = this.ui.buttonGroup({\n        className: 'note-' + groupName\n      }).render();\n\n      for (let idx = 0, len = buttons.length; idx < len; idx++) {\n        const btn = this.context.memo('button.' + buttons[idx]);\n        if (btn) {\n          $group.append(typeof btn === 'function' ? btn(this.context) : btn);\n        }\n      }\n      $group.appendTo($container);\n    }\n  }\n\n  /**\n   * @param {jQuery} [$container]\n   */\n  updateCurrentStyle($container) {\n    const $cont = $container || this.$toolbar;\n\n    const styleInfo = this.context.invoke('editor.currentStyle');\n    this.updateBtnStates($cont, {\n      '.note-btn-bold': () => {\n        return styleInfo['font-bold'] === 'bold';\n      },\n      '.note-btn-italic': () => {\n        return styleInfo['font-italic'] === 'italic';\n      },\n      '.note-btn-underline': () => {\n        return styleInfo['font-underline'] === 'underline';\n      },\n      '.note-btn-subscript': () => {\n        return styleInfo['font-subscript'] === 'subscript';\n      },\n      '.note-btn-superscript': () => {\n        return styleInfo['font-superscript'] === 'superscript';\n      },\n      '.note-btn-strikethrough': () => {\n        return styleInfo['font-strikethrough'] === 'strikethrough';\n      }\n    });\n\n    if (styleInfo['font-family']) {\n      const fontNames = styleInfo['font-family'].split(',').map((name) => {\n        return name.replace(/[\\'\\\"]/g, '')\n          .replace(/\\s+$/, '')\n          .replace(/^\\s+/, '');\n      });\n      const fontName = lists.find(fontNames, this.isFontInstalled.bind(this));\n\n      $cont.find('.dropdown-fontname a').each((idx, item) => {\n        const $item = $(item);\n        // always compare string to avoid creating another func.\n        const isChecked = ($item.data('value') + '') === (fontName + '');\n        $item.toggleClass('checked', isChecked);\n      });\n      $cont.find('.note-current-fontname').text(fontName).css('font-family', fontName);\n    }\n\n    if (styleInfo['font-size']) {\n      const fontSize = styleInfo['font-size'];\n      $cont.find('.dropdown-fontsize a').each((idx, item) => {\n        const $item = $(item);\n        // always compare with string to avoid creating another func.\n        const isChecked = ($item.data('value') + '') === (fontSize + '');\n        $item.toggleClass('checked', isChecked);\n      });\n      $cont.find('.note-current-fontsize').text(fontSize);\n    }\n\n    if (styleInfo['line-height']) {\n      const lineHeight = styleInfo['line-height'];\n      $cont.find('.dropdown-line-height li a').each((idx, item) => {\n        // always compare with string to avoid creating another func.\n        const isChecked = ($(item).data('value') + '') === (lineHeight + '');\n        this.className = isChecked ? 'checked' : '';\n      });\n    }\n  }\n\n  updateBtnStates($container, infos) {\n    $.each(infos, (selector, pred) => {\n      this.ui.toggleBtnActive($container.find(selector), pred());\n    });\n  }\n\n  tableMoveHandler(event) {\n    const PX_PER_EM = 18;\n    const $picker = $(event.target.parentNode); // target is mousecatcher\n    const $dimensionDisplay = $picker.next();\n    const $catcher = $picker.find('.note-dimension-picker-mousecatcher');\n    const $highlighted = $picker.find('.note-dimension-picker-highlighted');\n    const $unhighlighted = $picker.find('.note-dimension-picker-unhighlighted');\n\n    let posOffset;\n    // HTML5 with jQuery - e.offsetX is undefined in Firefox\n    if (event.offsetX === undefined) {\n      const posCatcher = $(event.target).offset();\n      posOffset = {\n        x: event.pageX - posCatcher.left,\n        y: event.pageY - posCatcher.top\n      };\n    } else {\n      posOffset = {\n        x: event.offsetX,\n        y: event.offsetY\n      };\n    }\n\n    const dim = {\n      c: Math.ceil(posOffset.x / PX_PER_EM) || 1,\n      r: Math.ceil(posOffset.y / PX_PER_EM) || 1\n    };\n\n    $highlighted.css({ width: dim.c + 'em', height: dim.r + 'em' });\n    $catcher.data('value', dim.c + 'x' + dim.r);\n\n    if (dim.c > 3 && dim.c < this.options.insertTableMaxSize.col) {\n      $unhighlighted.css({ width: dim.c + 1 + 'em' });\n    }\n\n    if (dim.r > 3 && dim.r < this.options.insertTableMaxSize.row) {\n      $unhighlighted.css({ height: dim.r + 1 + 'em' });\n    }\n\n    $dimensionDisplay.html(dim.c + ' x ' + dim.r);\n  }\n}\n", "import $ from 'jquery';\nexport default class Toolbar {\n  constructor(context) {\n    this.context = context;\n\n    this.$window = $(window);\n    this.$document = $(document);\n\n    this.ui = $.summernote.ui;\n    this.$note = context.layoutInfo.note;\n    this.$editor = context.layoutInfo.editor;\n    this.$toolbar = context.layoutInfo.toolbar;\n    this.options = context.options;\n\n    this.followScroll = this.followScroll.bind(this);\n  }\n\n  shouldInitialize() {\n    return !this.options.airMode;\n  }\n\n  initialize() {\n    this.options.toolbar = this.options.toolbar || [];\n\n    if (!this.options.toolbar.length) {\n      this.$toolbar.hide();\n    } else {\n      this.context.invoke('buttons.build', this.$toolbar, this.options.toolbar);\n    }\n\n    if (this.options.toolbarContainer) {\n      this.$toolbar.appendTo(this.options.toolbarContainer);\n    }\n\n    this.changeContainer(false);\n\n    this.$note.on('summernote.keyup summernote.mouseup summernote.change', () => {\n      this.context.invoke('buttons.updateCurrentStyle');\n    });\n\n    this.context.invoke('buttons.updateCurrentStyle');\n    if (this.options.followingToolbar) {\n      this.$window.on('scroll resize', this.followScroll);\n    }\n  }\n\n  destroy() {\n    this.$toolbar.children().remove();\n\n    if (this.options.followingToolbar) {\n      this.$window.off('scroll resize', this.followScroll);\n    }\n  }\n\n  followScroll() {\n    if (this.$editor.hasClass('fullscreen')) {\n      return false;\n    }\n\n    const $toolbarWrapper = this.$toolbar.parent('.note-toolbar-wrapper');\n    const editorHeight = this.$editor.outerHeight();\n    const editorWidth = this.$editor.width();\n\n    const toolbarHeight = this.$toolbar.height();\n    $toolbarWrapper.css({\n      height: toolbarHeight\n    });\n\n    // check if the web app is currently using another static bar\n    let otherBarHeight = 0;\n    if (this.options.otherStaticBar) {\n      otherBarHeight = $(this.options.otherStaticBar).outerHeight();\n    }\n\n    const currentOffset = this.$document.scrollTop();\n    const editorOffsetTop = this.$editor.offset().top;\n    const editorOffsetBottom = editorOffsetTop + editorHeight;\n    const activateOffset = editorOffsetTop - otherBarHeight;\n    const deactivateOffsetBottom = editorOffsetBottom - otherBarHeight - toolbarHeight;\n\n    if ((currentOffset > activateOffset) && (currentOffset < deactivateOffsetBottom)) {\n      this.$toolbar.css({\n        position: 'fixed',\n        top: otherBarHeight,\n        width: editorWidth\n      });\n    } else {\n      this.$toolbar.css({\n        position: 'relative',\n        top: 0,\n        width: '100%'\n      });\n    }\n  }\n\n  changeContainer(isFullscreen) {\n    if (isFullscreen) {\n      this.$toolbar.prependTo(this.$editor);\n    } else {\n      if (this.options.toolbarContainer) {\n        this.$toolbar.appendTo(this.options.toolbarContainer);\n      }\n    }\n  }\n\n  updateFullscreen(isFullscreen) {\n    this.ui.toggleBtnActive(this.$toolbar.find('.btn-fullscreen'), isFullscreen);\n\n    this.changeContainer(isFullscreen);\n  }\n\n  updateCodeview(isCodeview) {\n    this.ui.toggleBtnActive(this.$toolbar.find('.btn-codeview'), isCodeview);\n    if (isCodeview) {\n      this.deactivate();\n    } else {\n      this.activate();\n    }\n  }\n\n  activate(isIncludeCodeview) {\n    let $btn = this.$toolbar.find('button');\n    if (!isIncludeCodeview) {\n      $btn = $btn.not('.btn-codeview');\n    }\n    this.ui.toggleBtn($btn, true);\n  }\n\n  deactivate(isIncludeCodeview) {\n    let $btn = this.$toolbar.find('button');\n    if (!isIncludeCodeview) {\n      $btn = $btn.not('.btn-codeview');\n    }\n    this.ui.toggleBtn($btn, false);\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\n\nexport default class LinkDialog {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n\n    context.memo('help.linkDialog.show', this.options.langInfo.help['linkDialog.show']);\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.$editor;\n\n    const body = [\n      '<div class=\"form-group note-form-group\">',\n      `<label class=\"note-form-label\">${this.lang.link.textToDisplay}</label>`,\n      '<input class=\"note-link-text form-control note-form-control note-input\" type=\"text\" />',\n      '</div>',\n      '<div class=\"form-group note-form-group\">',\n      `<label class=\"note-form-label\">${this.lang.link.url}</label>`,\n      '<input class=\"note-link-url form-control note-form-control note-input\" type=\"text\" value=\"http://\" />',\n      '</div>',\n      !this.options.disableLinkTarget\n        ? $('<div/>').append(this.ui.checkbox({\n          className: 'sn-checkbox-open-in-new-window',\n          text: this.lang.link.openInNewWindow,\n          checked: true\n        }).render()).html()\n        : ''\n    ].join('');\n\n    const buttonClass = 'btn btn-primary note-btn note-btn-primary note-link-btn';\n    const footer = `<input type=\"button\" href=\"#\" class=\"${buttonClass}\" value=\"${this.lang.link.insert}\" disabled>`;\n\n    this.$dialog = this.ui.dialog({\n      className: 'link-dialog',\n      title: this.lang.link.insert,\n      fade: this.options.dialogsFade,\n      body: body,\n      footer: footer\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  bindEnterKey($input, $btn) {\n    $input.on('keypress', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        event.preventDefault();\n        $btn.trigger('click');\n      }\n    });\n  }\n\n  /**\n   * toggle update button\n   */\n  toggleLinkBtn($linkBtn, $linkText, $linkUrl) {\n    this.ui.toggleBtn($linkBtn, $linkText.val() && $linkUrl.val());\n  }\n\n  /**\n   * Show link dialog and set event handlers on dialog controls.\n   *\n   * @param {Object} linkInfo\n   * @return {Promise}\n   */\n  showLinkDialog(linkInfo) {\n    return $.Deferred((deferred) => {\n      const $linkText = this.$dialog.find('.note-link-text');\n      const $linkUrl = this.$dialog.find('.note-link-url');\n      const $linkBtn = this.$dialog.find('.note-link-btn');\n      const $openInNewWindow = this.$dialog\n        .find('.sn-checkbox-open-in-new-window input[type=checkbox]');\n\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n\n        // if no url was given, copy text to url\n        if (!linkInfo.url) {\n          linkInfo.url = linkInfo.text;\n        }\n\n        $linkText.val(linkInfo.text);\n\n        const handleLinkTextUpdate = () => {\n          this.toggleLinkBtn($linkBtn, $linkText, $linkUrl);\n          // if linktext was modified by keyup,\n          // stop cloning text from linkUrl\n          linkInfo.text = $linkText.val();\n        };\n\n        $linkText.on('input', handleLinkTextUpdate).on('paste', () => {\n          setTimeout(handleLinkTextUpdate, 0);\n        });\n\n        const handleLinkUrlUpdate = () => {\n          this.toggleLinkBtn($linkBtn, $linkText, $linkUrl);\n          // display same link on `Text to display` input\n          // when create a new link\n          if (!linkInfo.text) {\n            $linkText.val($linkUrl.val());\n          }\n        };\n\n        $linkUrl.on('input', handleLinkUrlUpdate).on('paste', () => {\n          setTimeout(handleLinkUrlUpdate, 0);\n        }).val(linkInfo.url);\n\n        if (!env.isSupportTouch) {\n          $linkUrl.trigger('focus');\n        }\n\n        this.toggleLinkBtn($linkBtn, $linkText, $linkUrl);\n        this.bindEnterKey($linkUrl, $linkBtn);\n        this.bindEnterKey($linkText, $linkBtn);\n\n        const isNewWindowChecked = linkInfo.isNewWindow !== undefined\n          ? linkInfo.isNewWindow : this.context.options.linkTargetBlank;\n\n        $openInNewWindow.prop('checked', isNewWindowChecked);\n\n        $linkBtn.one('click', (event) => {\n          event.preventDefault();\n\n          deferred.resolve({\n            range: linkInfo.range,\n            url: $linkUrl.val(),\n            text: $linkText.val(),\n            isNewWindow: $openInNewWindow.is(':checked')\n          });\n          this.ui.hideDialog(this.$dialog);\n        });\n      });\n\n      this.ui.onDialogHidden(this.$dialog, () => {\n        // detach events\n        $linkText.off('input paste keypress');\n        $linkUrl.off('input paste keypress');\n        $linkBtn.off('click');\n\n        if (deferred.state() === 'pending') {\n          deferred.reject();\n        }\n      });\n\n      this.ui.showDialog(this.$dialog);\n    }).promise();\n  }\n\n  /**\n   * @param {Object} layoutInfo\n   */\n  show() {\n    const linkInfo = this.context.invoke('editor.getLinkInfo');\n\n    this.context.invoke('editor.saveRange');\n    this.showLinkDialog(linkInfo).then((linkInfo) => {\n      this.context.invoke('editor.restoreRange');\n      this.context.invoke('editor.createLink', linkInfo);\n    }).fail(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nexport default class LinkPopover {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.options = context.options;\n    this.events = {\n      'summernote.keyup summernote.mouseup summernote.change summernote.scroll': () => {\n        this.update();\n      },\n      'summernote.disable summernote.dialog.shown': () => {\n        this.hide();\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return !lists.isEmpty(this.options.popover.link);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-link-popover',\n      callback: ($node) => {\n        const $content = $node.find('.popover-content,.note-popover-content');\n        $content.prepend('<span><a target=\"_blank\"></a>&nbsp;</span>');\n      }\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content,.note-popover-content');\n\n    this.context.invoke('buttons.build', $content, this.options.popover.link);\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update() {\n    // Prevent focusing on editable when invoke('code') is executed\n    if (!this.context.invoke('editor.hasFocus')) {\n      this.hide();\n      return;\n    }\n\n    const rng = this.context.invoke('editor.createRange');\n    if (rng.isCollapsed() && rng.isOnAnchor()) {\n      const anchor = dom.ancestor(rng.sc, dom.isAnchor);\n      const href = $(anchor).attr('href');\n      this.$popover.find('a').attr('href', href).html(href);\n\n      const pos = dom.posFromPlaceholder(anchor);\n      this.$popover.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top\n      });\n    } else {\n      this.hide();\n    }\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\n\nexport default class ImageDialog {\n  constructor(context) {\n    this.context = context;\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.$editor;\n\n    let imageLimitation = '';\n    if (this.options.maximumImageFileSize) {\n      const unit = Math.floor(Math.log(this.options.maximumImageFileSize) / Math.log(1024));\n      const readableSize = (this.options.maximumImageFileSize / Math.pow(1024, unit)).toFixed(2) * 1 +\n                         ' ' + ' KMGTP'[unit] + 'B';\n      imageLimitation = `<small>${this.lang.image.maximumFileSize + ' : ' + readableSize}</small>`;\n    }\n\n    const body = [\n      '<div class=\"form-group note-form-group note-group-select-from-files\">',\n      '<label class=\"note-form-label\">' + this.lang.image.selectFromFiles + '</label>',\n      '<input class=\"note-image-input note-form-control note-input\" ',\n      ' type=\"file\" name=\"files\" accept=\"image/*\" multiple=\"multiple\" />',\n      imageLimitation,\n      '</div>',\n      '<div class=\"form-group note-group-image-url\" style=\"overflow:auto;\">',\n      '<label class=\"note-form-label\">' + this.lang.image.url + '</label>',\n      '<input class=\"note-image-url form-control note-form-control note-input ',\n      ' col-md-12\" type=\"text\" />',\n      '</div>'\n    ].join('');\n    const buttonClass = 'btn btn-primary note-btn note-btn-primary note-image-btn';\n    const footer = `<input type=\"button\" href=\"#\" class=\"${buttonClass}\" value=\"${this.lang.image.insert}\" disabled>`;\n\n    this.$dialog = this.ui.dialog({\n      title: this.lang.image.insert,\n      fade: this.options.dialogsFade,\n      body: body,\n      footer: footer\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  bindEnterKey($input, $btn) {\n    $input.on('keypress', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        event.preventDefault();\n        $btn.trigger('click');\n      }\n    });\n  }\n\n  show() {\n    this.context.invoke('editor.saveRange');\n    this.showImageDialog().then((data) => {\n      // [workaround] hide dialog before restore range for IE range focus\n      this.ui.hideDialog(this.$dialog);\n      this.context.invoke('editor.restoreRange');\n\n      if (typeof data === 'string') { // image url\n        // If onImageLinkInsert set,\n        if (this.options.callbacks.onImageLinkInsert) {\n          this.context.triggerEvent('image.link.insert', data);\n        } else {\n          this.context.invoke('editor.insertImage', data);\n        }\n      } else { // array of files\n        // If onImageUpload set,\n        if (this.options.callbacks.onImageUpload) {\n          this.context.triggerEvent('image.upload', data);\n        } else {\n          // else insert Image as dataURL\n          this.context.invoke('editor.insertImagesAsDataURL', data);\n        }\n      }\n    }).fail(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n\n  /**\n   * show image dialog\n   *\n   * @param {jQuery} $dialog\n   * @return {Promise}\n   */\n  showImageDialog() {\n    return $.Deferred((deferred) => {\n      const $imageInput = this.$dialog.find('.note-image-input');\n      const $imageUrl = this.$dialog.find('.note-image-url');\n      const $imageBtn = this.$dialog.find('.note-image-btn');\n\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n\n        // Cloning imageInput to clear element.\n        $imageInput.replaceWith($imageInput.clone().on('change', (event) => {\n          deferred.resolve(event.target.files || event.target.value);\n        }).val(''));\n\n        $imageBtn.click((event) => {\n          event.preventDefault();\n\n          deferred.resolve($imageUrl.val());\n        });\n\n        $imageUrl.on('keyup paste', () => {\n          const url = $imageUrl.val();\n          this.ui.toggleBtn($imageBtn, url);\n        }).val('');\n\n        if (!env.isSupportTouch) {\n          $imageUrl.trigger('focus');\n        }\n        this.bindEnterKey($imageUrl, $imageBtn);\n      });\n\n      this.ui.onDialogHidden(this.$dialog, () => {\n        $imageInput.off('change');\n        $imageUrl.off('keyup paste keypress');\n        $imageBtn.off('click');\n\n        if (deferred.state() === 'pending') {\n          deferred.reject();\n        }\n      });\n\n      this.ui.showDialog(this.$dialog);\n    });\n  }\n}\n", "import $ from 'jquery';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\n/**\n * Image popover module\n *  mouse events that show/hide popover will be handled by Handle.js.\n *  Handle.js will receive the events and invoke 'imagePopover.update'.\n */\nexport default class ImagePopover {\n  constructor(context) {\n    this.context = context;\n    this.ui = $.summernote.ui;\n\n    this.editable = context.layoutInfo.editable[0];\n    this.options = context.options;\n\n    this.events = {\n      'summernote.disable': () => {\n        this.hide();\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return !lists.isEmpty(this.options.popover.image);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-image-popover'\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content,.note-popover-content');\n    this.context.invoke('buttons.build', $content, this.options.popover.image);\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update(target) {\n    if (dom.isImg(target)) {\n      const pos = dom.posFromPlaceholder(target);\n      const posEditor = dom.posFromPlaceholder(this.editable);\n      this.$popover.css({\n        display: 'block',\n        left: this.options.popatmouse ? event.pageX - 20 : pos.left,\n        top: this.options.popatmouse ? event.pageY : Math.min(pos.top, posEditor.top)\n      });\n    } else {\n      this.hide();\n    }\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nexport default class TablePopover {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.options = context.options;\n    this.events = {\n      'summernote.mousedown': (we, e) => {\n        this.update(e.target);\n      },\n      'summernote.keyup summernote.scroll summernote.change': () => {\n        this.update();\n      },\n      'summernote.disable': () => {\n        this.hide();\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return !lists.isEmpty(this.options.popover.table);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-table-popover'\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content,.note-popover-content');\n\n    this.context.invoke('buttons.build', $content, this.options.popover.table);\n\n    // [workaround] Disable Firefox's default table editor\n    if (env.isFF) {\n      document.execCommand('enableInlineTableEditing', false, false);\n    }\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update(target) {\n    if (this.context.isDisabled()) {\n      return false;\n    }\n\n    const isCell = dom.isCell(target);\n\n    if (isCell) {\n      const pos = dom.posFromPlaceholder(target);\n      this.$popover.css({\n        display: 'block',\n        left: pos.left,\n        top: pos.top\n      });\n    } else {\n      this.hide();\n    }\n\n    return isCell;\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport key from '../core/key';\n\nexport default class VideoDialog {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.$editor;\n\n    const body = [\n      '<div class=\"form-group note-form-group row-fluid\">',\n      `<label class=\"note-form-label\">${this.lang.video.url} <small class=\"text-muted\">${this.lang.video.providers}</small></label>`,\n      '<input class=\"note-video-url form-control note-form-control note-input\" type=\"text\" />',\n      '</div>'\n    ].join('');\n    const buttonClass = 'btn btn-primary note-btn note-btn-primary note-video-btn';\n    const footer = `<input type=\"button\" href=\"#\" class=\"${buttonClass}\" value=\"${this.lang.video.insert}\" disabled>`;\n\n    this.$dialog = this.ui.dialog({\n      title: this.lang.video.insert,\n      fade: this.options.dialogsFade,\n      body: body,\n      footer: footer\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  bindEnterKey($input, $btn) {\n    $input.on('keypress', (event) => {\n      if (event.keyCode === key.code.ENTER) {\n        event.preventDefault();\n        $btn.trigger('click');\n      }\n    });\n  }\n\n  createVideoNode(url) {\n    // video url patterns(youtube, instagram, vimeo, dailymotion, youku, mp4, ogg, webm)\n    const ytRegExp = /\\/\\/(?:www\\.)?(?:youtu\\.be\\/|youtube\\.com\\/(?:embed\\/|v\\/|watch\\?v=|watch\\?.+&v=))([\\w|-]{11})(?:(?:[\\?&]t=)(\\S+))?$/;\n    const ytRegExpForStart = /^(?:(\\d+)h)?(?:(\\d+)m)?(?:(\\d+)s)?$/;\n    const ytMatch = url.match(ytRegExp);\n\n    const igRegExp = /(?:www\\.|\\/\\/)instagram\\.com\\/p\\/(.[a-zA-Z0-9_-]*)/;\n    const igMatch = url.match(igRegExp);\n\n    const vRegExp = /\\/\\/vine\\.co\\/v\\/([a-zA-Z0-9]+)/;\n    const vMatch = url.match(vRegExp);\n\n    const vimRegExp = /\\/\\/(player\\.)?vimeo\\.com\\/([a-z]*\\/)*(\\d+)[?]?.*/;\n    const vimMatch = url.match(vimRegExp);\n\n    const dmRegExp = /.+dailymotion.com\\/(video|hub)\\/([^_]+)[^#]*(#video=([^_&]+))?/;\n    const dmMatch = url.match(dmRegExp);\n\n    const youkuRegExp = /\\/\\/v\\.youku\\.com\\/v_show\\/id_(\\w+)=*\\.html/;\n    const youkuMatch = url.match(youkuRegExp);\n\n    const qqRegExp = /\\/\\/v\\.qq\\.com.*?vid=(.+)/;\n    const qqMatch = url.match(qqRegExp);\n\n    const qqRegExp2 = /\\/\\/v\\.qq\\.com\\/x?\\/?(page|cover).*?\\/([^\\/]+)\\.html\\??.*/;\n    const qqMatch2 = url.match(qqRegExp2);\n\n    const mp4RegExp = /^.+.(mp4|m4v)$/;\n    const mp4Match = url.match(mp4RegExp);\n\n    const oggRegExp = /^.+.(ogg|ogv)$/;\n    const oggMatch = url.match(oggRegExp);\n\n    const webmRegExp = /^.+.(webm)$/;\n    const webmMatch = url.match(webmRegExp);\n\n    let $video;\n    if (ytMatch && ytMatch[1].length === 11) {\n      const youtubeId = ytMatch[1];\n      var start = 0;\n      if (typeof ytMatch[2] !== 'undefined') {\n        const ytMatchForStart = ytMatch[2].match(ytRegExpForStart);\n        if (ytMatchForStart) {\n          for (var n = [3600, 60, 1], i = 0, r = n.length; i < r; i++) {\n            start += (typeof ytMatchForStart[i + 1] !== 'undefined' ? n[i] * parseInt(ytMatchForStart[i + 1], 10) : 0);\n          }\n        }\n      }\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', '//www.youtube.com/embed/' + youtubeId + (start > 0 ? '?start=' + start : ''))\n        .attr('width', '640').attr('height', '360');\n    } else if (igMatch && igMatch[0].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', 'https://instagram.com/p/' + igMatch[1] + '/embed/')\n        .attr('width', '612').attr('height', '710')\n        .attr('scrolling', 'no')\n        .attr('allowtransparency', 'true');\n    } else if (vMatch && vMatch[0].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', vMatch[0] + '/embed/simple')\n        .attr('width', '600').attr('height', '600')\n        .attr('class', 'vine-embed');\n    } else if (vimMatch && vimMatch[3].length) {\n      $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')\n        .attr('frameborder', 0)\n        .attr('src', '//player.vimeo.com/video/' + vimMatch[3])\n        .attr('width', '640').attr('height', '360');\n    } else if (dmMatch && dmMatch[2].length) {\n      $video = $('<iframe>')\n        .attr('frameborder', 0)\n        .attr('src', '//www.dailymotion.com/embed/video/' + dmMatch[2])\n        .attr('width', '640').attr('height', '360');\n    } else if (youkuMatch && youkuMatch[1].length) {\n      $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')\n        .attr('frameborder', 0)\n        .attr('height', '498')\n        .attr('width', '510')\n        .attr('src', '//player.youku.com/embed/' + youkuMatch[1]);\n    } else if ((qqMatch && qqMatch[1].length) || (qqMatch2 && qqMatch2[2].length)) {\n      const vid = ((qqMatch && qqMatch[1].length) ? qqMatch[1] : qqMatch2[2]);\n      $video = $('<iframe webkitallowfullscreen mozallowfullscreen allowfullscreen>')\n        .attr('frameborder', 0)\n        .attr('height', '310')\n        .attr('width', '500')\n        .attr('src', 'http://v.qq.com/iframe/player.html?vid=' + vid + '&amp;auto=0');\n    } else if (mp4Match || oggMatch || webmMatch) {\n      $video = $('<video controls>')\n        .attr('src', url)\n        .attr('width', '640').attr('height', '360');\n    } else {\n      // this is not a known video link. Now what, Cat? Now what?\n      return false;\n    }\n\n    $video.addClass('note-video-clip');\n\n    return $video[0];\n  }\n\n  show() {\n    const text = this.context.invoke('editor.getSelectedText');\n    this.context.invoke('editor.saveRange');\n    this.showVideoDialog(text).then((url) => {\n      // [workaround] hide dialog before restore range for IE range focus\n      this.ui.hideDialog(this.$dialog);\n      this.context.invoke('editor.restoreRange');\n\n      // build node\n      const $node = this.createVideoNode(url);\n\n      if ($node) {\n        // insert video node\n        this.context.invoke('editor.insertNode', $node);\n      }\n    }).fail(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n\n  /**\n   * show image dialog\n   *\n   * @param {jQuery} $dialog\n   * @return {Promise}\n   */\n  showVideoDialog(text) {\n    return $.Deferred((deferred) => {\n      const $videoUrl = this.$dialog.find('.note-video-url');\n      const $videoBtn = this.$dialog.find('.note-video-btn');\n\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n\n        $videoUrl.val(text).on('input', () => {\n          this.ui.toggleBtn($videoBtn, $videoUrl.val());\n        });\n\n        if (!env.isSupportTouch) {\n          $videoUrl.trigger('focus');\n        }\n\n        $videoBtn.click((event) => {\n          event.preventDefault();\n\n          deferred.resolve($videoUrl.val());\n        });\n\n        this.bindEnterKey($videoUrl, $videoBtn);\n      });\n\n      this.ui.onDialogHidden(this.$dialog, () => {\n        $videoUrl.off('input');\n        $videoBtn.off('click');\n\n        if (deferred.state() === 'pending') {\n          deferred.reject();\n        }\n      });\n\n      this.ui.showDialog(this.$dialog);\n    });\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\n\nexport default class HelpDialog {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$body = $(document.body);\n    this.$editor = context.layoutInfo.editor;\n    this.options = context.options;\n    this.lang = this.options.langInfo;\n  }\n\n  initialize() {\n    const $container = this.options.dialogsInBody ? this.$body : this.$editor;\n\n    const body = [\n      '<p class=\"text-center\">',\n      '<a href=\"http://summernote.org/\" target=\"_blank\">Summernote @@VERSION@@</a> · ',\n      '<a href=\"https://github.com/summernote/summernote\" target=\"_blank\">Project</a> · ',\n      '<a href=\"https://github.com/summernote/summernote/issues\" target=\"_blank\">Issues</a>',\n      '</p>'\n    ].join('');\n\n    this.$dialog = this.ui.dialog({\n      title: this.lang.options.help,\n      fade: this.options.dialogsFade,\n      body: this.createShortcutList(),\n      footer: body,\n      callback: ($node) => {\n        $node.find('.modal-body,.note-modal-body').css({\n          'max-height': 300,\n          'overflow': 'scroll'\n        });\n      }\n    }).render().appendTo($container);\n  }\n\n  destroy() {\n    this.ui.hideDialog(this.$dialog);\n    this.$dialog.remove();\n  }\n\n  createShortcutList() {\n    const keyMap = this.options.keyMap[env.isMac ? 'mac' : 'pc'];\n    return Object.keys(keyMap).map((key) => {\n      const command = keyMap[key];\n      const $row = $('<div><div class=\"help-list-item\"/></div>');\n      $row.append($('<label><kbd>' + key + '</kdb></label>').css({\n        'width': 180,\n        'margin-right': 10\n      })).append($('<span/>').html(this.context.memo('help.' + command) || command));\n      return $row.html();\n    }).join('');\n  }\n\n  /**\n   * show help dialog\n   *\n   * @return {Promise}\n   */\n  showHelpDialog() {\n    return $.Deferred((deferred) => {\n      this.ui.onDialogShown(this.$dialog, () => {\n        this.context.triggerEvent('dialog.shown');\n        deferred.resolve();\n      });\n      this.ui.showDialog(this.$dialog);\n    }).promise();\n  }\n\n  show() {\n    this.context.invoke('editor.saveRange');\n    this.showHelpDialog().then(() => {\n      this.context.invoke('editor.restoreRange');\n    });\n  }\n}\n", "import $ from 'jquery';\nimport env from '../core/env';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\n\nconst AIR_MODE_POPOVER_X_OFFSET = 20;\n\nexport default class AirPopover {\n  constructor(context) {\n    this.context = context;\n    this.ui = $.summernote.ui;\n    this.options = context.options;\n    this.events = {\n      'summernote.keyup summernote.mouseup summernote.scroll': () => {\n        this.update();\n      },\n      'summernote.disable summernote.change summernote.dialog.shown': () => {\n        this.hide();\n      },\n      'summernote.focusout': (we, e) => {\n        // [workaround] Firefox doesn't support relatedTarget on focusout\n        //  - Ignore hide action on focus out in FF.\n        if (env.isFF) {\n          return;\n        }\n\n        if (!e.relatedTarget || !dom.ancestor(e.relatedTarget, func.eq(this.$popover[0]))) {\n          this.hide();\n        }\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return this.options.airMode && !lists.isEmpty(this.options.popover.air);\n  }\n\n  initialize() {\n    this.$popover = this.ui.popover({\n      className: 'note-air-popover'\n    }).render().appendTo(this.options.container);\n    const $content = this.$popover.find('.popover-content');\n\n    this.context.invoke('buttons.build', $content, this.options.popover.air);\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  update() {\n    const styleInfo = this.context.invoke('editor.currentStyle');\n    if (styleInfo.range && !styleInfo.range.isCollapsed()) {\n      const rect = lists.last(styleInfo.range.getClientRects());\n      if (rect) {\n        const bnd = func.rect2bnd(rect);\n        this.$popover.css({\n          display: 'block',\n          left: Math.max(bnd.left + bnd.width / 2, 0) - AIR_MODE_POPOVER_X_OFFSET,\n          top: bnd.top + bnd.height\n        });\n        this.context.invoke('buttons.updateCurrentStyle', this.$popover);\n      }\n    } else {\n      this.hide();\n    }\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport func from '../core/func';\nimport lists from '../core/lists';\nimport dom from '../core/dom';\nimport range from '../core/range';\nimport key from '../core/key';\n\nconst POPOVER_DIST = 5;\n\nexport default class HintPopover {\n  constructor(context) {\n    this.context = context;\n\n    this.ui = $.summernote.ui;\n    this.$editable = context.layoutInfo.editable;\n    this.options = context.options;\n    this.hint = this.options.hint || [];\n    this.direction = this.options.hintDirection || 'bottom';\n    this.hints = $.isArray(this.hint) ? this.hint : [this.hint];\n\n    this.events = {\n      'summernote.keyup': (we, e) => {\n        if (!e.isDefaultPrevented()) {\n          this.handleKeyup(e);\n        }\n      },\n      'summernote.keydown': (we, e) => {\n        this.handleKeydown(e);\n      },\n      'summernote.disable summernote.dialog.shown': () => {\n        this.hide();\n      }\n    };\n  }\n\n  shouldInitialize() {\n    return this.hints.length > 0;\n  }\n\n  initialize() {\n    this.lastWordRange = null;\n    this.$popover = this.ui.popover({\n      className: 'note-hint-popover',\n      hideArrow: true,\n      direction: ''\n    }).render().appendTo(this.options.container);\n\n    this.$popover.hide();\n    this.$content = this.$popover.find('.popover-content,.note-popover-content');\n    this.$content.on('click', '.note-hint-item', (e) => {\n      this.$content.find('.active').removeClass('active');\n      $(e.currentTarget).addClass('active');\n      this.replace();\n    });\n  }\n\n  destroy() {\n    this.$popover.remove();\n  }\n\n  selectItem($item) {\n    this.$content.find('.active').removeClass('active');\n    $item.addClass('active');\n\n    this.$content[0].scrollTop = $item[0].offsetTop - (this.$content.innerHeight() / 2);\n  }\n\n  moveDown() {\n    const $current = this.$content.find('.note-hint-item.active');\n    const $next = $current.next();\n\n    if ($next.length) {\n      this.selectItem($next);\n    } else {\n      let $nextGroup = $current.parent().next();\n\n      if (!$nextGroup.length) {\n        $nextGroup = this.$content.find('.note-hint-group').first();\n      }\n\n      this.selectItem($nextGroup.find('.note-hint-item').first());\n    }\n  }\n\n  moveUp() {\n    const $current = this.$content.find('.note-hint-item.active');\n    const $prev = $current.prev();\n\n    if ($prev.length) {\n      this.selectItem($prev);\n    } else {\n      let $prevGroup = $current.parent().prev();\n\n      if (!$prevGroup.length) {\n        $prevGroup = this.$content.find('.note-hint-group').last();\n      }\n\n      this.selectItem($prevGroup.find('.note-hint-item').last());\n    }\n  }\n\n  replace() {\n    const $item = this.$content.find('.note-hint-item.active');\n\n    if ($item.length) {\n      const node = this.nodeFromItem($item);\n      // XXX: consider to move codes to editor for recording redo/undo.\n      this.lastWordRange.insertNode(node);\n      range.createFromNode(node).collapse().select();\n\n      this.lastWordRange = null;\n      this.hide();\n      this.context.triggerEvent('change', this.$editable.html(), this.$editable[0]);\n      this.context.invoke('editor.focus');\n    }\n  }\n\n  nodeFromItem($item) {\n    const hint = this.hints[$item.data('index')];\n    const item = $item.data('item');\n    let node = hint.content ? hint.content(item) : item;\n    if (typeof node === 'string') {\n      node = dom.createText(node);\n    }\n    return node;\n  }\n\n  createItemTemplates(hintIdx, items) {\n    const hint = this.hints[hintIdx];\n    return items.map((item, idx) => {\n      const $item = $('<div class=\"note-hint-item\"/>');\n      $item.append(hint.template ? hint.template(item) : item + '');\n      $item.data({\n        'index': hintIdx,\n        'item': item\n      });\n      return $item;\n    });\n  }\n\n  handleKeydown(e) {\n    if (!this.$popover.is(':visible')) {\n      return;\n    }\n\n    if (e.keyCode === key.code.ENTER) {\n      e.preventDefault();\n      this.replace();\n    } else if (e.keyCode === key.code.UP) {\n      e.preventDefault();\n      this.moveUp();\n    } else if (e.keyCode === key.code.DOWN) {\n      e.preventDefault();\n      this.moveDown();\n    }\n  }\n\n  searchKeyword(index, keyword, callback) {\n    const hint = this.hints[index];\n    if (hint && hint.match.test(keyword) && hint.search) {\n      const matches = hint.match.exec(keyword);\n      hint.search(matches[1], callback);\n    } else {\n      callback();\n    }\n  }\n\n  createGroup(idx, keyword) {\n    const $group = $('<div class=\"note-hint-group note-hint-group-' + idx + '\"/>');\n    this.searchKeyword(idx, keyword, (items) => {\n      items = items || [];\n      if (items.length) {\n        $group.html(this.createItemTemplates(idx, items));\n        this.show();\n      }\n    });\n\n    return $group;\n  }\n\n  handleKeyup(e) {\n    if (!lists.contains([key.code.ENTER, key.code.UP, key.code.DOWN], e.keyCode)) {\n      const wordRange = this.context.invoke('editor.createRange').getWordRange();\n      const keyword = wordRange.toString();\n      if (this.hints.length && keyword) {\n        this.$content.empty();\n\n        const bnd = func.rect2bnd(lists.last(wordRange.getClientRects()));\n        if (bnd) {\n          this.$popover.hide();\n          this.lastWordRange = wordRange;\n          this.hints.forEach((hint, idx) => {\n            if (hint.match.test(keyword)) {\n              this.createGroup(idx, keyword).appendTo(this.$content);\n            }\n          });\n          // select first .note-hint-item\n          this.$content.find('.note-hint-item:first').addClass('active');\n\n          // set position for popover after group is created\n          if (this.direction === 'top') {\n            this.$popover.css({\n              left: bnd.left,\n              top: bnd.top - this.$popover.outerHeight() - POPOVER_DIST\n            });\n          } else {\n            this.$popover.css({\n              left: bnd.left,\n              top: bnd.top + bnd.height + POPOVER_DIST\n            });\n          }\n        }\n      } else {\n        this.hide();\n      }\n    }\n  }\n\n  show() {\n    this.$popover.show();\n  }\n\n  hide() {\n    this.$popover.hide();\n  }\n}\n", "import $ from 'jquery';\nimport func from './core/func';\nimport lists from './core/lists';\nimport dom from './core/dom';\n\nexport default class Context {\n  /**\n   * @param {jQuery} $note\n   * @param {Object} options\n   */\n  constructor($note, options) {\n    this.ui = $.summernote.ui;\n    this.$note = $note;\n\n    this.memos = {};\n    this.modules = {};\n    this.layoutInfo = {};\n    this.options = options;\n\n    this.initialize();\n  }\n\n  /**\n   * create layout and initialize modules and other resources\n   */\n  initialize() {\n    this.layoutInfo = this.ui.createLayout(this.$note, this.options);\n    this._initialize();\n    this.$note.hide();\n    return this;\n  }\n\n  /**\n   * destroy modules and other resources and remove layout\n   */\n  destroy() {\n    this._destroy();\n    this.$note.removeData('summernote');\n    this.ui.removeLayout(this.$note, this.layoutInfo);\n  }\n\n  /**\n   * destory modules and other resources and initialize it again\n   */\n  reset() {\n    const disabled = this.isDisabled();\n    this.code(dom.emptyPara);\n    this._destroy();\n    this._initialize();\n\n    if (disabled) {\n      this.disable();\n    }\n  }\n\n  _initialize() {\n    // add optional buttons\n    const buttons = $.extend({}, this.options.buttons);\n    Object.keys(buttons).forEach((key) => {\n      this.memo('button.' + key, buttons[key]);\n    });\n\n    const modules = $.extend({}, this.options.modules, $.summernote.plugins || {});\n\n    // add and initialize modules\n    Object.keys(modules).forEach((key) => {\n      this.module(key, modules[key], true);\n    });\n\n    Object.keys(this.modules).forEach((key) => {\n      this.initializeModule(key);\n    });\n  }\n\n  _destroy() {\n    // destroy modules with reversed order\n    Object.keys(this.modules).reverse().forEach((key) => {\n      this.removeModule(key);\n    });\n\n    Object.keys(this.memos).forEach((key) => {\n      this.removeMemo(key);\n    });\n    // trigger custom onDestroy callback\n    this.triggerEvent('destroy', this);\n  }\n\n  code(html) {\n    const isActivated = this.invoke('codeview.isActivated');\n\n    if (html === undefined) {\n      this.invoke('codeview.sync');\n      return isActivated ? this.layoutInfo.codable.val() : this.layoutInfo.editable.html();\n    } else {\n      if (isActivated) {\n        this.layoutInfo.codable.val(html);\n      } else {\n        this.layoutInfo.editable.html(html);\n      }\n      this.$note.val(html);\n      this.triggerEvent('change', html);\n    }\n  }\n\n  isDisabled() {\n    return this.layoutInfo.editable.attr('contenteditable') === 'false';\n  }\n\n  enable() {\n    this.layoutInfo.editable.attr('contenteditable', true);\n    this.invoke('toolbar.activate', true);\n    this.triggerEvent('disable', false);\n  }\n\n  disable() {\n    // close codeview if codeview is opend\n    if (this.invoke('codeview.isActivated')) {\n      this.invoke('codeview.deactivate');\n    }\n    this.layoutInfo.editable.attr('contenteditable', false);\n    this.invoke('toolbar.deactivate', true);\n\n    this.triggerEvent('disable', true);\n  }\n\n  triggerEvent() {\n    const namespace = lists.head(arguments);\n    const args = lists.tail(lists.from(arguments));\n\n    const callback = this.options.callbacks[func.namespaceToCamel(namespace, 'on')];\n    if (callback) {\n      callback.apply(this.$note[0], args);\n    }\n    this.$note.trigger('summernote.' + namespace, args);\n  }\n\n  initializeModule(key) {\n    const module = this.modules[key];\n    module.shouldInitialize = module.shouldInitialize || func.ok;\n    if (!module.shouldInitialize()) {\n      return;\n    }\n\n    // initialize module\n    if (module.initialize) {\n      module.initialize();\n    }\n\n    // attach events\n    if (module.events) {\n      dom.attachEvents(this.$note, module.events);\n    }\n  }\n\n  module(key, ModuleClass, withoutIntialize) {\n    if (arguments.length === 1) {\n      return this.modules[key];\n    }\n\n    this.modules[key] = new ModuleClass(this);\n\n    if (!withoutIntialize) {\n      this.initializeModule(key);\n    }\n  }\n\n  removeModule(key) {\n    const module = this.modules[key];\n    if (module.shouldInitialize()) {\n      if (module.events) {\n        dom.detachEvents(this.$note, module.events);\n      }\n\n      if (module.destroy) {\n        module.destroy();\n      }\n    }\n\n    delete this.modules[key];\n  }\n\n  memo(key, obj) {\n    if (arguments.length === 1) {\n      return this.memos[key];\n    }\n    this.memos[key] = obj;\n  }\n\n  removeMemo(key) {\n    if (this.memos[key] && this.memos[key].destroy) {\n      this.memos[key].destroy();\n    }\n\n    delete this.memos[key];\n  }\n\n  /**\n   * Some buttons need to change their visual style immediately once they get pressed\n   */\n  createInvokeHandlerAndUpdateState(namespace, value) {\n    return (event) => {\n      this.createInvokeHandler(namespace, value)(event);\n      this.invoke('buttons.updateCurrentStyle');\n    };\n  }\n\n  createInvokeHandler(namespace, value) {\n    return (event) => {\n      event.preventDefault();\n      const $target = $(event.target);\n      this.invoke(namespace, value || $target.closest('[data-value]').data('value'), $target);\n    };\n  }\n\n  invoke() {\n    const namespace = lists.head(arguments);\n    const args = lists.tail(lists.from(arguments));\n\n    const splits = namespace.split('.');\n    const hasSeparator = splits.length > 1;\n    const moduleName = hasSeparator && lists.head(splits);\n    const methodName = hasSeparator ? lists.last(splits) : lists.head(splits);\n\n    const module = this.modules[moduleName || 'editor'];\n    if (!moduleName && this[methodName]) {\n      return this[methodName].apply(this, args);\n    } else if (module && module[methodName] && module.shouldInitialize()) {\n      return module[methodName].apply(module, args);\n    }\n  }\n}\n", "import $ from 'jquery';\nimport env from './base/core/env';\nimport lists from './base/core/lists';\nimport Context from './base/Context';\n\n$.fn.extend({\n  /**\n   * Summernote API\n   *\n   * @param {Object|String}\n   * @return {this}\n   */\n  summernote: function() {\n    const type = $.type(lists.head(arguments));\n    const isExternalAPICalled = type === 'string';\n    const hasInitOptions = type === 'object';\n\n    const options = $.extend({}, $.summernote.options, hasInitOptions ? lists.head(arguments) : {});\n\n    // Update options\n    options.langInfo = $.extend(true, {}, $.summernote.lang['en-US'], $.summernote.lang[options.lang]);\n    options.icons = $.extend(true, {}, $.summernote.options.icons, options.icons);\n    options.tooltip = options.tooltip === 'auto' ? !env.isSupportTouch : options.tooltip;\n\n    this.each((idx, note) => {\n      const $note = $(note);\n      if (!$note.data('summernote')) {\n        const context = new Context($note, options);\n        $note.data('summernote', context);\n        $note.data('summernote').triggerEvent('init', context.layoutInfo);\n      }\n    });\n\n    const $note = this.first();\n    if ($note.length) {\n      const context = $note.data('summernote');\n      if (isExternalAPICalled) {\n        return context.invoke.apply(context, lists.from(arguments));\n      } else if (options.focus) {\n        context.invoke('editor.focus');\n      }\n    }\n\n    return this;\n  }\n});\n", "import $ from 'jquery';\nimport ui from './ui';\nimport '../base/summernote-en-US';\nimport Editor from '../base/module/Editor';\nimport Clipboard from '../base/module/Clipboard';\nimport Dropzone from '../base/module/Dropzone';\nimport Codeview from '../base/module/Codeview';\nimport Statusbar from '../base/module/Statusbar';\nimport Fullscreen from '../base/module/Fullscreen';\nimport Handle from '../base/module/Handle';\nimport AutoLink from '../base/module/AutoLink';\nimport AutoSync from '../base/module/AutoSync';\nimport Placeholder from '../base/module/Placeholder';\nimport Buttons from '../base/module/Buttons';\nimport Toolbar from '../base/module/Toolbar';\nimport LinkDialog from '../base/module/LinkDialog';\nimport LinkPopover from '../base/module/LinkPopover';\nimport ImageDialog from '../base/module/ImageDialog';\nimport ImagePopover from '../base/module/ImagePopover';\nimport TablePopover from '../base/module/TablePopover';\nimport VideoDialog from '../base/module/VideoDialog';\nimport HelpDialog from '../base/module/HelpDialog';\nimport AirPopover from '../base/module/AirPopover';\nimport HintPopover from '../base/module/HintPopover';\n\n$.summernote = $.extend($.summernote, {\n  version: '@@VERSION@@',\n  ui: ui,\n\n  plugins: {},\n\n  options: {\n    modules: {\n      'editor': Editor,\n      'clipboard': Clipboard,\n      'dropzone': Dropzone,\n      'codeview': Codeview,\n      'statusbar': Statusbar,\n      'fullscreen': Fullscreen,\n      'handle': Handle,\n      // FIXME: HintPopover must be front of autolink\n      //  - Script error about range when Enter key is pressed on hint popover\n      'hintPopover': HintPopover,\n      'autoLink': AutoLink,\n      'autoSync': AutoSync,\n      'placeholder': Placeholder,\n      'buttons': Buttons,\n      'toolbar': Toolbar,\n      'linkDialog': LinkDialog,\n      'linkPopover': LinkPopover,\n      'imageDialog': ImageDialog,\n      'imagePopover': ImagePopover,\n      'tablePopover': TablePopover,\n      'videoDialog': VideoDialog,\n      'helpDialog': HelpDialog,\n      'airPopover': AirPopover\n    },\n\n    buttons: {},\n\n    lang: 'en-US',\n\n    followingToolbar: true,\n    otherStaticBar: '',\n\n    // toolbar\n    toolbar: [\n      ['style', ['style']],\n      ['font', ['bold', 'underline', 'clear']],\n      ['fontname', ['fontname']],\n      ['fontsize', ['fontsize']],\n      ['color', ['color']],\n      ['para', ['ul', 'ol', 'paragraph']],\n      ['table', ['table']],\n      ['insert', ['link', 'picture', 'video']],\n      ['view', ['fullscreen', 'codeview', 'help']]\n    ],\n\n    // popover\n    popatmouse: true,\n    popover: {\n      image: [\n        ['imagesize', ['imageSize100', 'imageSize50', 'imageSize25']],\n        ['float', ['floatLeft', 'floatRight', 'floatNone']],\n        ['remove', ['removeMedia']]\n      ],\n      link: [\n        ['link', ['linkDialogShow', 'unlink']]\n      ],\n      table: [\n        ['add', ['addRowDown', 'addRowUp', 'addColLeft', 'addColRight']],\n        ['delete', ['deleteRow', 'deleteCol', 'deleteTable']]\n      ],\n      air: [\n        ['color', ['color']],\n        ['font', ['bold', 'underline', 'clear']],\n        ['para', ['ul', 'paragraph']],\n        ['table', ['table']],\n        ['insert', ['link', 'picture']]\n      ]\n    },\n\n    // air mode: inline editor\n    airMode: false,\n\n    width: null,\n    height: null,\n    linkTargetBlank: true,\n\n    focus: false,\n    tabSize: 4,\n    styleWithSpan: true,\n    shortcuts: true,\n    textareaAutoSync: true,\n    hintDirection: 'bottom',\n    tooltip: 'auto',\n    container: 'body',\n    maxTextLength: 0,\n    blockquoteBreakingLevel: 2,\n\n    styleTags: ['p', 'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],\n\n    fontNames: [\n      'Arial', 'Arial Black', 'Comic Sans MS', 'Courier New',\n      'Helvetica Neue', 'Helvetica', 'Impact', 'Lucida Grande',\n      'Tahoma', 'Times New Roman', 'Verdana'\n    ],\n\n    fontSizes: ['8', '9', '10', '11', '12', '14', '18', '24', '36'],\n\n    // pallete colors(n x n)\n    colors: [\n      ['#000000', '#424242', '#636363', '#9C9C94', '#CEC6CE', '#EFEFEF', '#F7F7F7', '#FFFFFF'],\n      ['#FF0000', '#FF9C00', '#FFFF00', '#00FF00', '#00FFFF', '#0000FF', '#9C00FF', '#FF00FF'],\n      ['#F7C6CE', '#FFE7CE', '#FFEFC6', '#D6EFD6', '#CEDEE7', '#CEE7F7', '#D6D6E7', '#E7D6DE'],\n      ['#E79C9C', '#FFC69C', '#FFE79C', '#B5D6A5', '#A5C6CE', '#9CC6EF', '#B5A5D6', '#D6A5BD'],\n      ['#E76363', '#F7AD6B', '#FFD663', '#94BD7B', '#73A5AD', '#6BADDE', '#8C7BC6', '#C67BA5'],\n      ['#CE0000', '#E79439', '#EFC631', '#6BA54A', '#4A7B8C', '#3984C6', '#634AA5', '#A54A7B'],\n      ['#9C0000', '#B56308', '#BD9400', '#397B21', '#104A5A', '#085294', '#311873', '#731842'],\n      ['#630000', '#7B3900', '#846300', '#295218', '#083139', '#003163', '#21104A', '#4A1031']\n    ],\n\n    // http://chir.ag/projects/name-that-color/\n    colorsName: [\n      ['Black', 'Tundora', 'Dove Gray', 'Star Dust', 'Pale Slate', 'Gallery', 'Alabaster', 'White'],\n      ['Red', 'Orange Peel', 'Yellow', 'Green', 'Cyan', 'Blue', 'Electric Violet', 'Magenta'],\n      ['Azalea', 'Karry', 'Egg White', 'Zanah', 'Botticelli', 'Tropical Blue', 'Mischka', 'Twilight'],\n      ['Tonys Pink', 'Peach Orange', 'Cream Brulee', 'Sprout', 'Casper', 'Perano', 'Cold Purple', 'Careys Pink'],\n      ['Mandy', 'Rajah', 'Dandelion', 'Olivine', 'Gulf Stream', 'Viking', 'Blue Marguerite', 'Puce'],\n      ['Guardsman Red', 'Fire Bush', 'Golden Dream', 'Chelsea Cucumber', 'Smalt Blue', 'Boston Blue', 'Butterfly Bush', 'Cadillac'],\n      ['Sangria', 'Mai Tai', 'Buddha Gold', 'Forest Green', 'Eden', 'Venice Blue', 'Meteorite', 'Claret'],\n      ['Rosewood', 'Cinnamon', 'Olive', 'Parsley', 'Tiber', 'Midnight Blue', 'Valentino', 'Loulou']\n    ],\n\n    lineHeights: ['1.0', '1.2', '1.4', '1.5', '1.6', '1.8', '2.0', '3.0'],\n\n    tableClassName: 'table table-bordered',\n\n    insertTableMaxSize: {\n      col: 10,\n      row: 10\n    },\n\n    dialogsInBody: false,\n    dialogsFade: false,\n\n    maximumImageFileSize: null,\n\n    callbacks: {\n      onInit: null,\n      onFocus: null,\n      onBlur: null,\n      onBlurCodeview: null,\n      onEnter: null,\n      onKeyup: null,\n      onKeydown: null,\n      onImageUpload: null,\n      onImageUploadError: null,\n      onImageLinkInsert: null\n    },\n\n    codemirror: {\n      mode: 'text/html',\n      htmlMode: true,\n      lineNumbers: true\n    },\n\n    keyMap: {\n      pc: {\n        'ENTER': 'insertParagraph',\n        'CTRL+Z': 'undo',\n        'CTRL+Y': 'redo',\n        'TAB': 'tab',\n        'SHIFT+TAB': 'untab',\n        'CTRL+B': 'bold',\n        'CTRL+I': 'italic',\n        'CTRL+U': 'underline',\n        'CTRL+SHIFT+S': 'strikethrough',\n        'CTRL+BACKSLASH': 'removeFormat',\n        'CTRL+SHIFT+L': 'justifyLeft',\n        'CTRL+SHIFT+E': 'justifyCenter',\n        'CTRL+SHIFT+R': 'justifyRight',\n        'CTRL+SHIFT+J': 'justifyFull',\n        'CTRL+SHIFT+NUM7': 'insertUnorderedList',\n        'CTRL+SHIFT+NUM8': 'insertOrderedList',\n        'CTRL+LEFTBRACKET': 'outdent',\n        'CTRL+RIGHTBRACKET': 'indent',\n        'CTRL+NUM0': 'formatPara',\n        'CTRL+NUM1': 'formatH1',\n        'CTRL+NUM2': 'formatH2',\n        'CTRL+NUM3': 'formatH3',\n        'CTRL+NUM4': 'formatH4',\n        'CTRL+NUM5': 'formatH5',\n        'CTRL+NUM6': 'formatH6',\n        'CTRL+ENTER': 'insertHorizontalRule',\n        'CTRL+K': 'linkDialog.show'\n      },\n\n      mac: {\n        'ENTER': 'insertParagraph',\n        'CMD+Z': 'undo',\n        'CMD+SHIFT+Z': 'redo',\n        'TAB': 'tab',\n        'SHIFT+TAB': 'untab',\n        'CMD+B': 'bold',\n        'CMD+I': 'italic',\n        'CMD+U': 'underline',\n        'CMD+SHIFT+S': 'strikethrough',\n        'CMD+BACKSLASH': 'removeFormat',\n        'CMD+SHIFT+L': 'justifyLeft',\n        'CMD+SHIFT+E': 'justifyCenter',\n        'CMD+SHIFT+R': 'justifyRight',\n        'CMD+SHIFT+J': 'justifyFull',\n        'CMD+SHIFT+NUM7': 'insertUnorderedList',\n        'CMD+SHIFT+NUM8': 'insertOrderedList',\n        'CMD+LEFTBRACKET': 'outdent',\n        'CMD+RIGHTBRACKET': 'indent',\n        'CMD+NUM0': 'formatPara',\n        'CMD+NUM1': 'formatH1',\n        'CMD+NUM2': 'formatH2',\n        'CMD+NUM3': 'formatH3',\n        'CMD+NUM4': 'formatH4',\n        'CMD+NUM5': 'formatH5',\n        'CMD+NUM6': 'formatH6',\n        'CMD+ENTER': 'insertHorizontalRule',\n        'CMD+K': 'linkDialog.show'\n      }\n    },\n    icons: {\n      'align': 'note-icon-align',\n      'alignCenter': 'note-icon-align-center',\n      'alignJustify': 'note-icon-align-justify',\n      'alignLeft': 'note-icon-align-left',\n      'alignRight': 'note-icon-align-right',\n      'rowBelow': 'note-icon-row-below',\n      'colBefore': 'note-icon-col-before',\n      'colAfter': 'note-icon-col-after',\n      'rowAbove': 'note-icon-row-above',\n      'rowRemove': 'note-icon-row-remove',\n      'colRemove': 'note-icon-col-remove',\n      'indent': 'note-icon-align-indent',\n      'outdent': 'note-icon-align-outdent',\n      'arrowsAlt': 'note-icon-arrows-alt',\n      'bold': 'note-icon-bold',\n      'caret': 'note-icon-caret',\n      'circle': 'note-icon-circle',\n      'close': 'note-icon-close',\n      'code': 'note-icon-code',\n      'eraser': 'note-icon-eraser',\n      'font': 'note-icon-font',\n      'frame': 'note-icon-frame',\n      'italic': 'note-icon-italic',\n      'link': 'note-icon-link',\n      'unlink': 'note-icon-chain-broken',\n      'magic': 'note-icon-magic',\n      'menuCheck': 'note-icon-menu-check',\n      'minus': 'note-icon-minus',\n      'orderedlist': 'note-icon-orderedlist',\n      'pencil': 'note-icon-pencil',\n      'picture': 'note-icon-picture',\n      'question': 'note-icon-question',\n      'redo': 'note-icon-redo',\n      'square': 'note-icon-square',\n      'strikethrough': 'note-icon-strikethrough',\n      'subscript': 'note-icon-subscript',\n      'superscript': 'note-icon-superscript',\n      'table': 'note-icon-table',\n      'textHeight': 'note-icon-text-height',\n      'trash': 'note-icon-trash',\n      'underline': 'note-icon-underline',\n      'undo': 'note-icon-undo',\n      'unorderedlist': 'note-icon-unorderedlist',\n      'video': 'note-icon-video'\n    }\n  }\n});\n\nimport '../summernote'; // eslint-disable-line\n"], "names": ["$", "isEmpty", "Codeview"], "mappings": ";;;;;;;;;;;;;;;;;EAEA;MACE,kBAAY,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ;UAC7C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;UACrB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;UACzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;OAC1B;MAED,yBAAM,GAAN,UAAO,OAAO;UACZ,IAAM,KAAK,GAAGA,GAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;UAE7B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;cACzC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;WACnC;UAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;cAC1C,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;WACxC;UAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;cACrCA,GAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,UAAC,CAAC,EAAE,CAAC;kBAC7B,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;eAC5B,CAAC,CAAC;WACJ;UAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;cACtC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;WACvC;UAED,IAAI,IAAI,CAAC,QAAQ,EAAE;cACjB,IAAM,YAAU,GAAG,KAAK,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;cAC1D,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAC,KAAK;kBAC1B,KAAK,CAAC,MAAM,CAAC,YAAU,CAAC,MAAM,GAAG,YAAU,GAAG,KAAK,CAAC,CAAC;eACtD,CAAC,CAAC;WACJ;UAED,IAAI,IAAI,CAAC,QAAQ,EAAE;cACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;WACpC;UAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;cACzC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;WAC9B;UAED,IAAI,OAAO,EAAE;cACX,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;WACvB;UAED,OAAO,KAAK,CAAC;OACd;MACH,eAAC;EAAD,CAAC,IAAA;AAED,iBAAe;MACb,MAAM,EAAE,UAAC,MAAM,EAAE,QAAQ;UACvB,OAAO;cACL,IAAM,OAAO,GAAG,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;cAC/E,IAAI,QAAQ,GAAGA,GAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;cAC3D,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE;kBAC/B,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;eAC7B;cACD,OAAO,IAAI,QAAQ,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;WAC1D,CAAC;OACH;GACF,CAAC;;ECjEF;MACE,mBAAY,KAAK,EAAE,OAAO;UACxB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;UACnB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE;cAC1B,KAAK,EAAE,EAAE;cACT,MAAM,EAAE,OAAO,CAAC,SAAS;cACzB,OAAO,EAAE,aAAa;cACtB,SAAS,EAAE,QAAQ;WACpB,EAAE,OAAO,CAAC,CAAC;;UAGZ,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;cAChB,+BAA+B;cAC/B,qCAAqC;cACrC,uCAAuC;cACvC,QAAQ;WACT,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;;UAGZ,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;cACrC,IAAM,cAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;cAC1C,IAAM,cAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;cAC1C,IAAM,gBAAc,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;cAE9C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAS,SAAS;kBACxD,IAAI,SAAS,KAAK,OAAO,EAAE;sBACzB,KAAK,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;sBACnC,KAAK,CAAC,EAAE,CAAC,YAAY,EAAE,cAAY,CAAC,CAAC,EAAE,CAAC,YAAY,EAAE,cAAY,CAAC,CAAC;mBACrE;uBAAM,IAAI,SAAS,KAAK,OAAO,EAAE;sBAChC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,gBAAc,CAAC,CAAC;mBACnC;uBAAM,IAAI,SAAS,KAAK,OAAO,EAAE;sBAChC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,cAAY,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,cAAY,CAAC,CAAC;mBAC1D;eACF,CAAC,CAAC;WACJ;OACF;MAED,wBAAI,GAAJ;UACE,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;UACzB,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;UAE9B,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;UAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;UAC/E,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;UAEpE,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;UAC7B,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;UACxB,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;UACnD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;UAEvC,IAAM,SAAS,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;UACrC,IAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;UACvC,IAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;UAC3C,IAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;UAE7C,IAAI,SAAS,KAAK,QAAQ,EAAE;cAC1B,QAAQ,CAAC,GAAG,CAAC;kBACX,GAAG,EAAE,MAAM,CAAC,GAAG,GAAG,UAAU;kBAC5B,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,SAAS,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,CAAC;eACvD,CAAC,CAAC;WACJ;eAAM,IAAI,SAAS,KAAK,KAAK,EAAE;cAC9B,QAAQ,CAAC,GAAG,CAAC;kBACX,GAAG,EAAE,MAAM,CAAC,GAAG,GAAG,aAAa;kBAC/B,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,SAAS,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,CAAC;eACvD,CAAC,CAAC;WACJ;eAAM,IAAI,SAAS,KAAK,MAAM,EAAE;cAC/B,QAAQ,CAAC,GAAG,CAAC;kBACX,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,CAAC;kBACtD,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,YAAY;eACjC,CAAC,CAAC;WACJ;eAAM,IAAI,SAAS,KAAK,OAAO,EAAE;cAChC,QAAQ,CAAC,GAAG,CAAC;kBACX,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,UAAU,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC,CAAC;kBACtD,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,SAAS;eAC9B,CAAC,CAAC;WACJ;OACF;MAED,wBAAI,GAAJ;UACE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;UAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;OACxB;MAED,0BAAM,GAAN;UACE,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;cAChC,IAAI,CAAC,IAAI,EAAE,CAAC;WACb;eAAM;cACL,IAAI,CAAC,IAAI,EAAE,CAAC;WACb;OACF;MACH,gBAAC;EAAD,CAAC,IAAA;;EC1FD;MACE,oBAAY,KAAK,EAAE,OAAO;UACxB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;UACrB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE;cAC1B,MAAM,EAAE,OAAO,CAAC,SAAS;WAC1B,EAAE,OAAO,CAAC,CAAC;UACZ,IAAI,CAAC,QAAQ,EAAE,CAAC;OACjB;MAED,6BAAQ,GAAR;UAAA,iBAKC;UAJC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,CAAC;cACzB,KAAI,CAAC,MAAM,EAAE,CAAC;cACd,CAAC,CAAC,wBAAwB,EAAE,CAAC;WAC9B,CAAC,CAAC;OACJ;MAED,0BAAK,GAAL;UACE,IAAI,OAAO,GAAG,CAAC,CAAC,sBAAsB,CAAC,CAAC;UACxC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;UACvD,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;OAC7B;MAED,yBAAI,GAAJ;UACE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;UAChC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;UAEvC,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;UACpC,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;UAChC,IAAI,KAAK,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;UACnC,IAAI,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,CAAC;UACpC,IAAI,iBAAiB,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;UAE/E,IAAI,MAAM,CAAC,IAAI,GAAG,KAAK,GAAG,WAAW,GAAG,iBAAiB,EAAE;cACzD,SAAS,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,GAAG,iBAAiB,IAAI,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;WACvF;eAAM;cACL,SAAS,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;WAClC;OACF;MAED,yBAAI,GAAJ;UACE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;UACnC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;OAC3C;MAED,2BAAM,GAAN;UACE,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;UAEtD,IAAI,CAAC,KAAK,EAAE,CAAC;UAEb,IAAI,QAAQ,EAAE;cACZ,IAAI,CAAC,IAAI,EAAE,CAAC;WACb;eAAM;cACL,IAAI,CAAC,IAAI,EAAE,CAAC;WACb;OACF;MACH,iBAAC;EAAD,CAAC,IAAA;EAED,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,UAAS,CAAC;MAChC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,MAAM,EAAE;UAClD,CAAC,CAAC,sBAAsB,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;OAC/C;EACH,CAAC,CAAC,CAAC;EAEH,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,0BAA0B,EAAE,UAAS,CAAC;MACnD,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,MAAM,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;EAC1E,CAAC,CAAC,CAAC;;ECjEH;MACE,iBAAY,KAAK,EAAE,OAAO;UACxB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE;cAC1B,MAAM,EAAE,OAAO,CAAC,SAAS,IAAI,MAAM;WACpC,EAAE,OAAO,CAAC,CAAC;UAEZ,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;UACpB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,qCAAqC,CAAC,CAAC;OAC3D;MAED,sBAAI,GAAJ;UACE,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE;cAClC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;cACxC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;WACtC;eAAM;cACL,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;cAC3C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;WACzC;UAED,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;UACpD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;UAElE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;UACvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;OAChF;MAED,sBAAI,GAAJ;UACE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;UACvC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;UACtB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;OACxC;MACH,cAAC;EAAD,CAAC,IAAA;;EC1BD,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,uCAAuC,CAAC,CAAC;EACxE,IAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,4CAA4C,CAAC,CAAC;EAC9E,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,kCAAkC,CAAC,CAAC;EACxE,IAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,uEAAuE,CAAC,CAAC;EACzG,IAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,0FAA0F,CAAC,CAAC;EAC7H,IAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC;MAChC,uEAAuE;MACvE,4CAA4C;MAC5C,mGAAmG;MACnG,kCAAkC;MAClC,kCAAkC;MAClC,kCAAkC;MAClC,UAAU;MACV,QAAQ;GACT,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;EAEZ,IAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;EAChE,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC;MAClC,0FAA0F;MAC1F,uEAAuE;GACxE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;EAEZ,IAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,8BAA8B,CAAC,CAAC;EACpE,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,qEAAqE,EAAE,UAAS,KAAK,EAAE,OAAO;;MAE3H,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;UAC9B,KAAK,CAAC,IAAI,CAAC;cACT,YAAY,EAAE,OAAO,CAAC,OAAO;WAC9B,CAAC,CAAC;UACH,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE;cAC/C,KAAK,EAAE,OAAO,CAAC,OAAO;cACtB,SAAS,EAAE,OAAO,CAAC,SAAS;WAC7B,CAAC,CAAC,CAAC;OACL;MACD,IAAI,OAAO,CAAC,QAAQ,EAAE;UACpB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;OAC9B;MAED,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE;UACjE,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,UAAU,CAAC,KAAK,EAAE;cACjD,SAAS,EAAE,OAAO,CAAC,SAAS;WAC7B,CAAC,CAAC,CAAC;OACL;EACH,CAAC,CAAC,CAAC;EAEH,IAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,8CAA8C,EAAE,UAAS,KAAK,EAAE,OAAO;MACtG,IAAM,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,UAAS,IAAI;UACvE,IAAM,KAAK,GAAG,CAAC,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;UACrE,IAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;UACjE,IAAM,KAAK,GAAG,CAAC,CAAC,qDAAqD,GAAG,KAAK,GAAG,gCAAgC,GAAG,IAAI,GAAG,QAAQ,CAAC,CAAC;UAEpI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;UAEvC,OAAO,KAAK,CAAC;OACd,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC;MAEnB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;MAEzD,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,uBAAuB,EAAE,UAAS,CAAC;UACnD,IAAM,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;UAEnB,IAAM,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;UAC7B,IAAM,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;UAE/B,IAAI,IAAI,CAAC,KAAK,EAAE;cACd,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;WAChB;eAAM,IAAI,OAAO,CAAC,SAAS,EAAE;cAC5B,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;WACnC;OACF,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;EAEH,IAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,yDAAyD,EAAE,UAAS,KAAK,EAAE,OAAO;MACtH,IAAM,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,UAAS,IAAI;UACvE,IAAM,KAAK,GAAG,CAAC,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;UACrE,IAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;UAEjE,IAAM,KAAK,GAAG,CAAC,CAAC,qDAAqD,GAAG,KAAK,GAAG,gCAAgC,GAAG,IAAI,GAAG,QAAQ,CAAC,CAAC;UACpI,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;UAC5E,OAAO,KAAK,CAAC;OACd,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC;MAEnB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;MAEzD,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,uBAAuB,EAAE,UAAS,CAAC;UACnD,IAAM,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;UAEnB,IAAM,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;UAC7B,IAAM,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;UAE/B,IAAI,IAAI,CAAC,KAAK,EAAE;cACd,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;WAChB;eAAM,IAAI,OAAO,CAAC,SAAS,EAAE;cAC5B,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;WACnC;OACF,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;EAEH,IAAM,sBAAsB,GAAG,UAAS,QAAQ,EAAE,OAAO;MACvD,OAAO,QAAQ,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;EAC5D,CAAC,CAAC;EAEF,IAAM,cAAc,GAAG,UAAS,GAAG,EAAE,QAAQ;MAC3C,OAAO,WAAW,CAAC;UACjB,MAAM,CAAC;cACL,SAAS,EAAE,iBAAiB;cAC5B,QAAQ,EAAE,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC;cACnD,OAAO,EAAE,GAAG,CAAC,OAAO;cACpB,IAAI,EAAE;kBACJ,MAAM,EAAE,UAAU;eACnB;WACF,CAAC;UACF,QAAQ,CAAC;cACP,SAAS,EAAE,GAAG,CAAC,SAAS;cACxB,KAAK,EAAE,GAAG,CAAC,KAAK;cAChB,QAAQ,EAAE,GAAG,CAAC,QAAQ;cACtB,SAAS,EAAE,GAAG,CAAC,SAAS;WACzB,CAAC;OACH,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;EACtC,CAAC,CAAC;EAEF,IAAM,mBAAmB,GAAG,UAAS,GAAG,EAAE,QAAQ;MAChD,OAAO,WAAW,CAAC;UACjB,MAAM,CAAC;cACL,SAAS,EAAE,iBAAiB;cAC5B,QAAQ,EAAE,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC;cACnD,OAAO,EAAE,GAAG,CAAC,OAAO;cACpB,IAAI,EAAE;kBACJ,MAAM,EAAE,UAAU;eACnB;WACF,CAAC;UACF,aAAa,CAAC;cACZ,SAAS,EAAE,GAAG,CAAC,SAAS;cACxB,cAAc,EAAE,GAAG,CAAC,cAAc;cAClC,KAAK,EAAE,GAAG,CAAC,KAAK;cAChB,QAAQ,EAAE,GAAG,CAAC,QAAQ;cACtB,SAAS,EAAE,GAAG,CAAC,SAAS;WACzB,CAAC;OACH,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;EACtC,CAAC,CAAC;EAEF,IAAM,uBAAuB,GAAG,UAAS,GAAG;MAC1C,OAAO,WAAW,CAAC;UACjB,MAAM,CAAC;cACL,SAAS,EAAE,iBAAiB;cAC5B,QAAQ,EAAE,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC;cACnD,OAAO,EAAE,GAAG,CAAC,OAAO;cACpB,IAAI,EAAE;kBACJ,MAAM,EAAE,UAAU;eACnB;WACF,CAAC;UACF,QAAQ,CAAC;cACP,WAAW,CAAC;kBACV,SAAS,EAAE,YAAY;kBACvB,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;eACvB,CAAC;cACF,WAAW,CAAC;kBACV,SAAS,EAAE,WAAW;kBACtB,QAAQ,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;eACvB,CAAC;WACH,CAAC;OACH,CAAC,CAAC,MAAM,EAAE,CAAC;EACd,CAAC,CAAC;EAEF,IAAM,gBAAgB,GAAG,UAAS,KAAK,EAAE,GAAG,EAAE,GAAG;MAC/C,IAAM,SAAS,GAAG,EAAE,CAAC;MACrB,IAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;MAC3C,IAAM,iBAAiB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;MACzC,IAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;MACrE,IAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;MACxE,IAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;MAE5E,IAAI,SAAS,CAAC;;MAEd,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE;UAC/B,IAAM,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;UAC5C,SAAS,GAAG;cACV,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI;cAChC,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG;WAChC,CAAC;OACH;WAAM;UACL,SAAS,GAAG;cACV,CAAC,EAAE,KAAK,CAAC,OAAO;cAChB,CAAC,EAAE,KAAK,CAAC,OAAO;WACjB,CAAC;OACH;MAED,IAAM,GAAG,GAAG;UACV,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;UAC1C,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;OAC3C,CAAC;MAEF,YAAY,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;MAChE,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;MAE5C,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE;UAC5B,cAAc,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;OACjD;MAED,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,EAAE;UAC5B,cAAc,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;OAClD;MAED,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD,CAAC,CAAC;EAEF,IAAM,mBAAmB,GAAG,UAAS,GAAG;MACtC,OAAO,WAAW,CAAC;UACjB,MAAM,CAAC;cACL,SAAS,EAAE,iBAAiB;cAC5B,QAAQ,EAAE,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC;cACnD,OAAO,EAAE,GAAG,CAAC,OAAO;cACpB,IAAI,EAAE;kBACJ,MAAM,EAAE,UAAU;eACnB;WACF,CAAC;UACF,QAAQ,CAAC;cACP,SAAS,EAAE,YAAY;cACvB,KAAK,EAAE;kBACL,qCAAqC;kBACrC,+FAA+F;kBAC/F,oDAAoD;kBACpD,sDAAsD;kBACtD,QAAQ;kBACR,iDAAiD;eAClD,CAAC,IAAI,CAAC,EAAE,CAAC;WACX,CAAC;OACH,EAAE;UACD,QAAQ,EAAE,UAAS,KAAK;cACtB,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;cACnE,QAAQ,CAAC,GAAG,CAAC;kBACX,KAAK,EAAE,GAAG,CAAC,GAAG,GAAG,IAAI;kBACrB,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,IAAI;eACvB,CAAC;mBACC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC;mBACxB,SAAS,CAAC,UAAS,CAAC;kBACnB,gBAAgB,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;eACvC,CAAC,CAAC;WACN;OACF,CAAC,CAAC,MAAM,EAAE,CAAC;EACd,CAAC,CAAC;EAEF,IAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,mCAAmC,EAAE,UAAS,KAAK,EAAE,OAAO;MAC1F,IAAM,QAAQ,GAAG,EAAE,CAAC;MACpB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,EAAE;UACvE,IAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;UACpC,IAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;UACnC,IAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;UAC3C,IAAM,OAAO,GAAG,EAAE,CAAC;UACnB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,EAAE;cAC/D,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;cAC1B,IAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;cAClC,OAAO,CAAC,IAAI,CAAC;kBACX,uDAAuD;kBACvD,0BAA0B,EAAE,KAAK,EAAE,IAAI;kBACvC,cAAc,EAAE,SAAS,EAAE,IAAI;kBAC/B,cAAc,EAAE,KAAK,EAAE,IAAI;kBAC3B,SAAS,EAAE,SAAS,EAAE,IAAI;kBAC1B,cAAc,EAAE,SAAS,EAAE,IAAI;kBAC/B,8CAA8C;eAC/C,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;WACb;UACD,QAAQ,CAAC,IAAI,CAAC,8BAA8B,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC;OAC7E;MACD,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;MAE9B,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC;UACjC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;cACnD,SAAS,EAAE,OAAO,CAAC,SAAS;WAC7B,CAAC,CAAC,CAAC;OACL,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;EAEH,IAAM,mBAAmB,GAAG,UAAS,GAAG,EAAE,IAAI;MAC5C,OAAO,WAAW,CAAC;UACjB,SAAS,EAAE,YAAY;UACvB,QAAQ,EAAE;cACR,MAAM,CAAC;kBACL,SAAS,EAAE,2BAA2B;kBACtC,QAAQ,EAAE,GAAG,CAAC,KAAK;kBACnB,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;kBAC9B,KAAK,EAAE,GAAG,CAAC,YAAY;kBACvB,QAAQ,EAAE,UAAS,OAAO;sBACxB,IAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;sBAExD,IAAI,IAAI,KAAK,WAAW,EAAE;0BACxB,YAAY,CAAC,GAAG,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;0BAChD,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;uBAC3C;mBACF;eACF,CAAC;cACF,MAAM,CAAC;kBACL,SAAS,EAAE,iBAAiB;kBAC5B,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC;kBACjC,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;kBAC5B,IAAI,EAAE;sBACJ,MAAM,EAAE,UAAU;mBACnB;eACF,CAAC;cACF,QAAQ,CAAC;kBACP,KAAK,EAAE;sBACL,OAAO;sBACP,mDAAmD;sBACnD,oCAAoC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ;sBAC3E,SAAS;sBACT,yEAAyE;0BACzE,+CAA+C;sBAC/C,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;sBAC1B,eAAe;sBACf,UAAU;sBACV,qDAAqD;sBACrD,wBAAwB;sBACxB,yHAAyH;sBACzH,yGAAyG;sBACzG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;sBACvB,eAAe;sBACf,UAAU;sBACV,QAAQ;sBACR,mDAAmD;sBACnD,oCAAoC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ;sBAC3E,SAAS;sBACT,yEAAyE;0BACzE,oDAAoD;sBACpD,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc;sBAC7B,eAAe;sBACf,UAAU;sBACV,qDAAqD;sBACrD,wBAAwB;sBACxB,yHAAyH;sBACzH,yGAAyG;sBACzG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;sBACvB,eAAe;sBACf,UAAU;sBACV,QAAQ;sBACR,QAAQ;mBACT,CAAC,IAAI,CAAC,EAAE,CAAC;kBACV,QAAQ,EAAE,UAAS,SAAS;sBAC1B,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC;0BAClC,IAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;0BACxB,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;8BACrB,MAAM,EAAE,GAAG,CAAC,MAAM;8BAClB,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;2BACjC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;uBACd,CAAC,CAAC;sBAEH,IAAI,IAAI,KAAK,MAAM,EAAE;0BACnB,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,EAAE,CAAC;0BAC/C,SAAS,CAAC,GAAG,CAAC,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC,CAAC;uBACzC;2BAAM,IAAI,IAAI,KAAK,MAAM,EAAE;0BAC1B,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,IAAI,EAAE,CAAC;0BAC/C,SAAS,CAAC,GAAG,CAAC,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC,CAAC;uBACzC;mBACF;kBACD,KAAK,EAAE,UAAS,KAAK;sBACnB,IAAM,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;sBAChC,IAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;sBACxC,IAAI,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;sBAClC,IAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;sBAC5D,IAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;sBAC5D,IAAI,KAAK,KAAK,IAAI,EAAE;0BAClB,KAAK,CAAC,eAAe,EAAE,CAAC;uBACzB;2BAAM,IAAI,KAAK,KAAK,aAAa,EAAE;0BAClC,KAAK,GAAG,SAAS,CAAC;uBACnB;2BAAM,IAAI,KAAK,KAAK,aAAa,EAAE;0BAClC,KAAK,GAAG,SAAS,CAAC;uBACnB;sBAED,IAAI,SAAS,IAAI,KAAK,EAAE;0BACtB,IAAM,GAAG,GAAG,SAAS,KAAK,WAAW,GAAG,kBAAkB,GAAG,OAAO,CAAC;0BACrE,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;0BACzE,IAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;0BAEzF,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;0BACvB,cAAc,CAAC,IAAI,CAAC,OAAO,GAAG,SAAS,EAAE,KAAK,CAAC,CAAC;0BAEhD,IAAI,IAAI,KAAK,MAAM,EAAE;8BACnB,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;2BACnC;+BAAM,IAAI,IAAI,KAAK,MAAM,EAAE;8BAC1B,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;2BACnC;+BAAM;8BACL,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;2BACjC;uBACF;mBACF;eACF,CAAC;WACH;OACF,CAAC,CAAC,MAAM,EAAE,CAAC;EACd,CAAC,CAAC;EAEF,IAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,2EAA2E,EAAE,UAAS,KAAK,EAAE,OAAO;MACjI,IAAI,OAAO,CAAC,IAAI,EAAE;UAChB,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;OACxB;MACD,KAAK,CAAC,IAAI,CAAC;UACT,YAAY,EAAE,OAAO,CAAC,KAAK;OAC5B,CAAC,CAAC;MACH,KAAK,CAAC,IAAI,CAAC;UACT,oCAAoC;WACnC,OAAO,CAAC,KAAK;gBACV,qCAAqC;kBACzC,0HAA0H;kBAC1H,qCAAqC,GAAG,OAAO,CAAC,KAAK,GAAG,OAAO;kBAC/D,YAAY,GAAG,EAAE;UAEjB,mCAAmC,GAAG,OAAO,CAAC,IAAI,GAAG,QAAQ;WAC5D,OAAO,CAAC,MAAM;gBACX,qCAAqC,GAAG,OAAO,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE;UAE1E,UAAU;OACX,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;MAEZ,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;EACnD,CAAC,CAAC,CAAC;EAEH,IAAM,WAAW,GAAG,UAAS,GAAG;MAC9B,IAAM,IAAI,GAAG,+BAA+B;UAC1C,iCAAiC;UACjC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,6BAA6B;UAClD,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,UAAU;UACrC,UAAU;UACV,yDAAyD;UACzD,QAAQ,CAAC;MACX,IAAM,MAAM,GAAG;UACb,oGAAoG;UACpG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;UACrB,WAAW;OACZ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MAEX,OAAO,MAAM,CAAC;UACZ,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;UAC5B,IAAI,EAAE,GAAG,CAAC,IAAI;UACd,IAAI,EAAE,IAAI;UACV,MAAM,EAAE,MAAM;OACf,CAAC,CAAC,MAAM,EAAE,CAAC;EACd,CAAC,CAAC;EAEF,IAAM,WAAW,GAAG,UAAS,GAAG;MAC9B,IAAM,IAAI,GAAG,4DAA4D;UACvE,iCAAiC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,UAAU;UAC/E,kHAAkH;UAClH,GAAG,CAAC,eAAe;UACnB,QAAQ;UACR,sDAAsD;UACtD,iCAAiC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,UAAU;UACnE,yDAAyD;UACzD,QAAQ,CAAC;MACX,IAAM,MAAM,GAAG;UACb,mHAAmH;UACnH,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;UACrB,WAAW;OACZ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MAEX,OAAO,MAAM,CAAC;UACZ,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;UAC5B,IAAI,EAAE,GAAG,CAAC,IAAI;UACd,IAAI,EAAE,IAAI;UACV,MAAM,EAAE,MAAM;OACf,CAAC,CAAC,MAAM,EAAE,CAAC;EACd,CAAC,CAAC;EAEF,IAAM,UAAU,GAAG,UAAS,GAAG;MAC7B,IAAM,IAAI,GAAG,+BAA+B;UAC1C,iCAAiC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,UAAU;UAC5E,yDAAyD;UACzD,QAAQ;UACR,+BAA+B;UAC/B,iCAAiC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,UAAU;UAClE,wEAAwE;UACxE,QAAQ;WACP,CAAC,GAAG,CAAC,iBAAiB;gBACnB,wBAAwB;kBAC1B,SAAS,GAAG,kCAAkC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,UAAU;kBAC3F,QAAQ,GAAG,EAAE,CACd,CAAC;MACJ,IAAM,MAAM,GAAG;UACb,mGAAmG;UACnG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;UACpB,WAAW;OACZ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MAEX,OAAO,MAAM,CAAC;UACZ,SAAS,EAAE,aAAa;UACxB,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;UAC3B,IAAI,EAAE,GAAG,CAAC,IAAI;UACd,IAAI,EAAE,IAAI;UACV,MAAM,EAAE,MAAM;OACf,CAAC,CAAC,MAAM,EAAE,CAAC;EACd,CAAC,CAAC;EAEF,IAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC;MAC9B,mCAAmC;MACnC,qCAAqC;MACrC,0DAA0D;MAC1D,QAAQ;GACT,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,UAAS,KAAK,EAAE,OAAO;MACjC,IAAM,SAAS,GAAG,OAAO,OAAO,CAAC,SAAS,KAAK,WAAW,GAAG,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC;MAE1F,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,CAAC;MAEjC,IAAI,OAAO,CAAC,SAAS,EAAE;UACrB,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,IAAI,EAAE,CAAC;OAC1C;EACH,CAAC,CAAC,CAAC;EAEH,IAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,8BAA8B,EAAE,UAAS,KAAK,EAAE,OAAO;MACtF,KAAK,CAAC,IAAI,CAAC;UACT,QAAQ,IAAI,OAAO,CAAC,EAAE,GAAG,QAAQ,GAAG,OAAO,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG;UAChE,yCAAyC,IAAI,OAAO,CAAC,EAAE,GAAG,OAAO,GAAG,OAAO,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;WACzF,OAAO,CAAC,OAAO,GAAG,UAAU,GAAG,EAAE;UAClC,iBAAiB,IAAI,OAAO,CAAC,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,KAAK;WAC/D,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,EAAE;UACjC,UAAU;OACX,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC;EAEH,IAAM,IAAI,GAAG,UAAS,aAAa,EAAE,OAAO;MAC1C,OAAO,GAAG,OAAO,IAAI,GAAG,CAAC;MACzB,OAAO,GAAG,GAAG,OAAO,GAAG,UAAU,GAAG,aAAa,GAAG,KAAK,CAAC;EAC5D,CAAC,CAAC;EAEF,IAAM,EAAE,GAAG;MACT,MAAM,EAAE,MAAM;MACd,OAAO,EAAE,OAAO;MAChB,WAAW,EAAE,WAAW;MACxB,OAAO,EAAE,OAAO;MAChB,QAAQ,EAAE,QAAQ;MAClB,SAAS,EAAE,SAAS;MACpB,SAAS,EAAE,SAAS;MACpB,WAAW,EAAE,WAAW;MACxB,WAAW,EAAE,WAAW;MACxB,MAAM,EAAE,MAAM;MACd,QAAQ,EAAE,QAAQ;MAClB,aAAa,EAAE,aAAa;MAC5B,cAAc,EAAE,cAAc;MAC9B,sBAAsB,EAAE,sBAAsB;MAC9C,mBAAmB,EAAE,mBAAmB;MACxC,uBAAuB,EAAE,uBAAuB;MAChD,mBAAmB,EAAE,mBAAmB;MACxC,mBAAmB,EAAE,mBAAmB;MACxC,OAAO,EAAE,OAAO;MAChB,MAAM,EAAE,MAAM;MACd,WAAW,EAAE,WAAW;MACxB,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE,UAAU;MACtB,OAAO,EAAE,OAAO;MAChB,QAAQ,EAAE,QAAQ;MAClB,IAAI,EAAE,IAAI;MAEV,SAAS,EAAE,UAAS,IAAI,EAAE,QAAQ;UAChC,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;UACxC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC;OAClC;MAED,eAAe,EAAE,UAAS,IAAI,EAAE,QAAQ;UACtC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;OACtC;MAED,KAAK,EAAE,UAAS,IAAI,EAAE,KAAK;UACzB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;UAC7C,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;OAC/D;MAED,aAAa,EAAE,UAAS,OAAO,EAAE,OAAO;UACtC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;OACzC;MAED,cAAc,EAAE,UAAS,OAAO,EAAE,OAAO;UACvC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;OACzC;MAED,UAAU,EAAE,UAAS,OAAO;UAC1B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;OAC9B;MAED,UAAU,EAAE,UAAS,OAAO;UAC1B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;OAC9B;;;;;;;MAQD,iBAAiB,EAAE,UAAS,QAAQ;UAClC,OAAO,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;OAC/C;;;;;;;MAQD,aAAa,EAAE,UAAS,OAAO;UAC7B,OAAO,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;OACzC;MAED,YAAY,EAAE,UAAS,KAAK,EAAE,OAAO;UACnC,IAAM,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC,SAAS,CAAC;cAC9C,EAAE,CAAC,WAAW,CAAC;kBACb,EAAE,CAAC,WAAW,EAAE;eACjB,CAAC;WACH,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC;cACb,EAAE,CAAC,OAAO,EAAE;cACZ,EAAE,CAAC,WAAW,CAAC;kBACb,EAAE,CAAC,OAAO,EAAE;kBACZ,EAAE,CAAC,QAAQ,EAAE;eACd,CAAC;cACF,EAAE,CAAC,SAAS,EAAE;WACf,CAAC,EAAE,MAAM,EAAE,CAAC;UAEb,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;UAE3B,OAAO;cACL,IAAI,EAAE,KAAK;cACX,MAAM,EAAE,OAAO;cACf,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;cACtC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC;cAC/C,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;cACxC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC;cACtC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC;WAC3C,CAAC;OACH;MAED,YAAY,EAAE,UAAS,KAAK,EAAE,UAAU;UACtC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;UACvC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;UAC3B,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;UACxB,KAAK,CAAC,IAAI,EAAE,CAAC;OACd;GACF,CAAC;;AC1nBFA,KAAC,CAAC,UAAU,GAAGA,GAAC,CAAC,UAAU,IAAI;MAC7B,IAAI,EAAE,EAAE;GACT,CAAC;AAEFA,KAAC,CAAC,MAAM,CAACA,GAAC,CAAC,UAAU,CAAC,IAAI,EAAE;MAC1B,OAAO,EAAE;UACP,IAAI,EAAE;cACJ,IAAI,EAAE,MAAM;cACZ,MAAM,EAAE,QAAQ;cAChB,SAAS,EAAE,WAAW;cACtB,KAAK,EAAE,mBAAmB;cAC1B,MAAM,EAAE,aAAa;cACrB,IAAI,EAAE,aAAa;cACnB,aAAa,EAAE,eAAe;cAC9B,SAAS,EAAE,WAAW;cACtB,WAAW,EAAE,aAAa;cAC1B,IAAI,EAAE,WAAW;WAClB;UACD,KAAK,EAAE;cACL,KAAK,EAAE,SAAS;cAChB,MAAM,EAAE,cAAc;cACtB,UAAU,EAAE,aAAa;cACzB,UAAU,EAAE,aAAa;cACzB,aAAa,EAAE,gBAAgB;cAC/B,SAAS,EAAE,YAAY;cACvB,UAAU,EAAE,aAAa;cACzB,SAAS,EAAE,YAAY;cACvB,YAAY,EAAE,gBAAgB;cAC9B,WAAW,EAAE,eAAe;cAC5B,cAAc,EAAE,kBAAkB;cAClC,SAAS,EAAE,aAAa;cACxB,aAAa,EAAE,yBAAyB;cACxC,SAAS,EAAE,oBAAoB;cAC/B,eAAe,EAAE,mBAAmB;cACpC,eAAe,EAAE,mBAAmB;cACpC,oBAAoB,EAAE,6BAA6B;cACnD,GAAG,EAAE,WAAW;cAChB,MAAM,EAAE,cAAc;cACtB,QAAQ,EAAE,UAAU;WACrB;UACD,KAAK,EAAE;cACL,KAAK,EAAE,OAAO;cACd,SAAS,EAAE,YAAY;cACvB,MAAM,EAAE,cAAc;cACtB,GAAG,EAAE,WAAW;cAChB,SAAS,EAAE,yDAAyD;WACrE;UACD,IAAI,EAAE;cACJ,IAAI,EAAE,MAAM;cACZ,MAAM,EAAE,aAAa;cACrB,MAAM,EAAE,QAAQ;cAChB,IAAI,EAAE,MAAM;cACZ,aAAa,EAAE,iBAAiB;cAChC,GAAG,EAAE,kCAAkC;cACvC,eAAe,EAAE,oBAAoB;WACtC;UACD,KAAK,EAAE;cACL,KAAK,EAAE,OAAO;cACd,WAAW,EAAE,eAAe;cAC5B,WAAW,EAAE,eAAe;cAC5B,UAAU,EAAE,iBAAiB;cAC7B,WAAW,EAAE,kBAAkB;cAC/B,MAAM,EAAE,YAAY;cACpB,MAAM,EAAE,eAAe;cACvB,QAAQ,EAAE,cAAc;WACzB;UACD,EAAE,EAAE;cACF,MAAM,EAAE,wBAAwB;WACjC;UACD,KAAK,EAAE;cACL,KAAK,EAAE,OAAO;cACd,CAAC,EAAE,QAAQ;cACX,UAAU,EAAE,OAAO;cACnB,GAAG,EAAE,MAAM;cACX,EAAE,EAAE,UAAU;cACd,EAAE,EAAE,UAAU;cACd,EAAE,EAAE,UAAU;cACd,EAAE,EAAE,UAAU;cACd,EAAE,EAAE,UAAU;cACd,EAAE,EAAE,UAAU;WACf;UACD,KAAK,EAAE;cACL,SAAS,EAAE,gBAAgB;cAC3B,OAAO,EAAE,cAAc;WACxB;UACD,OAAO,EAAE;cACP,IAAI,EAAE,MAAM;cACZ,UAAU,EAAE,aAAa;cACzB,QAAQ,EAAE,WAAW;WACtB;UACD,SAAS,EAAE;cACT,SAAS,EAAE,WAAW;cACtB,OAAO,EAAE,SAAS;cAClB,MAAM,EAAE,QAAQ;cAChB,IAAI,EAAE,YAAY;cAClB,MAAM,EAAE,cAAc;cACtB,KAAK,EAAE,aAAa;cACpB,OAAO,EAAE,cAAc;WACxB;UACD,KAAK,EAAE;cACL,MAAM,EAAE,cAAc;cACtB,IAAI,EAAE,YAAY;cAClB,UAAU,EAAE,kBAAkB;cAC9B,UAAU,EAAE,kBAAkB;cAC9B,WAAW,EAAE,aAAa;cAC1B,cAAc,EAAE,iBAAiB;cACjC,KAAK,EAAE,OAAO;cACd,cAAc,EAAE,kBAAkB;cAClC,QAAQ,EAAE,QAAQ;WACnB;UACD,QAAQ,EAAE;cACR,SAAS,EAAE,oBAAoB;cAC/B,KAAK,EAAE,OAAO;cACd,cAAc,EAAE,iBAAiB;cACjC,MAAM,EAAE,QAAQ;cAChB,mBAAmB,EAAE,sBAAsB;cAC3C,aAAa,EAAE,gBAAgB;cAC/B,SAAS,EAAE,YAAY;WACxB;UACD,IAAI,EAAE;cACJ,iBAAiB,EAAE,kBAAkB;cACrC,MAAM,EAAE,yBAAyB;cACjC,MAAM,EAAE,yBAAyB;cACjC,KAAK,EAAE,KAAK;cACZ,OAAO,EAAE,OAAO;cAChB,MAAM,EAAE,kBAAkB;cAC1B,QAAQ,EAAE,oBAAoB;cAC9B,WAAW,EAAE,uBAAuB;cACpC,eAAe,EAAE,2BAA2B;cAC5C,cAAc,EAAE,eAAe;cAC/B,aAAa,EAAE,gBAAgB;cAC/B,eAAe,EAAE,kBAAkB;cACnC,cAAc,EAAE,iBAAiB;cACjC,aAAa,EAAE,gBAAgB;cAC/B,qBAAqB,EAAE,uBAAuB;cAC9C,mBAAmB,EAAE,qBAAqB;cAC1C,SAAS,EAAE,8BAA8B;cACzC,QAAQ,EAAE,6BAA6B;cACvC,YAAY,EAAE,sDAAsD;cACpE,UAAU,EAAE,sCAAsC;cAClD,UAAU,EAAE,sCAAsC;cAClD,UAAU,EAAE,sCAAsC;cAClD,UAAU,EAAE,sCAAsC;cAClD,UAAU,EAAE,sCAAsC;cAClD,UAAU,EAAE,sCAAsC;cAClD,sBAAsB,EAAE,wBAAwB;cAChD,iBAAiB,EAAE,kBAAkB;WACtC;UACD,OAAO,EAAE;cACP,IAAI,EAAE,MAAM;cACZ,IAAI,EAAE,MAAM;WACb;UACD,WAAW,EAAE;cACX,WAAW,EAAE,oBAAoB;cACjC,MAAM,EAAE,2BAA2B;WACpC;OACF;GACF,CAAC,CAAC;;EC9JH,IAAM,YAAY,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,GAAG,CAAC;EAEhE;;;;;;EAMA,SAAS,eAAe,CAAC,QAAQ;MAC/B,IAAM,YAAY,GAAG,QAAQ,KAAK,eAAe,GAAG,aAAa,GAAG,eAAe,CAAC;MACpF,IAAM,OAAO,GAAGA,GAAC,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC;UAC7B,QAAQ,EAAE,UAAU;UACpB,IAAI,EAAE,SAAS;UACf,GAAG,EAAE,SAAS;UACd,QAAQ,EAAE,OAAO;OAClB,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;MAEpD,IAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,KAAK,EAAE,CAAC;MACtE,IAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,GAAG,GAAG,GAAG,YAAY,CAAC,CAAC,KAAK,EAAE,CAAC;MAE/E,OAAO,CAAC,MAAM,EAAE,CAAC;MAEjB,OAAO,aAAa,KAAK,KAAK,CAAC;EACjC,CAAC;EAED,IAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;EACtC,IAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;EAC/C,IAAI,cAAc,CAAC;EACnB,IAAI,MAAM,EAAE;MACV,IAAI,OAAO,GAAG,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;MACjD,IAAI,OAAO,EAAE;UACX,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;OACzC;MACD,OAAO,GAAG,qCAAqC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;MAChE,IAAI,OAAO,EAAE;UACX,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;OACzC;GACF;EAED,IAAM,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;EAE3C,IAAI,aAAa,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;EACxC,IAAI,CAAC,aAAa,IAAI,YAAY,EAAE;;MAElC,IAAI,OAAO,mBAAmB,KAAK,UAAU,EAAE;UAC7C,IAAI;;;cAGF,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;cAC9B,aAAa,GAAG,IAAI,CAAC;WACtB;UAAC,OAAO,CAAC,EAAE;;WAEX;OACF;WAAM,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;;UAEzC,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,WAAW,EAAE;cAC1C,IAAI;;;kBAGF,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;kBAC9B,aAAa,GAAG,IAAI,CAAC;eACtB;cAAC,OAAO,CAAC,EAAE;;eAEX;;WAEF;eAAM,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,WAAW,EAAE;cACnD,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;WACjD;OACF;GACF;EAED,IAAM,cAAc,IACjB,CAAC,cAAc,IAAI,MAAM;OACxB,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC;OAC7B,SAAS,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;EAErC;EACA;EACA,IAAM,cAAc,GAAG,CAAC,MAAM,IAAI,MAAM,IAAI,6DAA6D,GAAG,OAAO,CAAC;EAEpH;;;;;;;;AAQA,YAAe;MACb,KAAK,EAAE,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;MAC/C,MAAM,QAAA;MACN,MAAM,QAAA;MACN,IAAI,EAAE,CAAC,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC;MAC3C,SAAS,EAAE,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;MACvC,QAAQ,EAAE,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;MAC9C,QAAQ,EAAE,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;MAC9C,QAAQ,EAAE,CAAC,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;MAC9C,cAAc,gBAAA;MACd,aAAa,EAAE,UAAU,CAACA,GAAC,CAAC,EAAE,CAAC,MAAM,CAAC;MACtC,YAAY,cAAA;MACZ,cAAc,gBAAA;MACd,aAAa,eAAA;MACb,eAAe,iBAAA;MACf,iBAAiB,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW;MACzC,cAAc,gBAAA;GACf,CAAC;;EC1GF;;;;;;;;EAQA,SAAS,EAAE,CAAC,KAAK;MACf,OAAO,UAAS,KAAK;UACnB,OAAO,KAAK,KAAK,KAAK,CAAC;OACxB,CAAC;EACJ,CAAC;EAED,SAAS,GAAG,CAAC,KAAK,EAAE,KAAK;MACvB,OAAO,KAAK,KAAK,KAAK,CAAC;EACzB,CAAC;EAED,SAAS,IAAI,CAAC,QAAQ;MACpB,OAAO,UAAS,KAAK,EAAE,KAAK;UAC1B,OAAO,KAAK,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,QAAQ,CAAC,CAAC;OAC5C,CAAC;EACJ,CAAC;EAED,SAAS,EAAE;MACT,OAAO,IAAI,CAAC;EACd,CAAC;EAED,SAAS,IAAI;MACX,OAAO,KAAK,CAAC;EACf,CAAC;EAED,SAAS,GAAG,CAAC,CAAC;MACZ,OAAO;UACL,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;OAC/B,CAAC;EACJ,CAAC;EAED,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE;MACjB,OAAO,UAAS,IAAI;UAClB,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;OAC7B,CAAC;EACJ,CAAC;EAED,SAAS,IAAI,CAAC,CAAC;MACb,OAAO,CAAC,CAAC;EACX,CAAC;EAED,SAAS,MAAM,CAAC,GAAG,EAAE,MAAM;MACzB,OAAO;UACL,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;OAC1C,CAAC;EACJ,CAAC;EAED,IAAI,SAAS,GAAG,CAAC,CAAC;EAElB;;;;;EAKA,SAAS,QAAQ,CAAC,MAAM;MACtB,IAAM,EAAE,GAAG,EAAE,SAAS,GAAG,EAAE,CAAC;MAC5B,OAAO,MAAM,GAAG,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC;EACnC,CAAC;EAED;;;;;;;;;;;;;EAaA,SAAS,QAAQ,CAAC,IAAI;MACpB,IAAM,SAAS,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC;MAC9B,OAAO;UACL,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,SAAS,EAAE;UACrC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,UAAU,EAAE;UACxC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI;UAC7B,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG;OAC/B,CAAC;EACJ,CAAC;EAED;;;;;EAKA,SAAS,YAAY,CAAC,GAAG;MACvB,IAAM,QAAQ,GAAG,EAAE,CAAC;MACpB,KAAK,IAAM,GAAG,IAAI,GAAG,EAAE;UACrB,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;cAC3B,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;WAC1B;OACF;MACD,OAAO,QAAQ,CAAC;EAClB,CAAC;EAED;;;;;EAKA,SAAS,gBAAgB,CAAC,SAAS,EAAE,MAAM;MACzC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;MACtB,OAAO,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAS,IAAI;UACpD,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;OAC/D,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;EACd,CAAC;EAED;;;;;;;;;;EAUA,SAAS,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS;MACrC,IAAI,OAAO,CAAC;MACZ,OAAO;UACL,IAAM,OAAO,GAAG,IAAI,CAAC;UACrB,IAAM,IAAI,GAAG,SAAS,CAAC;UACvB,IAAM,KAAK,GAAG;cACZ,OAAO,GAAG,IAAI,CAAC;cACf,IAAI,CAAC,SAAS,EAAE;kBACd,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;eAC3B;WACF,CAAC;UACF,IAAM,OAAO,GAAG,SAAS,IAAI,CAAC,OAAO,CAAC;UACtC,YAAY,CAAC,OAAO,CAAC,CAAC;UACtB,OAAO,GAAG,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;UAClC,IAAI,OAAO,EAAE;cACX,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;WAC3B;OACF,CAAC;EACJ,CAAC;AAED,aAAe;MACb,EAAE,IAAA;MACF,GAAG,KAAA;MACH,IAAI,MAAA;MACJ,EAAE,IAAA;MACF,IAAI,MAAA;MACJ,IAAI,MAAA;MACJ,GAAG,KAAA;MACH,GAAG,KAAA;MACH,MAAM,QAAA;MACN,QAAQ,UAAA;MACR,QAAQ,UAAA;MACR,YAAY,cAAA;MACZ,gBAAgB,kBAAA;MAChB,QAAQ,UAAA;GACT,CAAC;;EC9JF;;;;;EAKA,SAAS,IAAI,CAAC,KAAK;MACjB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC;EAED;;;;;EAKA,SAAS,IAAI,CAAC,KAAK;MACjB,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;EACjC,CAAC;EAED;;;;;EAKA,SAAS,OAAO,CAAC,KAAK;MACpB,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;EAC1C,CAAC;EAED;;;;;EAKA,SAAS,IAAI,CAAC,KAAK;MACjB,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;EAED;;;EAGA,SAAS,IAAI,CAAC,KAAK,EAAE,IAAI;MACvB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;UACtD,IAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;UACxB,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;cACd,OAAO,IAAI,CAAC;WACb;OACF;EACH,CAAC;EAED;;;EAGA,SAAS,GAAG,CAAC,KAAK,EAAE,IAAI;MACtB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;UACtD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;cACrB,OAAO,KAAK,CAAC;WACd;OACF;MACD,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;;EAGA,SAAS,OAAO,CAAC,KAAK,EAAE,IAAI;MAC1B,OAAOA,GAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;EAChC,CAAC;EAED;;;EAGA,SAAS,QAAQ,CAAC,KAAK,EAAE,IAAI;MAC3B,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EACrC,CAAC;EAED;;;;;;EAMA,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE;MACpB,EAAE,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC;MACrB,OAAO,KAAK,CAAC,MAAM,CAAC,UAAS,IAAI,EAAE,CAAC;UAClC,OAAO,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;OACrB,EAAE,CAAC,CAAC,CAAC;EACR,CAAC;EAED;;;;EAIA,SAAS,IAAI,CAAC,UAAU;MACtB,IAAM,MAAM,GAAG,EAAE,CAAC;MAClB,IAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;MACjC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;MACb,OAAO,EAAE,GAAG,GAAG,MAAM,EAAE;UACrB,MAAM,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;OAC/B;MACD,OAAO,MAAM,CAAC;EAChB,CAAC;EAED;;;EAGA,SAAS,OAAO,CAAC,KAAK;MACpB,OAAO,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;EACjC,CAAC;EAED;;;;;;;EAOA,SAAS,SAAS,CAAC,KAAK,EAAE,EAAE;MAC1B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;UAAE,OAAO,EAAE,CAAC;OAAE;MACjC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;MAC1B,OAAO,KAAK,CAAC,MAAM,CAAC,UAAS,IAAI,EAAE,CAAC;UAClC,IAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;UACzB,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACtB,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;WACzB;eAAM;cACL,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;WACzB;UACD,OAAO,IAAI,CAAC;OACb,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACtB,CAAC;EAED;;;;;;EAMA,SAAS,OAAO,CAAC,KAAK;MACpB,IAAM,OAAO,GAAG,EAAE,CAAC;MACnB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;UACtD,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;cAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;WAAE;OAC9C;MACD,OAAO,OAAO,CAAC;EACjB,CAAC;EAED;;;;;EAKA,SAAS,MAAM,CAAC,KAAK;MACnB,IAAM,OAAO,GAAG,EAAE,CAAC;MAEnB,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;UACtD,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;cAClC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;WAC1B;OACF;MAED,OAAO,OAAO,CAAC;EACjB,CAAC;EAED;;;;EAIA,SAAS,IAAI,CAAC,KAAK,EAAE,IAAI;MACvB,IAAM,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;MACjC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;UAAE,OAAO,IAAI,CAAC;OAAE;MAEhC,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;EACxB,CAAC;EAED;;;;EAIA,SAAS,IAAI,CAAC,KAAK,EAAE,IAAI;MACvB,IAAM,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;MACjC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;UAAE,OAAO,IAAI,CAAC;OAAE;MAEhC,OAAO,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;EACxB,CAAC;EAED;;;;;;;;AAQA,cAAe;MACb,IAAI,MAAA;MACJ,IAAI,MAAA;MACJ,OAAO,SAAA;MACP,IAAI,MAAA;MACJ,IAAI,MAAA;MACJ,IAAI,MAAA;MACJ,IAAI,MAAA;MACJ,QAAQ,UAAA;MACR,GAAG,KAAA;MACH,GAAG,KAAA;MACH,IAAI,MAAA;MACJ,OAAO,SAAA;MACP,SAAS,WAAA;MACT,OAAO,SAAA;MACP,MAAM,QAAA;GACP,CAAC;;EC9MF,IAAM,OAAO,GAAG;MACd,WAAW,EAAE,CAAC;MACd,KAAK,EAAE,CAAC;MACR,OAAO,EAAE,EAAE;MACX,OAAO,EAAE,EAAE;MACX,QAAQ,EAAE,EAAE;;MAGZ,MAAM,EAAE,EAAE;MACV,IAAI,EAAE,EAAE;MACR,OAAO,EAAE,EAAE;MACX,MAAM,EAAE,EAAE;;MAGV,MAAM,EAAE,EAAE;MACV,MAAM,EAAE,EAAE;MACV,MAAM,EAAE,EAAE;MACV,MAAM,EAAE,EAAE;MACV,MAAM,EAAE,EAAE;MACV,MAAM,EAAE,EAAE;MACV,MAAM,EAAE,EAAE;MACV,MAAM,EAAE,EAAE;MACV,MAAM,EAAE,EAAE;;MAGV,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MACP,GAAG,EAAE,EAAE;MAEP,OAAO,EAAE,GAAG;MACZ,aAAa,EAAE,GAAG;MAClB,WAAW,EAAE,GAAG;MAChB,cAAc,EAAE,GAAG;GACpB,CAAC;EAEF;;;;;;;;AAQA,YAAe;;;;;;;MAOb,MAAM,EAAE,UAAC,OAAO;UACd,OAAO,KAAK,CAAC,QAAQ,CAAC;cACpB,OAAO,CAAC,SAAS;cACjB,OAAO,CAAC,GAAG;cACX,OAAO,CAAC,KAAK;cACb,OAAO,CAAC,KAAK;cACb,OAAO,CAAC,MAAM;WACf,EAAE,OAAO,CAAC,CAAC;OACb;;;;;;;MAOD,MAAM,EAAE,UAAC,OAAO;UACd,OAAO,KAAK,CAAC,QAAQ,CAAC;cACpB,OAAO,CAAC,IAAI;cACZ,OAAO,CAAC,EAAE;cACV,OAAO,CAAC,KAAK;cACb,OAAO,CAAC,IAAI;WACb,EAAE,OAAO,CAAC,CAAC;OACb;;;;;MAKD,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;MACxC,IAAI,EAAE,OAAO;GACd,CAAC;;ECtFF,IAAM,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;EAC3C,IAAM,oBAAoB,GAAG,QAAQ,CAAC;EAEtC;;;;;;;;EAQA,SAAS,UAAU,CAAC,IAAI;MACtB,OAAO,IAAI,IAAIA,GAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;EACnD,CAAC;EAED;;;;;;;;EAQA,SAAS,eAAe,CAAC,IAAI;MAC3B,OAAO,IAAI,IAAIA,GAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;EACzD,CAAC;EAED;;;;;;;;EAQA,SAAS,kBAAkB,CAAC,QAAQ;MAClC,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;MAClC,OAAO,UAAS,IAAI;UAClB,OAAO,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC;OACzD,CAAC;EACJ,CAAC;EAED;;;;;;;;EAQA,SAAS,MAAM,CAAC,IAAI;MAClB,OAAO,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;EACrC,CAAC;EAED;;;;;;;;EAQA,SAAS,SAAS,CAAC,IAAI;MACrB,OAAO,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,CAAC,CAAC;EACrC,CAAC;EAED;;;;EAIA,SAAS,MAAM,CAAC,IAAI;MAClB,OAAO,IAAI,IAAI,mDAAmD,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;EACvG,CAAC;EAED,SAAS,MAAM,CAAC,IAAI;MAClB,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;UACpB,OAAO,KAAK,CAAC;OACd;;MAGD,OAAO,IAAI,IAAI,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;EACzE,CAAC;EAED,SAAS,SAAS,CAAC,IAAI;MACrB,OAAO,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;EAC7D,CAAC;EAED,IAAM,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;EAExC,IAAM,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;EAEtC,SAAS,UAAU,CAAC,IAAI;MACtB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EACrC,CAAC;EAED,IAAM,OAAO,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;EAE5C,IAAM,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;EAE1C,SAAS,QAAQ,CAAC,IAAI;MACpB,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC;UACtB,CAAC,MAAM,CAAC,IAAI,CAAC;UACb,CAAC,IAAI,CAAC,IAAI,CAAC;UACX,CAAC,MAAM,CAAC,IAAI,CAAC;UACb,CAAC,OAAO,CAAC,IAAI,CAAC;UACd,CAAC,YAAY,CAAC,IAAI,CAAC;UACnB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;EACvB,CAAC;EAED,SAAS,MAAM,CAAC,IAAI;MAClB,OAAO,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;EAC7D,CAAC;EAED,IAAM,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;EAEtC,SAAS,MAAM,CAAC,IAAI;MAClB,OAAO,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;EAC7D,CAAC;EAED,IAAM,YAAY,GAAG,kBAAkB,CAAC,YAAY,CAAC,CAAC;EAEtD,SAAS,eAAe,CAAC,IAAI;MAC3B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC;EAChE,CAAC;EAED,IAAM,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;EAEzC,SAAS,YAAY,CAAC,IAAI;MACxB,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;EACpD,CAAC;EAED,SAAS,YAAY,CAAC,IAAI;MACxB,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;EACnD,CAAC;EAED,IAAM,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;EAE1C;;;;;;;EAOA,SAAS,gBAAgB,CAAC,KAAK,EAAE,KAAK;MACpC,OAAO,KAAK,CAAC,WAAW,KAAK,KAAK;UAC3B,KAAK,CAAC,eAAe,KAAK,KAAK,CAAC;EACzC,CAAC;EAED;;;;;;;EAOA,SAAS,mBAAmB,CAAC,IAAI,EAAE,IAAI;MACrC,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;MAEvB,IAAM,QAAQ,GAAG,EAAE,CAAC;MACpB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;UACtD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;OACrC;MACD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;MACpB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;UAC9C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;OACjC;MACD,OAAO,QAAQ,CAAC;EAClB,CAAC;EAED;;;;;EAKA,IAAM,SAAS,GAAG,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,cAAc,GAAG,EAAE,GAAG,QAAQ,GAAG,MAAM,CAAC;EAE5E;;;;;;;EAOA,SAAS,UAAU,CAAC,IAAI;MACtB,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;UAChB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;OAC9B;MAED,IAAI,IAAI,EAAE;UACR,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;OAC/B;MAED,OAAO,CAAC,CAAC;EACX,CAAC;EAED;;;;;;EAMA,SAASC,SAAO,CAAC,IAAI;MACnB,IAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;MAE7B,IAAI,GAAG,KAAK,CAAC,EAAE;UACb,OAAO,IAAI,CAAC;OACb;WAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;;UAErE,OAAO,IAAI,CAAC;OACb;WAAM,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,SAAS,KAAK,EAAE,EAAE;;UAEtE,OAAO,IAAI,CAAC;OACb;MAED,OAAO,KAAK,CAAC;EACf,CAAC;EAED;;;EAGA,SAAS,gBAAgB,CAAC,IAAI;MAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;UACtC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;OAC5B;EACH,CAAC;EAED;;;;;;EAMA,SAAS,QAAQ,CAAC,IAAI,EAAE,IAAI;MAC1B,OAAO,IAAI,EAAE;UACX,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;cAAE,OAAO,IAAI,CAAC;WAAE;UAChC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;cAAE,MAAM;WAAE;UAEhC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;OACxB;MACD,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;;;;;EAMA,SAAS,mBAAmB,CAAC,IAAI,EAAE,IAAI;MACrC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;MAEvB,OAAO,IAAI,EAAE;UACX,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;cAAE,MAAM;WAAE;UACtC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;cAAE,OAAO,IAAI,CAAC;WAAE;UAChC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;cAAE,MAAM;WAAE;UAEhC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;OACxB;MACD,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;;;;;EAMA,SAAS,YAAY,CAAC,IAAI,EAAE,IAAI;MAC9B,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;MAEzB,IAAM,SAAS,GAAG,EAAE,CAAC;MACrB,QAAQ,CAAC,IAAI,EAAE,UAAS,EAAE;UACxB,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;cACnB,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;WACpB;UAED,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC;OACjB,CAAC,CAAC;MACH,OAAO,SAAS,CAAC;EACnB,CAAC;EAED;;;EAGA,SAAS,YAAY,CAAC,IAAI,EAAE,IAAI;MAC9B,IAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;MACrC,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5C,CAAC;EAED;;;;;;EAMA,SAAS,cAAc,CAAC,KAAK,EAAE,KAAK;MAClC,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;MACtC,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE;UACvC,IAAID,GAAC,CAAC,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE;cAAE,OAAO,CAAC,CAAC;WAAE;OAChD;MACD,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;;;;;EAMA,SAAS,QAAQ,CAAC,IAAI,EAAE,IAAI;MAC1B,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;MAEzB,IAAM,KAAK,GAAG,EAAE,CAAC;MACjB,OAAO,IAAI,EAAE;UACX,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;cAAE,MAAM;WAAE;UAC1B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;UACjB,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC;OAC7B;MACD,OAAO,KAAK,CAAC;EACf,CAAC;EAED;;;;;;EAMA,SAAS,QAAQ,CAAC,IAAI,EAAE,IAAI;MAC1B,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC;MAEzB,IAAM,KAAK,GAAG,EAAE,CAAC;MACjB,OAAO,IAAI,EAAE;UACX,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;cAAE,MAAM;WAAE;UAC1B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;UACjB,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;OACzB;MACD,OAAO,KAAK,CAAC;EACf,CAAC;EAED;;;;;;EAMA,SAAS,cAAc,CAAC,IAAI,EAAE,IAAI;MAChC,IAAM,WAAW,GAAG,EAAE,CAAC;MACvB,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;;MAGvB,CAAC,SAAS,MAAM,CAAC,OAAO;UACtB,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;cACrC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;WAC3B;UACD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;cACnE,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;WACjC;OACF,EAAE,IAAI,CAAC,CAAC;MAET,OAAO,WAAW,CAAC;EACrB,CAAC;EAED;;;;;;;EAOA,SAAS,IAAI,CAAC,IAAI,EAAE,WAAW;MAC7B,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;MAC/B,IAAM,OAAO,GAAGA,GAAC,CAAC,GAAG,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAE9C,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;MACnC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;MAE1B,OAAO,OAAO,CAAC;EACjB,CAAC;EAED;;;;;;EAMA,SAAS,WAAW,CAAC,IAAI,EAAE,SAAS;MAClC,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC;MACnC,IAAI,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC;MAClC,IAAI,IAAI,EAAE;UACR,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;OACjC;WAAM;UACL,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;OAC1B;MACD,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;;;;;EAMA,SAAS,gBAAgB,CAAC,IAAI,EAAE,MAAM;MACpCA,GAAC,CAAC,IAAI,CAAC,MAAM,EAAE,UAAS,GAAG,EAAE,KAAK;UAChC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;OACzB,CAAC,CAAC;MACH,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;;;;;EAMA,SAAS,eAAe,CAAC,KAAK;MAC5B,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;EAC5B,CAAC;EAED;;;;;;EAMA,SAAS,gBAAgB,CAAC,KAAK;MAC7B,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;EACjD,CAAC;EAED;;;;;;EAMA,SAAS,WAAW,CAAC,KAAK;MACxB,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;EAC3D,CAAC;EAED;;;;;;;EAOA,SAAS,YAAY,CAAC,IAAI,EAAE,QAAQ;MAClC,OAAO,IAAI,IAAI,IAAI,KAAK,QAAQ,EAAE;UAChC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;cACxB,OAAO,KAAK,CAAC;WACd;UACD,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;OACxB;MAED,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;;;;;;EAOA,SAAS,aAAa,CAAC,IAAI,EAAE,QAAQ;MACnC,IAAI,CAAC,QAAQ,EAAE;UACb,OAAO,KAAK,CAAC;OACd;MACD,OAAO,IAAI,IAAI,IAAI,KAAK,QAAQ,EAAE;UAChC,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;cACtD,OAAO,KAAK,CAAC;WACd;UACD,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;OACxB;MAED,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;;;;;EAMA,SAAS,iBAAiB,CAAC,KAAK,EAAE,QAAQ;MACxC,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;EACtE,CAAC;EAED;;;;;;EAMA,SAAS,kBAAkB,CAAC,KAAK,EAAE,QAAQ;MACzC,OAAO,gBAAgB,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;EACxE,CAAC;EAED;;;;;EAKA,SAAS,QAAQ,CAAC,IAAI;MACpB,IAAI,MAAM,GAAG,CAAC,CAAC;MACf,QAAQ,IAAI,GAAG,IAAI,CAAC,eAAe,GAAG;UACpC,MAAM,IAAI,CAAC,CAAC;OACb;MACD,OAAO,MAAM,CAAC;EAChB,CAAC;EAED,SAAS,WAAW,CAAC,IAAI;MACvB,OAAO,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;EAC/D,CAAC;EAED;;;;;;;EAOA,SAAS,SAAS,CAAC,KAAK,EAAE,iBAAiB;MACzC,IAAI,IAAI,CAAC;MACT,IAAI,MAAM,CAAC;MAEX,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;UACtB,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;cAC1B,OAAO,IAAI,CAAC;WACb;UAED,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;UAC7B,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;OAC/B;WAAM,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;UAClC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;UAC/C,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;OAC3B;WAAM;UACL,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;UAClB,MAAM,GAAG,iBAAiB,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;OACnD;MAED,OAAO;UACL,IAAI,EAAE,IAAI;UACV,MAAM,EAAE,MAAM;OACf,CAAC;EACJ,CAAC;EAED;;;;;;;EAOA,SAAS,SAAS,CAAC,KAAK,EAAE,iBAAiB;MACzC,IAAI,IAAI,EAAE,MAAM,CAAC;MAEjB,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,EAAE;UAC3C,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;cAC1B,OAAO,IAAI,CAAC;WACb;UAED,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;UAC7B,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;OACnC;WAAM,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;UAClC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;UAC3C,MAAM,GAAG,CAAC,CAAC;OACZ;WAAM;UACL,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;UAClB,MAAM,GAAG,iBAAiB,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;OACxE;MAED,OAAO;UACL,IAAI,EAAE,IAAI;UACV,MAAM,EAAE,MAAM;OACf,CAAC;EACJ,CAAC;EAED;;;;;;;EAOA,SAAS,WAAW,CAAC,MAAM,EAAE,MAAM;MACjC,OAAO,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC;EACxE,CAAC;EAED;;;;;;EAMA,SAAS,cAAc,CAAC,KAAK;MAC3B,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAIC,SAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;UACzE,OAAO,IAAI,CAAC;OACb;MAED,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;MACzD,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;MACtD,IAAI,CAAC,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;UACxE,OAAO,IAAI,CAAC;OACb;MAED,OAAO,KAAK,CAAC;EACf,CAAC;EAED;;;;;;;EAOA,SAAS,cAAc,CAAC,KAAK,EAAE,IAAI;MACjC,OAAO,KAAK,EAAE;UACZ,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;cACf,OAAO,KAAK,CAAC;WACd;UAED,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;OAC1B;MAED,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;;;;;;EAOA,SAAS,cAAc,CAAC,KAAK,EAAE,IAAI;MACjC,OAAO,KAAK,EAAE;UACZ,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE;cACf,OAAO,KAAK,CAAC;WACd;UAED,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;OAC1B;MAED,OAAO,IAAI,CAAC;EACd,CAAC;EAED;;;;;;EAMA,SAAS,WAAW,CAAC,KAAK;MACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;UACvB,OAAO,KAAK,CAAC;OACd;MAED,IAAM,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;MACzD,OAAO,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,SAAS,CAAC,CAAC;EAChD,CAAC;EAED;;;;;;;;EAQA,SAAS,SAAS,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,iBAAiB;MACjE,IAAI,KAAK,GAAG,UAAU,CAAC;MAEvB,OAAO,KAAK,EAAE;UACZ,OAAO,CAAC,KAAK,CAAC,CAAC;UAEf,IAAI,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;cAChC,MAAM;WACP;UAED,IAAM,YAAY,GAAG,iBAAiB;cACnB,UAAU,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI;cAC9B,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,CAAC;UAChD,KAAK,GAAG,SAAS,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;OACxC;EACH,CAAC;EAED;;;;;;;;EAQA,SAAS,cAAc,CAAC,QAAQ,EAAE,IAAI;MACpC,IAAM,SAAS,GAAG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;MACxD,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC;EAC3C,CAAC;EAED;;;;;;;;EAQA,SAAS,cAAc,CAAC,QAAQ,EAAE,OAAO;MACvC,IAAI,OAAO,GAAG,QAAQ,CAAC;MACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;UAClD,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;cAC3C,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;WAC7D;eAAM;cACL,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;WAC1C;OACF;MACD,OAAO,OAAO,CAAC;EACjB,CAAC;EAED;;;;;;;;;;;;EAYA,SAAS,SAAS,CAAC,KAAK,EAAE,OAAO;MAC/B,IAAI,sBAAsB,GAAG,OAAO,IAAI,OAAO,CAAC,sBAAsB,CAAC;MACvE,IAAM,mBAAmB,GAAG,OAAO,IAAI,OAAO,CAAC,mBAAmB,CAAC;MACnE,IAAM,oBAAoB,GAAG,OAAO,IAAI,OAAO,CAAC,oBAAoB,CAAC;MAErE,IAAI,oBAAoB,EAAE;UACxB,sBAAsB,GAAG,IAAI,CAAC;OAC/B;;MAGD,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,mBAAmB,CAAC,EAAE;UACrE,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE;cAC1B,OAAO,KAAK,CAAC,IAAI,CAAC;WACnB;eAAM,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;cAClC,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC;WAC/B;OACF;;MAGD,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;UACtB,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;OAC3C;WAAM;UACL,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;UACtD,IAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;UACnE,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;UAE7C,IAAI,CAAC,sBAAsB,EAAE;cAC3B,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;cAC7B,gBAAgB,CAAC,KAAK,CAAC,CAAC;WACzB;UAED,IAAI,oBAAoB,EAAE;cACxB,IAAIA,SAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;kBACvB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;eACpB;cACD,IAAIA,SAAO,CAAC,KAAK,CAAC,EAAE;kBAClB,MAAM,CAAC,KAAK,CAAC,CAAC;kBACd,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC;eAC/B;WACF;UAED,OAAO,KAAK,CAAC;OACd;EACH,CAAC;EAED;;;;;;;;;;;;EAYA,SAAS,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO;;MAErC,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;MAE1D,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;UACrB,OAAO,IAAI,CAAC;OACb;WAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;UACjC,OAAO,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;OAClC;MAED,OAAO,SAAS,CAAC,MAAM,CAAC,UAAS,IAAI,EAAE,MAAM;UAC3C,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;cACvB,IAAI,GAAG,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;WAClC;UAED,OAAO,SAAS,CAAC;cACf,IAAI,EAAE,MAAM;cACZ,MAAM,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC;WACnD,EAAE,OAAO,CAAC,CAAC;OACb,CAAC,CAAC;EACL,CAAC;EAED;;;;;;;EAOA,SAAS,UAAU,CAAC,KAAK,EAAE,QAAQ;;;;MAIjC,IAAM,IAAI,GAAG,QAAQ,GAAG,MAAM,GAAG,eAAe,CAAC;MACjD,IAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;MACjD,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC;MAExD,IAAI,SAAS,EAAE,SAAS,CAAC;MACzB,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE;UACrB,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;UAC5C,SAAS,GAAG,WAAW,CAAC;OACzB;WAAM;UACL,SAAS,GAAG,WAAW,CAAC;UACxB,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC;OAClC;;MAGD,IAAI,KAAK,GAAG,SAAS,IAAI,SAAS,CAAC,SAAS,EAAE,KAAK,EAAE;UACnD,sBAAsB,EAAE,QAAQ;UAChC,mBAAmB,EAAE,QAAQ;OAC9B,CAAC,CAAC;;MAGH,IAAI,CAAC,KAAK,IAAI,SAAS,KAAK,KAAK,CAAC,IAAI,EAAE;UACtC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;OAC7C;MAED,OAAO;UACL,SAAS,EAAE,KAAK;UAChB,SAAS,EAAE,SAAS;OACrB,CAAC;EACJ,CAAC;EAED,SAAS,MAAM,CAAC,QAAQ;MACtB,OAAO,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;EAC1C,CAAC;EAED,SAAS,UAAU,CAAC,IAAI;MACtB,OAAO,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;EACvC,CAAC;EAED;;;;;;;;EAQA,SAAS,MAAM,CAAC,IAAI,EAAE,aAAa;MACjC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;UAAE,OAAO;OAAE;MAC1C,IAAI,IAAI,CAAC,UAAU,EAAE;UAAE,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;OAAE;MAE/D,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;MAC/B,IAAI,CAAC,aAAa,EAAE;UAClB,IAAM,KAAK,GAAG,EAAE,CAAC;UACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;cAC1D,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;WAChC;UAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;cAChD,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;WACrC;OACF;MAED,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;EAC3B,CAAC;EAED;;;;;;EAMA,SAAS,WAAW,CAAC,IAAI,EAAE,IAAI;MAC7B,OAAO,IAAI,EAAE;UACX,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;cACnC,MAAM;WACP;UAED,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;UAC/B,MAAM,CAAC,IAAI,CAAC,CAAC;UACb,IAAI,GAAG,MAAM,CAAC;OACf;EACH,CAAC;EAED;;;;;;;;;EASA,SAAS,OAAO,CAAC,IAAI,EAAE,QAAQ;MAC7B,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,CAAC,WAAW,EAAE,EAAE;UAC1D,OAAO,IAAI,CAAC;OACb;MAED,IAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;MAEjC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;UACtB,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;OAC5C;MAED,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;MACvD,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;MAC3B,MAAM,CAAC,IAAI,CAAC,CAAC;MAEb,OAAO,OAAO,CAAC;EACjB,CAAC;EAED,IAAM,UAAU,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;EAElD;;;;EAIA,SAAS,KAAK,CAAC,KAAK,EAAE,eAAe;MACnC,IAAM,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;MAC9D,IAAI,eAAe,EAAE;UACnB,OAAO,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;OACnC;MACD,OAAO,GAAG,CAAC;EACb,CAAC;EAED;;;;;;;;EAQA,SAAS,IAAI,CAAC,KAAK,EAAE,gBAAgB;MACnC,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;MAE1B,IAAI,gBAAgB,EAAE;UACpB,IAAM,QAAQ,GAAG,uCAAuC,CAAC;UACzD,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAS,KAAK,EAAE,QAAQ,EAAE,IAAI;cAC9D,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;cAC1B,IAAM,sBAAsB,GAAG,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC;kBAC1C,CAAC,CAAC,QAAQ,CAAC;cACxC,IAAM,WAAW,GAAG,2CAA2C,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;cAE3E,OAAO,KAAK,IAAI,CAAC,sBAAsB,IAAI,WAAW,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;WACtE,CAAC,CAAC;UACH,MAAM,GAAGD,GAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;OACzB;MAED,OAAO,MAAM,CAAC;EAChB,CAAC;EAED,SAAS,kBAAkB,CAAC,WAAW;MACrC,IAAM,YAAY,GAAGA,GAAC,CAAC,WAAW,CAAC,CAAC;MACpC,IAAM,GAAG,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC;MAClC,IAAM,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;MAE9C,OAAO;UACL,IAAI,EAAE,GAAG,CAAC,IAAI;UACd,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,MAAM;OACtB,CAAC;EACJ,CAAC;EAED,SAAS,YAAY,CAAC,KAAK,EAAE,MAAM;MACjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAS,GAAG;UACtC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;OAC5B,CAAC,CAAC;EACL,CAAC;EAED,SAAS,YAAY,CAAC,KAAK,EAAE,MAAM;MACjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAS,GAAG;UACtC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;OAC7B,CAAC,CAAC;EACL,CAAC;EAED;;;;;;;;EAQA,SAAS,gBAAgB,CAAC,IAAI;MAC5B,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;EAClF,CAAC;AAED,YAAe;;MAEb,SAAS,WAAA;;MAET,oBAAoB,sBAAA;;MAEpB,KAAK,EAAE,SAAS;;MAEhB,SAAS,EAAE,QAAM,SAAS,SAAM;MAChC,kBAAkB,oBAAA;MAClB,UAAU,YAAA;MACV,eAAe,iBAAA;MACf,MAAM,QAAA;MACN,SAAS,WAAA;MACT,MAAM,QAAA;MACN,MAAM,QAAA;MACN,UAAU,YAAA;MACV,SAAS,WAAA;MACT,QAAQ,UAAA;MACR,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;MAC3B,YAAY,cAAA;MACZ,MAAM,QAAA;MACN,YAAY,cAAA;MACZ,KAAK,OAAA;MACL,MAAM,QAAA;MACN,OAAO,SAAA;MACP,MAAM,QAAA;MACN,MAAM,QAAA;MACN,YAAY,cAAA;MACZ,eAAe,iBAAA;MACf,QAAQ,UAAA;MACR,KAAK,EAAE,kBAAkB,CAAC,KAAK,CAAC;MAChC,IAAI,MAAA;MACJ,IAAI,EAAE,kBAAkB,CAAC,IAAI,CAAC;MAC9B,MAAM,EAAE,kBAAkB,CAAC,MAAM,CAAC;MAClC,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC;MAC5B,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC;MAC5B,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC;MAC5B,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC;MAC5B,KAAK,EAAE,kBAAkB,CAAC,KAAK,CAAC;MAChC,UAAU,YAAA;MACV,OAAO,WAAA;MACP,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAEC,SAAO,CAAC;MAC1C,gBAAgB,kBAAA;MAChB,mBAAmB,qBAAA;MACnB,UAAU,YAAA;MACV,eAAe,iBAAA;MACf,gBAAgB,kBAAA;MAChB,WAAW,aAAA;MACX,YAAY,cAAA;MACZ,aAAa,eAAA;MACb,iBAAiB,mBAAA;MACjB,kBAAkB,oBAAA;MAClB,SAAS,WAAA;MACT,SAAS,WAAA;MACT,WAAW,aAAA;MACX,cAAc,gBAAA;MACd,cAAc,gBAAA;MACd,cAAc,gBAAA;MACd,WAAW,aAAA;MACX,SAAS,WAAA;MACT,QAAQ,UAAA;MACR,mBAAmB,qBAAA;MACnB,YAAY,cAAA;MACZ,YAAY,cAAA;MACZ,QAAQ,UAAA;MACR,QAAQ,UAAA;MACR,cAAc,gBAAA;MACd,cAAc,gBAAA;MACd,IAAI,MAAA;MACJ,WAAW,aAAA;MACX,gBAAgB,kBAAA;MAChB,QAAQ,UAAA;MACR,WAAW,aAAA;MACX,cAAc,gBAAA;MACd,cAAc,gBAAA;MACd,SAAS,WAAA;MACT,UAAU,YAAA;MACV,MAAM,QAAA;MACN,UAAU,YAAA;MACV,MAAM,QAAA;MACN,WAAW,aAAA;MACX,OAAO,SAAA;MACP,IAAI,MAAA;MACJ,KAAK,OAAA;MACL,kBAAkB,oBAAA;MAClB,YAAY,cAAA;MACZ,YAAY,cAAA;MACZ,gBAAgB,kBAAA;GACjB,CAAC;;ECzkCF;;;;;;;;;EASA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO;MAC1C,IAAI,SAAS,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;MAC1C,IAAI,MAAM,CAAC;MAEX,IAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;MAC/C,IAAI,aAAa,CAAC;MAClB,IAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;MACpD,KAAK,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;UACrD,IAAI,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE;cAClC,SAAS;WACV;UACD,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;UAC7C,IAAI,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE;cAC3D,MAAM;WACP;UACD,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;OACpC;MAED,IAAI,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;UACtD,IAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;UACvD,IAAI,WAAW,GAAG,IAAI,CAAC;UACvB,cAAc,CAAC,iBAAiB,CAAC,aAAa,IAAI,SAAS,CAAC,CAAC;UAC7D,cAAc,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;UACxC,WAAW,GAAG,aAAa,GAAG,aAAa,CAAC,WAAW,GAAG,SAAS,CAAC,UAAU,CAAC;UAE/E,IAAM,WAAW,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;UAC1C,WAAW,CAAC,WAAW,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;UACxD,IAAI,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC;UAE/D,OAAO,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC,MAAM,IAAI,WAAW,CAAC,WAAW,EAAE;cAC1E,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC;cAC1C,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;WACvC;;UAGD,IAAM,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC;UAEpC,IAAI,OAAO,IAAI,WAAW,CAAC,WAAW,IAAI,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC;cAC3E,SAAS,KAAK,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE;cAC5C,SAAS,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC;cAC1C,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC;WACvC;UAED,SAAS,GAAG,WAAW,CAAC;UACxB,MAAM,GAAG,SAAS,CAAC;OACpB;MAED,OAAO;UACL,IAAI,EAAE,SAAS;UACf,MAAM,EAAE,MAAM;OACf,CAAC;EACJ,CAAC;EAED;;;;;EAKA,SAAS,gBAAgB,CAAC,KAAK;MAC7B,IAAM,aAAa,GAAG,UAAS,SAAS,EAAE,MAAM;UAC9C,IAAI,IAAI,EAAE,iBAAiB,CAAC;UAE5B,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;cACzB,IAAM,aAAa,GAAG,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;cACpE,IAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,eAAe,CAAC;cAChE,IAAI,GAAG,aAAa,IAAI,SAAS,CAAC,UAAU,CAAC;cAC7C,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;cAC/D,iBAAiB,GAAG,CAAC,aAAa,CAAC;WACpC;eAAM;cACL,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,SAAS,CAAC;cACjD,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;kBACpB,OAAO,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;eAC/B;cAED,MAAM,GAAG,CAAC,CAAC;cACX,iBAAiB,GAAG,KAAK,CAAC;WAC3B;UAED,OAAO;cACL,IAAI,EAAE,IAAI;cACV,eAAe,EAAE,iBAAiB;cAClC,MAAM,EAAE,MAAM;WACf,CAAC;OACH,CAAC;MAEF,IAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;MAClD,IAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;MAErD,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;MACvC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;MACzC,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;MAC9C,OAAO,SAAS,CAAC;EACnB,CAAC;EAED;;;;;;;;;EASA;MACE,sBAAY,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;UACxB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;UACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;UACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;UACb,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;;UAGb,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;;UAElD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;;UAE1C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;;UAE9C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;;UAE1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;OAC3C;;MAGD,kCAAW,GAAX;UACE,IAAI,GAAG,CAAC,iBAAiB,EAAE;cACzB,IAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;cACxC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;cACpC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;cAElC,OAAO,QAAQ,CAAC;WACjB;eAAM;cACL,IAAM,SAAS,GAAG,gBAAgB,CAAC;kBACjC,IAAI,EAAE,IAAI,CAAC,EAAE;kBACb,MAAM,EAAE,IAAI,CAAC,EAAE;eAChB,CAAC,CAAC;cAEH,SAAS,CAAC,WAAW,CAAC,UAAU,EAAE,gBAAgB,CAAC;kBACjD,IAAI,EAAE,IAAI,CAAC,EAAE;kBACb,MAAM,EAAE,IAAI,CAAC,EAAE;eAChB,CAAC,CAAC,CAAC;cAEJ,OAAO,SAAS,CAAC;WAClB;OACF;MAED,gCAAS,GAAT;UACE,OAAO;cACL,EAAE,EAAE,IAAI,CAAC,EAAE;cACX,EAAE,EAAE,IAAI,CAAC,EAAE;cACX,EAAE,EAAE,IAAI,CAAC,EAAE;cACX,EAAE,EAAE,IAAI,CAAC,EAAE;WACZ,CAAC;OACH;MAED,oCAAa,GAAb;UACE,OAAO;cACL,IAAI,EAAE,IAAI,CAAC,EAAE;cACb,MAAM,EAAE,IAAI,CAAC,EAAE;WAChB,CAAC;OACH;MAED,kCAAW,GAAX;UACE,OAAO;cACL,IAAI,EAAE,IAAI,CAAC,EAAE;cACb,MAAM,EAAE,IAAI,CAAC,EAAE;WAChB,CAAC;OACH;;;;MAKD,6BAAM,GAAN;UACE,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;UACrC,IAAI,GAAG,CAAC,iBAAiB,EAAE;cACzB,IAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;cAC1C,IAAI,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE;kBAC5B,SAAS,CAAC,eAAe,EAAE,CAAC;eAC7B;cACD,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;WAC/B;eAAM;cACL,SAAS,CAAC,MAAM,EAAE,CAAC;WACpB;UAED,OAAO,IAAI,CAAC;OACb;;;;;;MAOD,qCAAc,GAAd,UAAe,SAAS;UACtB,IAAM,MAAM,GAAGD,GAAC,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC;UACrC,IAAI,SAAS,CAAC,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;cACpD,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;WACnF;UAED,OAAO,IAAI,CAAC;OACb;;;;MAKD,gCAAS,GAAT;;;;;;UAME,IAAM,eAAe,GAAG,UAAS,KAAK,EAAE,aAAa;cACnD,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC;mBACpD,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;mBAC3E,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC;mBACzE,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE;kBACrF,OAAO,KAAK,CAAC;eACd;;cAGD,IAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;cACpD,IAAI,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa;mBAChG,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,aAAa,CAAC,EAAE;;kBAEtG,IAAI,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;sBAC7B,OAAO,KAAK,CAAC;mBACd;;kBAED,aAAa,GAAG,CAAC,aAAa,CAAC;eAChC;cAED,IAAM,SAAS,GAAG,aAAa,GAAG,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,cAAc,CAAC;oBAC1F,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,cAAc,CAAC,CAAC;cACjE,OAAO,SAAS,IAAI,KAAK,CAAC;WAC3B,CAAC;UAEF,IAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;UAC5D,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,CAAC;UAE/F,OAAO,IAAI,YAAY,CACrB,UAAU,CAAC,IAAI,EACf,UAAU,CAAC,MAAM,EACjB,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,MAAM,CAChB,CAAC;OACH;;;;;;;;;;MAWD,4BAAK,GAAL,UAAM,IAAI,EAAE,OAAO;UACjB,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;UAEvB,IAAM,eAAe,GAAG,OAAO,IAAI,OAAO,CAAC,eAAe,CAAC;UAC3D,IAAM,aAAa,GAAG,OAAO,IAAI,OAAO,CAAC,aAAa,CAAC;;UAGvD,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;UACxC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;UAEpC,IAAM,KAAK,GAAG,EAAE,CAAC;UACjB,IAAM,aAAa,GAAG,EAAE,CAAC;UAEzB,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,EAAE,UAAS,KAAK;cAChD,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;kBAC9B,OAAO;eACR;cAED,IAAI,IAAI,CAAC;cACT,IAAI,aAAa,EAAE;kBACjB,IAAI,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;sBAC9B,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;mBAChC;kBACD,IAAI,GAAG,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE;sBAC5E,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;mBACnB;eACF;mBAAM,IAAI,eAAe,EAAE;kBAC1B,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;eACvC;mBAAM;kBACL,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;eACnB;cAED,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;kBACtB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;eAClB;WACF,EAAE,IAAI,CAAC,CAAC;UAET,OAAO,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;OAC5B;;;;;MAMD,qCAAc,GAAd;UACE,OAAO,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;OAC7C;;;;;;;MAQD,6BAAM,GAAN,UAAO,IAAI;UACT,IAAM,aAAa,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;UAClD,IAAM,WAAW,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;UAEhD,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW,EAAE;cAClC,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;WAC7D;UAED,IAAM,cAAc,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;UAExC,IAAI,aAAa,EAAE;cACjB,cAAc,CAAC,EAAE,GAAG,aAAa,CAAC;cAClC,cAAc,CAAC,EAAE,GAAG,CAAC,CAAC;WACvB;UAED,IAAI,WAAW,EAAE;cACf,cAAc,CAAC,EAAE,GAAG,WAAW,CAAC;cAChC,cAAc,CAAC,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;WACjD;UAED,OAAO,IAAI,YAAY,CACrB,cAAc,CAAC,EAAE,EACjB,cAAc,CAAC,EAAE,EACjB,cAAc,CAAC,EAAE,EACjB,cAAc,CAAC,EAAE,CAClB,CAAC;OACH;;;;;MAMD,+BAAQ,GAAR,UAAS,iBAAiB;UACxB,IAAI,iBAAiB,EAAE;cACrB,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;WAC7D;eAAM;cACL,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;WAC7D;OACF;;;;MAKD,gCAAS,GAAT;UACE,IAAM,eAAe,GAAG,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC;UAC5C,IAAM,cAAc,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;UAExC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;cAC/D,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;WAC5B;UAED,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE;cACjE,cAAc,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;cAC/C,cAAc,CAAC,EAAE,GAAG,CAAC,CAAC;cAEtB,IAAI,eAAe,EAAE;kBACnB,cAAc,CAAC,EAAE,GAAG,cAAc,CAAC,EAAE,CAAC;kBACtC,cAAc,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;eACvC;WACF;UAED,OAAO,IAAI,YAAY,CACrB,cAAc,CAAC,EAAE,EACjB,cAAc,CAAC,EAAE,EACjB,cAAc,CAAC,EAAE,EACjB,cAAc,CAAC,EAAE,CAClB,CAAC;OACH;;;;;MAMD,qCAAc,GAAd;UACE,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;cACtB,OAAO,IAAI,CAAC;WACb;UAED,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;UAC7B,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE;cAC5B,aAAa,EAAE,IAAI;WACpB,CAAC,CAAC;;UAGH,IAAM,KAAK,GAAG,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,UAAS,KAAK;cAClE,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;WAC3C,CAAC,CAAC;UAEH,IAAM,YAAY,GAAG,EAAE,CAAC;UACxBA,GAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAS,GAAG,EAAE,IAAI;;cAE9B,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;cAC/B,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;kBACzD,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;eAC3B;cACD,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;WACzB,CAAC,CAAC;;UAGHA,GAAC,CAAC,IAAI,CAAC,YAAY,EAAE,UAAS,GAAG,EAAE,IAAI;cACrC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;WACzB,CAAC,CAAC;UAEH,OAAO,IAAI,YAAY,CACrB,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,MAAM,CACb,CAAC,SAAS,EAAE,CAAC;OACf;;;;MAKD,+BAAQ,GAAR,UAAS,IAAI;UACX,OAAO;cACL,IAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;cAC7C,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;WACjE,CAAC;OACH;;;;;MAMD,mCAAY,GAAZ,UAAa,IAAI;UACf,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,EAAE;cAC9C,OAAO,KAAK,CAAC;WACd;UAED,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;UACzC,OAAO,IAAI,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;OAChD;;;;MAKD,kCAAW,GAAX;UACE,OAAO,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC;OACnD;;;;;;MAOD,6CAAsB,GAAtB;UACE,IAAI,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;cACxD,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;cAClC,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;WACvE;;;;;;UAOD,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;UAC7B,IAAI,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;cACpD,OAAO,GAAG,CAAC;WACZ;;UAGD,IAAI,WAAW,CAAC;UAChB,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;cACxB,IAAM,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;cACnE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;cACpC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;kBAC9B,WAAW,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;eAC5E;WACF;eAAM;cACL,WAAW,GAAG,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;WAC9D;;UAGD,IAAI,cAAc,GAAG,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC;UAC3E,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;;UAGhG,IAAI,cAAc,CAAC,MAAM,EAAE;cACzB,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC,CAAC;cACvD,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;WACxD;UAED,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;OACzB;;;;;;;MAQD,iCAAU,GAAV,UAAW,IAAI;UACb,IAAM,GAAG,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC,cAAc,EAAE,CAAC;UAC3D,IAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;UAErE,IAAI,IAAI,CAAC,SAAS,EAAE;cAClB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;WAC9D;eAAM;cACL,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;WAClC;UAED,OAAO,IAAI,CAAC;OACb;;;;MAKD,gCAAS,GAAT,UAAU,MAAM;UACd,IAAM,iBAAiB,GAAGA,GAAC,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,IAAI,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;UAC1D,IAAM,GAAG,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC,cAAc,EAAE,CAAC;UAE3D,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE;cACd,UAAU,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;WACnC;UACD,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,UAAS,SAAS;cAC5C,OAAO,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;WAClC,CAAC,CAAC;UACH,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE;cACd,UAAU,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;WACnC;UACD,OAAO,UAAU,CAAC;OACnB;;;;;;MAOD,+BAAQ,GAAR;UACE,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;UACrC,OAAO,GAAG,CAAC,iBAAiB,GAAG,SAAS,CAAC,QAAQ,EAAE,GAAG,SAAS,CAAC,IAAI,CAAC;OACtE;;;;;;;MAQD,mCAAY,GAAZ,UAAa,SAAS;UACpB,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;UAElC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;cAC9B,OAAO,IAAI,CAAC;WACb;UAED,IAAM,UAAU,GAAG,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAS,KAAK;cAC5D,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;WAChC,CAAC,CAAC;UAEH,IAAI,SAAS,EAAE;cACb,QAAQ,GAAG,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAS,KAAK;kBACpD,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;eAChC,CAAC,CAAC;WACJ;UAED,OAAO,IAAI,YAAY,CACrB,UAAU,CAAC,IAAI,EACf,UAAU,CAAC,MAAM,EACjB,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,MAAM,CAChB,CAAC;OACH;;;;;;MAOD,+BAAQ,GAAR,UAAS,QAAQ;UACf,OAAO;cACL,CAAC,EAAE;kBACD,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;kBAC3C,MAAM,EAAE,IAAI,CAAC,EAAE;eAChB;cACD,CAAC,EAAE;kBACD,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;kBAC3C,MAAM,EAAE,IAAI,CAAC,EAAE;eAChB;WACF,CAAC;OACH;;;;;;MAOD,mCAAY,GAAZ,UAAa,KAAK;UAChB,OAAO;cACL,CAAC,EAAE;kBACD,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;kBAChE,MAAM,EAAE,IAAI,CAAC,EAAE;eAChB;cACD,CAAC,EAAE;kBACD,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;kBAChE,MAAM,EAAE,IAAI,CAAC,EAAE;eAChB;WACF,CAAC;OACH;;;;;MAMD,qCAAc,GAAd;UACE,IAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;UACrC,OAAO,SAAS,CAAC,cAAc,EAAE,CAAC;OACnC;MACH,mBAAC;EAAD,CAAC,IAAA;EAED;;;;;;;AAOA,cAAe;;;;;;;;;;MAUb,MAAM,EAAE,UAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;UAC7B,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;cAC1B,OAAO,IAAI,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;WACzC;eAAM,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;cACjC,EAAE,GAAG,EAAE,CAAC;cACR,EAAE,GAAG,EAAE,CAAC;cACR,OAAO,IAAI,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;WACzC;eAAM;cACL,IAAI,YAAY,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;cAC9C,IAAI,CAAC,YAAY,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;kBAC3C,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjD,OAAO,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;eACxE;cACD,OAAO,YAAY,CAAC;WACrB;OACF;MAED,mBAAmB,EAAE;UACnB,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;UACnB,IAAI,GAAG,CAAC,iBAAiB,EAAE;cACzB,IAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;cAC1C,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,KAAK,CAAC,EAAE;kBAC5C,OAAO,IAAI,CAAC;eACb;mBAAM,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;;;kBAG3C,OAAO,IAAI,CAAC;eACb;cAED,IAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;cAC1C,EAAE,GAAG,SAAS,CAAC,cAAc,CAAC;cAC9B,EAAE,GAAG,SAAS,CAAC,WAAW,CAAC;cAC3B,EAAE,GAAG,SAAS,CAAC,YAAY,CAAC;cAC5B,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC;WAC1B;eAAM;cACL,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;cACnD,IAAM,YAAY,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;cAC3C,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;cAC7B,IAAM,cAAc,GAAG,SAAS,CAAC;cACjC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;cAE9B,IAAI,UAAU,GAAG,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;cACxD,IAAI,QAAQ,GAAG,gBAAgB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;;cAGrD,IAAI,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,eAAe,CAAC,UAAU,CAAC;kBAC9D,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,gBAAgB,CAAC,QAAQ,CAAC;kBAC/D,QAAQ,CAAC,IAAI,CAAC,WAAW,KAAK,UAAU,CAAC,IAAI,EAAE;kBACjD,UAAU,GAAG,QAAQ,CAAC;eACvB;cAED,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC;cACrB,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC;cACvB,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC;cACnB,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC;WACtB;UAED,OAAO,IAAI,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;OACzC;;;;;;;;;MAUD,cAAc,EAAE,UAAS,IAAI;UAC3B,IAAI,EAAE,GAAG,IAAI,CAAC;UACd,IAAI,EAAE,GAAG,CAAC,CAAC;UACX,IAAI,EAAE,GAAG,IAAI,CAAC;UACd,IAAI,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;;UAG5B,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;cAClB,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;cACjC,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;WACpB;UACD,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;cAChB,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;cACjC,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;WACpB;eAAM,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;cACzB,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;cAC7B,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;WACpB;UAED,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;OACpC;;;;;;;MAQD,oBAAoB,EAAE,UAAS,IAAI;UACjC,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;OACjD;;;;;;;MAQD,mBAAmB,EAAE,UAAS,IAAI;UAChC,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;OAC7C;;;;;;;;;;MAWD,kBAAkB,EAAE,UAAS,QAAQ,EAAE,QAAQ;UAC7C,IAAM,EAAE,GAAG,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;UACzD,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;UAC7B,IAAM,EAAE,GAAG,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;UACzD,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;UAC7B,OAAO,IAAI,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;OACzC;;;;;;;;;;MAWD,sBAAsB,EAAE,UAAS,QAAQ,EAAE,KAAK;UAC9C,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;UAC7B,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;UAC7B,IAAM,EAAE,GAAG,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;UAClE,IAAM,EAAE,GAAG,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;UAElE,OAAO,IAAI,YAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;OACzC;GACF,CAAC;;EC3xBF;;;;;;;;AAQA,WAAgB,iBAAiB,CAAC,IAAI;MACpC,OAAOA,GAAC,CAAC,QAAQ,CAAC,UAAC,QAAQ;UACzBA,GAAC,CAAC,MAAM,CAAC,IAAI,UAAU,EAAE,EAAE;cACzB,MAAM,EAAE,UAAC,CAAC;kBACR,IAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;kBAChC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;eAC3B;cACD,OAAO,EAAE,UAAC,GAAG;kBACX,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;eACtB;WACF,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;OACxB,CAAC,CAAC,OAAO,EAAE,CAAC;EACf,CAAC;EAED;;;;;;;;AAQA,WAAgB,WAAW,CAAC,GAAG;MAC7B,OAAOA,GAAC,CAAC,QAAQ,CAAC,UAAC,QAAQ;UACzB,IAAM,IAAI,GAAGA,GAAC,CAAC,OAAO,CAAC,CAAC;UAExB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;cACf,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;cACxB,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;WACxB,CAAC,CAAC,GAAG,CAAC,aAAa,EAAE;cACpB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;cAC1B,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;WACvB,CAAC,CAAC,GAAG,CAAC;cACL,OAAO,EAAE,MAAM;WAChB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;OAC7C,CAAC,CAAC,OAAO,EAAE,CAAC;EACf,CAAC;;EC5CD;MACE,iBAAY,SAAS;UACnB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;UAChB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;UACtB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;UAC3B,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;OAC9B;MAED,8BAAY,GAAZ;UACE,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;UACxC,IAAM,aAAa,GAAG,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;UAEjF,OAAO;cACL,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE;cAC/B,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC;WAC9D,CAAC;OACH;MAED,+BAAa,GAAb,UAAc,QAAQ;UACpB,IAAI,QAAQ,CAAC,QAAQ,KAAK,IAAI,EAAE;cAC9B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;WACxC;UACD,IAAI,QAAQ,CAAC,QAAQ,KAAK,IAAI,EAAE;cAC9B,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;WACrE;OACF;;;;;;MAOD,wBAAM,GAAN;;UAEE,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE;cACnE,IAAI,CAAC,UAAU,EAAE,CAAC;WACnB;;UAGD,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;;UAGrB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;OAClD;;;;;MAMD,wBAAM,GAAN;;UAEE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;;UAGhB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;;UAGtB,IAAI,CAAC,UAAU,EAAE,CAAC;OACnB;;;;;MAMD,uBAAK,GAAL;;UAEE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;;UAGhB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;;UAGtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;UAGxB,IAAI,CAAC,UAAU,EAAE,CAAC;OACnB;;;;MAKD,sBAAI,GAAJ;;UAEE,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE;cACnE,IAAI,CAAC,UAAU,EAAE,CAAC;WACnB;UAED,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;cACxB,IAAI,CAAC,WAAW,EAAE,CAAC;cACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;WAClD;OACF;;;;MAKD,sBAAI,GAAJ;UACE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE;cAC5C,IAAI,CAAC,WAAW,EAAE,CAAC;cACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;WAClD;OACF;;;;MAKD,4BAAU,GAAV;UACE,IAAI,CAAC,WAAW,EAAE,CAAC;;UAGnB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;cACxC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;WACpD;;UAGD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;OACtC;MACH,cAAC;EAAD,CAAC,IAAA;;ECjHD;MAAA;OAuJC;;;;;;;;;;;;;;MAzIC,yBAAS,GAAT,UAAU,IAAI,EAAE,aAAa;UAC3B,IAAI,GAAG,CAAC,aAAa,GAAG,GAAG,EAAE;cAC3B,IAAM,QAAM,GAAG,EAAE,CAAC;cAClBA,GAAC,CAAC,IAAI,CAAC,aAAa,EAAE,UAAC,GAAG,EAAE,YAAY;kBACtC,QAAM,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;eAC/C,CAAC,CAAC;cACH,OAAO,QAAM,CAAC;WACf;UACD,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;OAChC;;;;;;;MAQD,wBAAQ,GAAR,UAAS,KAAK;UACZ,IAAM,UAAU,GAAG,CAAC,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC;UAChG,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC;UAC1D,SAAS,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;UAC9D,OAAO,SAAS,CAAC;OAClB;;;;;;;MAQD,yBAAS,GAAT,UAAU,GAAG,EAAE,SAAS;UACtBA,GAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE;cAC3B,eAAe,EAAE,IAAI;WACtB,CAAC,EAAE,UAAC,GAAG,EAAE,IAAI;cACZA,GAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;WACxB,CAAC,CAAC;OACJ;;;;;;;;;;;MAYD,0BAAU,GAAV,UAAW,GAAG,EAAE,OAAO;UACrB,GAAG,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;UAEtB,IAAM,QAAQ,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,MAAM,CAAC;UACzD,IAAM,oBAAoB,GAAG,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,oBAAoB,CAAC,CAAC;UACzE,IAAM,mBAAmB,GAAG,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,mBAAmB,CAAC,CAAC;UAEvE,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE;cACrB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;WAC/C;UAED,IAAI,IAAI,GAAG,GAAG,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;UAC5C,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE;cAClC,aAAa,EAAE,IAAI;WACpB,CAAC,CAAC,GAAG,CAAC,UAAC,IAAI;cACV,OAAO,GAAG,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;WACxE,CAAC,CAAC;UAEH,IAAI,oBAAoB,EAAE;cACxB,IAAI,mBAAmB,EAAE;kBACvB,IAAM,cAAY,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;;kBAEjC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,UAAC,IAAI;sBACzB,OAAO,KAAK,CAAC,QAAQ,CAAC,cAAY,EAAE,IAAI,CAAC,CAAC;mBAC3C,CAAC,CAAC;eACJ;cAED,OAAO,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;kBACpB,IAAM,QAAQ,GAAG,GAAG,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;kBACrD,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;kBAClC,IAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;kBACnCA,GAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,GAAG,EAAE,IAAI;sBACtB,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;sBAC5C,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;mBAClB,CAAC,CAAC;kBACH,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;eAC7B,CAAC,CAAC;WACJ;eAAM;cACL,OAAO,KAAK,CAAC;WACd;OACF;;;;;;;MAQD,uBAAO,GAAP,UAAQ,GAAG;UACT,IAAM,KAAK,GAAGA,GAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,UAAU,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;UACrE,IAAI,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;;;UAIrC,IAAI;cACF,SAAS,GAAGA,GAAC,CAAC,MAAM,CAAC,SAAS,EAAE;kBAC9B,WAAW,EAAE,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,QAAQ;kBACnE,aAAa,EAAE,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,QAAQ;kBACzE,gBAAgB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC,GAAG,WAAW,GAAG,QAAQ;kBAClF,gBAAgB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC,GAAG,WAAW,GAAG,QAAQ;kBAClF,kBAAkB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,aAAa,CAAC,GAAG,aAAa,GAAG,QAAQ;kBACxF,oBAAoB,EAAE,QAAQ,CAAC,iBAAiB,CAAC,eAAe,CAAC,GAAG,eAAe,GAAG,QAAQ;kBAC9F,aAAa,EAAE,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC,aAAa,CAAC;eAClF,CAAC,CAAC;WACJ;UAAC,OAAO,CAAC,EAAE,GAAE;;UAGd,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE;cACnB,SAAS,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;WAClC;eAAM;cACL,IAAM,YAAY,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,mBAAmB,EAAE,QAAQ,CAAC,CAAC;cACvE,IAAM,WAAW,GAAGA,GAAC,CAAC,OAAO,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;cAC/E,SAAS,CAAC,YAAY,CAAC,GAAG,WAAW,GAAG,WAAW,GAAG,SAAS,CAAC;WACjE;UAED,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;UAC9C,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;cACrC,SAAS,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;WAClD;eAAM;cACL,IAAM,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;cACjG,SAAS,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;WAClD;UAED,SAAS,CAAC,MAAM,GAAG,GAAG,CAAC,UAAU,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;UAC1E,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;UAC/D,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC;UAEtB,OAAO,SAAS,CAAC;OAClB;MACH,YAAC;EAAD,CAAC,IAAA;;ECvJD;MAAA;OAuRC;;;;MAnRC,kCAAiB,GAAjB,UAAkB,QAAQ;UACxB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;OACjC;;;;MAKD,oCAAmB,GAAnB,UAAoB,QAAQ;UAC1B,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;OACjC;;;;MAKD,uBAAM,GAAN,UAAO,QAAQ;UAAf,iBA6BC;UA5BC,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,sBAAsB,EAAE,CAAC;UAE5D,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;UAC/D,IAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;UAEnEA,GAAC,CAAC,IAAI,CAAC,UAAU,EAAE,UAAC,GAAG,EAAE,KAAK;cAC5B,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;cAC/B,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;kBAClB,IAAM,cAAY,GAAG,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;kBACzD,IAAI,cAAY,EAAE;sBAChB,KAAK;2BACF,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,cAAY,CAAC,WAAW,CAAC,IAAI,CAAC,GAAA,CAAC,CAAC;mBAChD;uBAAM;sBACL,KAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;sBAC/C,KAAK;2BACF,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,UAAU,GAAA,CAAC;2BAC9B,GAAG,CAAC,UAAC,IAAI,IAAK,OAAA,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAA,CAAC,CAAC;mBAC/C;eACF;mBAAM;kBACLA,GAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,GAAG,EAAE,IAAI;sBACtBA,GAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE,UAAC,GAAG,EAAE,GAAG;0BACjC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;uBACtC,CAAC,CAAC;mBACJ,CAAC,CAAC;eACJ;WACF,CAAC,CAAC;UAEH,GAAG,CAAC,MAAM,EAAE,CAAC;OACd;;;;MAKD,wBAAO,GAAP,UAAQ,QAAQ;UAAhB,iBAqBC;UApBC,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,sBAAsB,EAAE,CAAC;UAE5D,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;UAC/D,IAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;UAEnEA,GAAC,CAAC,IAAI,CAAC,UAAU,EAAE,UAAC,GAAG,EAAE,KAAK;cAC5B,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;cAC/B,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;kBAClB,KAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;eAC3B;mBAAM;kBACLA,GAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,GAAG,EAAE,IAAI;sBACtBA,GAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE,UAAC,GAAG,EAAE,GAAG;0BACjC,GAAG,IAAI,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;0BAC/B,OAAO,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;uBACjC,CAAC,CAAC;mBACJ,CAAC,CAAC;eACJ;WACF,CAAC,CAAC;UAEH,GAAG,CAAC,MAAM,EAAE,CAAC;OACd;;;;;;MAOD,2BAAU,GAAV,UAAW,QAAQ,EAAE,QAAQ;UAA7B,iBAgCC;UA/BC,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,sBAAsB,EAAE,CAAC;UAE5D,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC;UAC7D,IAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;UACzC,IAAM,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;;UAGnE,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,EAAE;cACrC,IAAI,cAAY,GAAG,EAAE,CAAC;cACtBA,GAAC,CAAC,IAAI,CAAC,UAAU,EAAE,UAAC,GAAG,EAAE,KAAK;kBAC5B,cAAY,GAAG,cAAY,CAAC,MAAM,CAAC,KAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;eACpE,CAAC,CAAC;cACH,KAAK,GAAG,cAAY,CAAC;;WAEtB;eAAM;cACL,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE;kBACtC,eAAe,EAAE,IAAI;eACtB,CAAC,CAAC,MAAM,CAAC,UAAC,QAAQ;kBACjB,OAAO,CAACA,GAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;eACxC,CAAC,CAAC;cAEH,IAAI,SAAS,CAAC,MAAM,EAAE;kBACpBA,GAAC,CAAC,IAAI,CAAC,SAAS,EAAE,UAAC,GAAG,EAAE,QAAQ;sBAC9B,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;mBACjC,CAAC,CAAC;eACJ;mBAAM;kBACL,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;eAC5C;WACF;UAED,KAAK,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;OACxD;;;;;;MAOD,yBAAQ,GAAR,UAAS,KAAK,EAAE,QAAQ;UACtB,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;UAC/B,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;UAE/B,IAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC;UAC1E,IAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC;UAElE,IAAM,QAAQ,GAAG,QAAQ,IAAI,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;;UAGjF,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;cACrB,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;WAC9D,CAAC,CAAC;;UAGH,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;UAEtC,IAAI,QAAQ,EAAE;cACZ,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;cAChE,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;WACtB;UAED,OAAO,KAAK,CAAC;OACd;;;;;;;;MASD,4BAAW,GAAX,UAAY,UAAU,EAAE,eAAe;UAAvC,iBAiFC;UAhFC,IAAI,aAAa,GAAG,EAAE,CAAC;UAEvBA,GAAC,CAAC,IAAI,CAAC,UAAU,EAAE,UAAC,GAAG,EAAE,KAAK;cAC5B,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;cAC/B,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;cAE/B,IAAM,QAAQ,GAAG,eAAe,GAAG,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;cACxF,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;cAEvC,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,KAAK,IAAI,EAAE;kBACzC,KAAK,CAAC,GAAG,CAAC,UAAA,IAAI;sBACZ,IAAM,OAAO,GAAG,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;sBAE5C,IAAI,UAAU,CAAC,WAAW,EAAE;0BAC1B,UAAU,CAAC,UAAU,CAAC,YAAY,CAChC,IAAI,EACJ,UAAU,CAAC,WAAW,CACvB,CAAC;uBACH;2BAAM;0BACL,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;uBACzC;sBAED,IAAI,OAAO,CAAC,MAAM,EAAE;0BAClB,KAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;0BAC1C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;uBACzC;mBACF,CAAC,CAAC;kBAEH,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;sBAClC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;mBAClC;kBAED,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;sBACtC,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;mBAC/C;eACF;mBAAM;kBACL,IAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE;sBACxE,IAAI,EAAE,IAAI,CAAC,UAAU;sBACrB,MAAM,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;mBAC/B,EAAE;sBACD,sBAAsB,EAAE,IAAI;mBAC7B,CAAC,GAAG,IAAI,CAAC;kBAEV,IAAM,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE;sBACzC,IAAI,EAAE,IAAI,CAAC,UAAU;sBACrB,MAAM,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;mBAC3B,EAAE;sBACD,sBAAsB,EAAE,IAAI;mBAC7B,CAAC,CAAC;kBAEH,KAAK,GAAG,eAAe,GAAG,GAAG,CAAC,cAAc,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC;wBAC9D,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;kBAGvD,IAAI,eAAe,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;sBACvD,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;0BACrB,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;uBAC/B,CAAC,CAAC;mBACJ;kBAEDA,GAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE,UAAC,GAAG,EAAE,IAAI;sBAC5C,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;mBACjC,CAAC,CAAC;;kBAGH,IAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;kBAClEA,GAAC,CAAC,IAAI,CAAC,SAAS,EAAE,UAAC,GAAG,EAAE,QAAQ;sBAC9B,IAAM,SAAS,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;sBAC9EA,GAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,UAAC,GAAG,EAAE,QAAQ;0BACxC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;8BAC7B,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;2BAC5B;uBACF,CAAC,CAAC;mBACJ,CAAC,CAAC;eACJ;cAED,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;WAC7C,CAAC,CAAC;UAEH,OAAO,aAAa,CAAC;OACtB;;;;;;;;;;MAWD,iCAAgB,GAAhB,UAAiB,IAAI;UACnB,OAAO,IAAI,CAAC,eAAe;gBACvB,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,CAAC;gBAClD,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;OACjC;;;;;;;;;MAUD,yBAAQ,GAAR,UAAS,IAAI;UACX,OAAO,IAAI;gBACP,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAA,KAAK,IAAI,OAAA,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAA,CAAC;gBAC7E,IAAI,CAAC;OACV;;;;;;;;;MAUD,iCAAgB,GAAhB,UAAiB,IAAI;UACnB,IAAM,QAAQ,GAAG,EAAE,CAAC;UACpB,OAAO,IAAI,CAAC,WAAW,EAAE;cACvB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;cAChC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;WACzB;UACD,OAAO,QAAQ,CAAC;OACjB;MACH,aAAC;EAAD,CAAC,IAAA;;ECxRD;;;;;;EAMA;MACE,gBAAY,OAAO;;UAEjB,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;UAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;OAChC;;;;;;;MAQD,0BAAS,GAAT,UAAU,GAAG,EAAE,OAAO;UACpB,IAAM,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;UACvE,GAAG,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;UAC3B,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;UAE1B,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;UACjC,GAAG,CAAC,MAAM,EAAE,CAAC;OACd;;;;;;;;;;;;MAaD,gCAAe,GAAf,UAAgB,QAAQ,EAAE,GAAG;UAC3B,GAAG,GAAG,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;;UAGpC,GAAG,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;;UAG3B,GAAG,GAAG,GAAG,CAAC,sBAAsB,EAAE,CAAC;;UAGnC,IAAM,SAAS,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;UAEnD,IAAI,QAAQ,CAAC;;UAEb,IAAI,SAAS,EAAE;;cAEb,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;;kBAEjD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;kBACtD,OAAO;eACR;mBAAM;kBACL,IAAI,UAAU,GAAG,IAAI,CAAC;kBACtB,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,KAAK,CAAC,EAAE;sBAC9C,UAAU,GAAG,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC;mBACxD;uBAAM,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,KAAK,CAAC,EAAE;sBACrD,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,SAAS,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC;mBAC5D;kBAED,IAAI,UAAU,EAAE;;sBAEd,QAAQ,GAAGA,GAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;;;sBAG/B,IAAI,GAAG,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE;0BAC7EA,GAAC,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE,CAAC;uBAChC;sBACD,IAAM,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,GAAG,CAAC,aAAa,EAAE,EAAE,EAAE,oBAAoB,EAAE,IAAI,EAAE,CAAC,CAAC;sBAC7F,IAAI,KAAK,EAAE;0BACT,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;uBAChD;2BAAM;0BACL,GAAG,CAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;uBACvC;mBACF;uBAAM;sBACL,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,aAAa,EAAE,CAAC,CAAC;;sBAGzD,IAAI,YAAY,GAAG,GAAG,CAAC,cAAc,CAAC,SAAS,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC;sBACpE,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;sBAEpFA,GAAC,CAAC,IAAI,CAAC,YAAY,EAAE,UAAC,GAAG,EAAE,MAAM;0BAC/B,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;uBACpB,CAAC,CAAC;;sBAGH,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;0BAC/G,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;uBACvC;mBACF;eACF;;WAEF;eAAM;cACL,IAAM,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;cACvC,QAAQ,GAAGA,GAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/B,IAAI,IAAI,EAAE;kBACR,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;eACrC;mBAAM;kBACL,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;eAC9B;WACF;UAED,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;OACzE;MACH,aAAC;EAAD,CAAC,IAAA;;EC/GD;;;;;;;EAOA,IAAM,iBAAiB,GAAG,UAAS,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ;MACpE,IAAM,WAAW,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;MACjD,IAAM,aAAa,GAAG,EAAE,CAAC;MACzB,IAAM,eAAe,GAAG,EAAE,CAAC;;;;;;;MAS3B,SAAS,aAAa;UACpB,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,IAAI,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,EAAE;cAClI,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,UAAU,CAAC,CAAC;cACtE,OAAO;WACR;UACD,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC;UAC1C,IAAI,CAAC,UAAU,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,IAAI,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;cAC7H,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,UAAU,CAAC,CAAC;cACrE,OAAO;WACR;UACD,WAAW,CAAC,MAAM,GAAG,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC;OACxD;;;;;;;;;;MAWD,SAAS,uBAAuB,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa;UAC1G,IAAM,WAAW,GAAG;cAClB,SAAS,EAAE,OAAO;cAClB,UAAU,EAAE,QAAQ;cACpB,WAAW,EAAE,SAAS;cACtB,WAAW,EAAE,SAAS;cACtB,WAAW,EAAE,aAAa;WAC3B,CAAC;UACF,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;cAC5B,aAAa,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;WAC9B;UACD,aAAa,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC;OAClD;;;;;;;MAQD,SAAS,aAAa,CAAC,mBAAmB,EAAE,YAAY,EAAE,kBAAkB,EAAE,kBAAkB;UAC9F,OAAO;cACL,UAAU,EAAE,mBAAmB,CAAC,QAAQ;cACxC,QAAQ,EAAE,YAAY;cACtB,cAAc,EAAE;kBACd,UAAU,EAAE,kBAAkB;kBAC9B,WAAW,EAAE,kBAAkB;eAChC;WACF,CAAC;OACH;;;;;;;MAQD,SAAS,gBAAgB,CAAC,QAAQ,EAAE,SAAS;UAC3C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;cAC5B,OAAO,SAAS,CAAC;WAClB;UACD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;cACvC,OAAO,SAAS,CAAC;WAClB;UAED,IAAI,YAAY,GAAG,SAAS,CAAC;UAC7B,OAAO,aAAa,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;cAC5C,YAAY,EAAE,CAAC;cACf,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,YAAY,CAAC,EAAE;kBAC1C,OAAO,YAAY,CAAC;eACrB;WACF;OACF;;;;;;;MAQD,SAAS,oBAAoB,CAAC,GAAG,EAAE,IAAI;UACrC,IAAM,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;UACjE,IAAM,cAAc,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;UAC1C,IAAM,cAAc,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;UAC1C,IAAM,kBAAkB,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC;UAC1G,uBAAuB,CAAC,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,cAAc,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;;UAGnG,IAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;UAChG,IAAI,aAAa,GAAG,CAAC,EAAE;cACrB,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,aAAa,EAAE,EAAE,EAAE,EAAE;kBACzC,IAAM,YAAY,GAAG,GAAG,CAAC,QAAQ,GAAG,EAAE,CAAC;kBACvC,gBAAgB,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;kBACpE,uBAAuB,CAAC,YAAY,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;eACzF;WACF;;UAGD,IAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;UAChG,IAAI,aAAa,GAAG,CAAC,EAAE;cACrB,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,aAAa,EAAE,EAAE,EAAE,EAAE;kBACzC,IAAM,aAAa,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,GAAG,SAAS,GAAG,EAAE,EAAE,CAAC;kBACvE,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,EAAE,IAAI,EAAE,kBAAkB,CAAC,CAAC;kBACxE,uBAAuB,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,EAAE,GAAG,EAAE,IAAI,EAAE,cAAc,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;eAC7F;WACF;OACF;;;;;;;;;MAUD,SAAS,gBAAgB,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,EAAE,cAAc;UACjE,IAAI,QAAQ,KAAK,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI,CAAC,cAAc,EAAE;cAC7H,WAAW,CAAC,MAAM,EAAE,CAAC;WACtB;OACF;;;;MAKD,SAAS,kBAAkB;UACzB,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;UAC3B,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;cACzD,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;cACnC,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;kBAC7D,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;eACxD;WACF;OACF;;;;;;MAOD,SAAS,2BAA2B,CAAC,IAAI;UACvC,QAAQ,KAAK;cACX,KAAK,iBAAiB,CAAC,KAAK,CAAC,MAAM;kBACjC,IAAI,IAAI,CAAC,SAAS,EAAE;sBAClB,OAAO,iBAAiB,CAAC,YAAY,CAAC,iBAAiB,CAAC;mBACzD;kBACD,MAAM;cACR,KAAK,iBAAiB,CAAC,KAAK,CAAC,GAAG;kBAC9B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;sBACrC,OAAO,iBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC;mBAC/C;uBAAM,IAAI,IAAI,CAAC,SAAS,EAAE;sBACzB,OAAO,iBAAiB,CAAC,YAAY,CAAC,iBAAiB,CAAC;mBACzD;kBACD,MAAM;WACT;UACD,OAAO,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC;OAClD;;;;;;MAOD,SAAS,wBAAwB,CAAC,IAAI;UACpC,QAAQ,KAAK;cACX,KAAK,iBAAiB,CAAC,KAAK,CAAC,MAAM;kBACjC,IAAI,IAAI,CAAC,SAAS,EAAE;sBAClB,OAAO,iBAAiB,CAAC,YAAY,CAAC,YAAY,CAAC;mBACpD;uBAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;sBAC3C,OAAO,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC;mBAC9C;kBACD,MAAM;cACR,KAAK,iBAAiB,CAAC,KAAK,CAAC,GAAG;kBAC9B,IAAI,IAAI,CAAC,SAAS,EAAE;sBAClB,OAAO,iBAAiB,CAAC,YAAY,CAAC,YAAY,CAAC;mBACpD;uBAAM,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;sBAC3C,OAAO,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC;mBAC9C;kBACD,MAAM;WACT;UACD,OAAO,iBAAiB,CAAC,YAAY,CAAC,OAAO,CAAC;OAC/C;MAED,SAAS,IAAI;UACX,aAAa,EAAE,CAAC;UAChB,kBAAkB,EAAE,CAAC;OACtB;;;;;;;MASD,IAAI,CAAC,aAAa,GAAG;UACnB,IAAM,QAAQ,GAAG,CAAC,KAAK,KAAK,iBAAiB,CAAC,KAAK,CAAC,GAAG,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;UACnF,IAAM,QAAQ,GAAG,CAAC,KAAK,KAAK,iBAAiB,CAAC,KAAK,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;UAEtF,IAAI,cAAc,GAAG,CAAC,CAAC;UACvB,IAAI,WAAW,GAAG,IAAI,CAAC;UACvB,OAAO,WAAW,EAAE;cAClB,IAAM,WAAW,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,QAAQ,GAAG,cAAc,CAAC;cAChE,IAAM,WAAW,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,QAAQ,GAAG,cAAc,CAAC;cAChE,IAAM,GAAG,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;cACvC,IAAI,CAAC,GAAG,EAAE;kBACR,WAAW,GAAG,KAAK,CAAC;kBACpB,OAAO,eAAe,CAAC;eACxB;cACD,IAAM,IAAI,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC;cAC9B,IAAI,CAAC,IAAI,EAAE;kBACT,WAAW,GAAG,KAAK,CAAC;kBACpB,OAAO,eAAe,CAAC;eACxB;;cAGD,IAAI,YAAY,GAAG,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC;cACzD,QAAQ,MAAM;kBACZ,KAAK,iBAAiB,CAAC,aAAa,CAAC,GAAG;sBACtC,YAAY,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAC;sBAC9C,MAAM;kBACR,KAAK,iBAAiB,CAAC,aAAa,CAAC,MAAM;sBACzC,YAAY,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC;sBACjD,MAAM;eACT;cACD,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC,CAAC;cAClF,cAAc,EAAE,CAAC;WAClB;UAED,OAAO,eAAe,CAAC;OACxB,CAAC;MAEF,IAAI,EAAE,CAAC;EACT,CAAC,CAAC;EACF;;;;EAIA,iBAAiB,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;EACpD;;;;EAIA,iBAAiB,CAAC,aAAa,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;EAC5D;;;;EAIA,iBAAiB,CAAC,YAAY,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,mBAAmB,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;EAE3H;;;;;;;EAOA;MAAA;OAkSC;;;;;;;MA3RC,mBAAG,GAAH,UAAI,GAAG,EAAE,OAAO;UACd,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;UAC5D,IAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;UAC9C,IAAM,KAAK,GAAG,GAAG,CAAC,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;UAEpD,IAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;UAC/D,IAAI,QAAQ,EAAE;cACZ,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;WACpC;OACF;;;;;;;;MASD,sBAAM,GAAN,UAAO,GAAG,EAAE,QAAQ;UAClB,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;UAE5D,IAAM,SAAS,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;UACxC,IAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;UACvD,IAAM,IAAI,GAAGA,GAAC,CAAC,KAAK,GAAG,YAAY,GAAG,QAAQ,CAAC,CAAC;UAEhD,IAAM,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,KAAK,CAAC,GAAG,EACpE,iBAAiB,CAAC,aAAa,CAAC,GAAG,EAAEA,GAAC,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzE,IAAM,OAAO,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;UAEvC,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;cACtD,IAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;cACpC,IAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;cAClE,QAAQ,WAAW,CAAC,MAAM;kBACxB,KAAK,iBAAiB,CAAC,YAAY,CAAC,OAAO;sBACzC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;sBAC9D,MAAM;kBACR,KAAK,iBAAiB,CAAC,YAAY,CAAC,YAAY;sBAC9C,IAAI,QAAQ,KAAK,KAAK,EAAE;0BACtB,IAAM,UAAU,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;0BAC/C,IAAM,gBAAgB,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;0BAClH,IAAI,gBAAgB,EAAE;8BACpB,IAAM,KAAK,GAAGA,GAAC,CAAC,aAAa,CAAC,CAAC,MAAM,CAACA,GAAC,CAAC,KAAK,GAAG,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;8BACxH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;8BACnB,MAAM;2BACP;uBACF;sBACD,IAAI,aAAa,GAAG,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;sBAC/D,aAAa,EAAE,CAAC;sBAChB,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;sBAC5D,MAAM;eACT;WACF;UAED,IAAI,QAAQ,KAAK,KAAK,EAAE;cACtB,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;WACxB;eAAM;cACL,IAAM,cAAc,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;cAC1C,IAAI,cAAc,EAAE;kBAClB,IAAM,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;kBAC/DA,GAAC,CAACA,GAAC,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC;kBAChE,OAAO;eACR;cACD,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;WACvB;OACF;;;;;;;;MASD,sBAAM,GAAN,UAAO,GAAG,EAAE,QAAQ;UAClB,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;UAC5D,IAAM,GAAG,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;UAClC,IAAM,SAAS,GAAGA,GAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;UACpC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;UAEpB,IAAM,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,KAAK,CAAC,MAAM,EACvE,iBAAiB,CAAC,aAAa,CAAC,GAAG,EAAEA,GAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnE,IAAM,OAAO,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;UAEvC,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;cACrE,IAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;cACzC,IAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;cAClE,QAAQ,WAAW,CAAC,MAAM;kBACxB,KAAK,iBAAiB,CAAC,YAAY,CAAC,OAAO;sBACzC,IAAI,QAAQ,KAAK,OAAO,EAAE;0BACxBA,GAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;uBACjF;2BAAM;0BACLA,GAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;uBAClF;sBACD,MAAM;kBACR,KAAK,iBAAiB,CAAC,YAAY,CAAC,YAAY;sBAC9C,IAAI,QAAQ,KAAK,OAAO,EAAE;0BACxB,IAAI,aAAa,GAAG,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;0BAC/D,aAAa,EAAE,CAAC;0BAChB,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;uBAC7D;2BAAM;0BACLA,GAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;uBAClF;sBACD,MAAM;eACT;WACF;OACF;;;;;;;MAQD,iCAAiB,GAAjB,UAAkB,EAAE;UAClB,IAAI,SAAS,GAAG,EAAE,CAAC;UAEnB,IAAI,CAAC,EAAE,EAAE;cACP,OAAO,SAAS,CAAC;WAClB;UAED,IAAM,QAAQ,GAAG,EAAE,CAAC,UAAU,IAAI,EAAE,CAAC;UAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;cACxC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;kBAC3C,SAAS;eACV;cAED,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE;kBACzB,SAAS,IAAI,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC;eACxE;WACF;UAED,OAAO,SAAS,CAAC;OAClB;;;;;;;MAQD,yBAAS,GAAT,UAAU,GAAG;UACX,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;UAC5D,IAAM,GAAG,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;UAClC,IAAM,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC;UACtD,IAAM,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;UAE/B,IAAM,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,KAAK,CAAC,GAAG,EACpE,iBAAiB,CAAC,aAAa,CAAC,MAAM,EAAEA,GAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtE,IAAM,OAAO,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;UAEvC,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;cACrE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;kBACzB,SAAS;eACV;cAED,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC;cAC/C,IAAM,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC;cAC1D,IAAM,UAAU,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;cAC9D,IAAI,aAAa,GAAG,CAAC,UAAU,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;cACtE,QAAQ,OAAO,CAAC,WAAW,CAAC,CAAC,MAAM;kBACjC,KAAK,iBAAiB,CAAC,YAAY,CAAC,MAAM;sBACxC,SAAS;kBACX,KAAK,iBAAiB,CAAC,YAAY,CAAC,OAAO;sBACzC,IAAM,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;sBAClC,IAAI,CAAC,OAAO,EAAE;0BAAE,SAAS;uBAAE;sBAC3B,IAAM,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;sBACvC,IAAI,UAAU,EAAE;0BACd,IAAI,aAAa,GAAG,CAAC,EAAE;8BACrB,aAAa,EAAE,CAAC;8BAChB,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;8BACvD,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;8BAC9D,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC;2BACvC;+BAAM,IAAI,aAAa,KAAK,CAAC,EAAE;8BAC9B,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;8BACvD,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;8BAClD,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC;2BACvC;uBACF;sBACD,SAAS;kBACX,KAAK,iBAAiB,CAAC,YAAY,CAAC,iBAAiB;sBACnD,IAAI,UAAU,EAAE;0BACd,IAAI,aAAa,GAAG,CAAC,EAAE;8BACrB,aAAa,EAAE,CAAC;8BAChB,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;8BAChD,IAAI,eAAe,CAAC,QAAQ,KAAK,MAAM,IAAI,QAAQ,CAAC,SAAS,KAAK,OAAO,EAAE;kCAAE,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;+BAAE;2BACxG;+BAAM,IAAI,aAAa,KAAK,CAAC,EAAE;8BAC9B,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;8BACpC,IAAI,eAAe,CAAC,QAAQ,KAAK,MAAM,IAAI,QAAQ,CAAC,SAAS,KAAK,OAAO,EAAE;kCAAE,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;+BAAE;2BACxG;uBACF;sBACD,SAAS;kBACX,KAAK,iBAAiB,CAAC,YAAY,CAAC,UAAU;;sBAE5C,SAAS;eACZ;WACF;UACD,GAAG,CAAC,MAAM,EAAE,CAAC;OACd;;;;;;;MAQD,yBAAS,GAAT,UAAU,GAAG;UACX,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;UAC5D,IAAM,GAAG,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;UAClC,IAAM,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,KAAK,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,CAAC;UAEtD,IAAM,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,KAAK,CAAC,MAAM,EACvE,iBAAiB,CAAC,aAAa,CAAC,MAAM,EAAEA,GAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtE,IAAM,OAAO,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;UAEvC,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;cACrE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;kBACzB,SAAS;eACV;cACD,QAAQ,OAAO,CAAC,WAAW,CAAC,CAAC,MAAM;kBACjC,KAAK,iBAAiB,CAAC,YAAY,CAAC,MAAM;sBACxC,SAAS;kBACX,KAAK,iBAAiB,CAAC,YAAY,CAAC,iBAAiB;sBACnD,IAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC;sBAC/C,IAAM,UAAU,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;sBAC9D,IAAI,UAAU,EAAE;0BACd,IAAI,aAAa,GAAG,CAAC,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;0BAC5E,IAAI,aAAa,GAAG,CAAC,EAAE;8BACrB,aAAa,EAAE,CAAC;8BAChB,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;8BAChD,IAAI,QAAQ,CAAC,SAAS,KAAK,OAAO,EAAE;kCAAE,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;+BAAE;2BACjE;+BAAM,IAAI,aAAa,KAAK,CAAC,EAAE;8BAC9B,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;8BACpC,IAAI,QAAQ,CAAC,SAAS,KAAK,OAAO,EAAE;kCAAE,QAAQ,CAAC,SAAS,GAAG,EAAE,CAAC;+BAAE;2BACjE;uBACF;sBACD,SAAS;kBACX,KAAK,iBAAiB,CAAC,YAAY,CAAC,UAAU;sBAC5C,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;sBAChD,SAAS;eACZ;WACF;OACF;;;;;;;;MASD,2BAAW,GAAX,UAAY,QAAQ,EAAE,QAAQ,EAAE,OAAO;UACrC,IAAM,GAAG,GAAG,EAAE,CAAC;UACf,IAAI,MAAM,CAAC;UACX,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,MAAM,EAAE,EAAE;cAChD,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,CAAC;WACxC;UACD,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;UAEtB,IAAM,GAAG,GAAG,EAAE,CAAC;UACf,IAAI,MAAM,CAAC;UACX,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,QAAQ,EAAE,MAAM,EAAE,EAAE;cAChD,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,CAAC;WACrC;UACD,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;UACtB,IAAM,MAAM,GAAGA,GAAC,CAAC,SAAS,GAAG,MAAM,GAAG,UAAU,CAAC,CAAC;UAClD,IAAI,OAAO,IAAI,OAAO,CAAC,cAAc,EAAE;cACrC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;WACzC;UAED,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;OAClB;;;;;;;MAQD,2BAAW,GAAX,UAAY,GAAG;UACb,IAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;UAC5DA,GAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;OACnC;MACH,YAAC;EAAD,CAAC,IAAA;;EC/iBD,IAAM,SAAS,GAAG,OAAO,CAAC;EAE1B;;;EAGA;MACE,gBAAY,OAAO;UAAnB,iBA0SC;UAzSC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UAEvB,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;UACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;UACzC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;UAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;UAElC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;UAClC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;UAEtB,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;UACzB,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;UACzB,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;UAClC,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;UAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;UAE3C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;UACpD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;UACpD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;UAClD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;UACtD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;UAC1E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;UAC9E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;UAClF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;UACxD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;UAC1D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;UAChE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2BAA2B,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;UACpF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;UAG5D,IAAM,QAAQ,GAAG;cACf,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,aAAa,EAAE,WAAW;cAC1E,aAAa,EAAE,eAAe,EAAE,cAAc,EAAE,aAAa;cAC7D,aAAa,EAAE,cAAc,EAAE,WAAW;WAC3C,CAAC;UAEF,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;cACzD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAC,IAAI;kBAC1B,OAAO,UAAC,KAAK;sBACX,KAAI,CAAC,aAAa,EAAE,CAAC;sBACrB,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;sBACzC,KAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;mBACzB,CAAC;eACH,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;cAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;WAC3E;UAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,KAAK;cACrC,OAAO,KAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;WAC7D,CAAC,CAAC;UAEH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,KAAK;cACrC,OAAO,KAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC;WACpD,CAAC,CAAC;UAEH,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;cACjC,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,UAAC,GAAG;kBAC3B,OAAO;sBACL,KAAI,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;mBAC7B,CAAC;eACH,EAAE,GAAG,CAAC,CAAC;cACR,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC;WAC1E;UAED,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC;cACtC,KAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;WAC5C,CAAC,CAAC;UAEH,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC;cACxC,KAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;WAC9C,CAAC,CAAC;UAEH,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC;cAC1C,KAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;WAChD,CAAC,CAAC;UAEH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;cAC7B,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;WACnC,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;cAC9B,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;WACpC,CAAC,CAAC;;;;;;UAOH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,IAAI;cACtC,IAAI,KAAI,CAAC,SAAS,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE;kBACzC,OAAO;eACR;cACD,IAAM,GAAG,GAAG,KAAI,CAAC,WAAW,EAAE,CAAC;cAC/B,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;cACrB,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;WAC1C,CAAC,CAAC;;;;;UAMH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,IAAI;cACtC,IAAI,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;kBAC/B,OAAO;eACR;cACD,IAAM,GAAG,GAAG,KAAI,CAAC,WAAW,EAAE,CAAC;cAC/B,IAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;cACtD,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;WAC3D,CAAC,CAAC;;;;;UAKH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,MAAM;cACvC,IAAI,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;kBACjC,OAAO;eACR;cACD,IAAM,QAAQ,GAAG,KAAI,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;cACtD,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;WAC1D,CAAC,CAAC;;;;;;UAOH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,OAAO,EAAE,OAAO;cACnD,IAAM,kBAAkB,GAAG,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,kBAAkB,CAAC;cACrE,IAAI,kBAAkB,EAAE;kBACtB,kBAAkB,CAAC,IAAI,CAAC,KAAI,EAAE,OAAO,EAAE,KAAI,CAAC,OAAO,EAAE,KAAI,CAAC,aAAa,CAAC,CAAC;eAC1E;mBAAM;kBACL,KAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;eACtC;WACF,CAAC,CAAC;;;;UAKH,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC;cAC3C,IAAM,MAAM,GAAG,KAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;cAC/D,IAAI,MAAM,CAAC,WAAW,EAAE;kBACtB,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,CAAC;eAC1D;WACF,CAAC,CAAC;;;;;UAMH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,KAAK;cACvC,KAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAI,CAAC,WAAW,EAAE,EAAE;kBACvC,UAAU,EAAE,KAAK;eAClB,CAAC,CAAC;WACJ,CAAC,CAAC;;;;;;UAOH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,QAAQ;cAC1C,IAAI,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC;cAC3B,IAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC;cAC/B,IAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;cACzC,IAAI,GAAG,GAAG,QAAQ,CAAC,KAAK,IAAI,KAAI,CAAC,WAAW,EAAE,CAAC;cAC/C,IAAM,oBAAoB,GAAG,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC;cACrE,IAAI,oBAAoB,GAAG,CAAC,IAAI,KAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE;kBACpE,OAAO;eACR;cACD,IAAM,aAAa,GAAG,GAAG,CAAC,QAAQ,EAAE,KAAK,QAAQ,CAAC;;cAGlD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;kBAC/B,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;eAC1B;cAED,IAAI,KAAI,CAAC,OAAO,CAAC,YAAY,EAAE;kBAC7B,OAAO,GAAG,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;eAC9C;mBAAM;;kBAEL,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;;sBAE/B,OAAO,GAAG,mCAAmC,CAAC,IAAI,CAAC,OAAO,CAAC;4BACvD,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;mBACnC;eACF;cAED,IAAI,OAAO,GAAG,EAAE,CAAC;cACjB,IAAI,aAAa,EAAE;kBACjB,GAAG,GAAG,GAAG,CAAC,cAAc,EAAE,CAAC;kBAC3B,IAAM,MAAM,GAAG,GAAG,CAAC,UAAU,CAACA,GAAC,CAAC,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAC/D,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;eACtB;mBAAM;kBACL,OAAO,GAAG,KAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE;sBACnC,QAAQ,EAAE,GAAG;sBACb,oBAAoB,EAAE,IAAI;sBAC1B,mBAAmB,EAAE,IAAI;mBAC1B,CAAC,CAAC;eACJ;cAEDA,GAAC,CAAC,IAAI,CAAC,OAAO,EAAE,UAAC,GAAG,EAAE,MAAM;kBAC1BA,GAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;kBAChC,IAAI,WAAW,EAAE;sBACfA,GAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;mBACpC;uBAAM;sBACLA,GAAC,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;mBAChC;eACF,CAAC,CAAC;cAEH,IAAM,UAAU,GAAG,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;cACnE,IAAM,UAAU,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC;cAC9C,IAAM,QAAQ,GAAG,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;cAChE,IAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;cAExC,KAAK,CAAC,MAAM,CACV,UAAU,CAAC,IAAI,EACf,UAAU,CAAC,MAAM,EACjB,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,MAAM,CAChB,CAAC,MAAM,EAAE,CAAC;WACZ,CAAC,CAAC;;;;;;;;UASH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,SAAS;cACtC,IAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;cACtC,IAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;cAEtC,IAAI,SAAS,EAAE;kBAAE,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;eAAE;cACvE,IAAI,SAAS,EAAE;kBAAE,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;eAAE;WACxE,CAAC,CAAC;;;;;;UAOH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,SAAS;cAC1C,QAAQ,CAAC,WAAW,CAAC,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;cAClD,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;WACrD,CAAC,CAAC;;;;;;UAOH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,GAAG;cACtC,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;cAEjC,IAAM,GAAG,GAAG,KAAI,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,CAAC;cAChD,GAAG,CAAC,UAAU,CAAC,KAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC;WAClF,CAAC,CAAC;;;;UAKH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;cAClC,IAAI,OAAO,GAAGA,GAAC,CAAC,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;cAC/C,IAAI,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE;kBACnC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;eACnC;mBAAM;kBACL,OAAO,GAAGA,GAAC,CAAC,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;eAC5C;cACD,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,EAAE,KAAI,CAAC,SAAS,CAAC,CAAC;WACpE,CAAC,CAAC;;;;;;UAOH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,KAAK;cACpC,IAAM,OAAO,GAAGA,GAAC,CAAC,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC;cACxC,OAAO,CAAC,WAAW,CAAC,iBAAiB,EAAE,KAAK,KAAK,MAAM,CAAC,CAAC;cACzD,OAAO,CAAC,WAAW,CAAC,kBAAkB,EAAE,KAAK,KAAK,OAAO,CAAC,CAAC;cAC3D,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;WAC7B,CAAC,CAAC;;;;;UAMH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,UAAC,KAAK;cACnC,IAAM,OAAO,GAAGA,GAAC,CAAC,KAAI,CAAC,aAAa,EAAE,CAAC,CAAC;cACxC,OAAO,CAAC,GAAG,CAAC;kBACV,KAAK,EAAE,KAAK,GAAG,GAAG,GAAG,GAAG;kBACxB,MAAM,EAAE,EAAE;eACX,CAAC,CAAC;WACJ,CAAC,CAAC;OACJ;MAED,2BAAU,GAAV;UAAA,iBA+DC;;UA7DC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,UAAC,KAAK;cACjC,IAAI,KAAK,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE;kBACpC,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;eAC3C;cACD,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;cAE5C,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,EAAE;kBAC/B,IAAI,KAAI,CAAC,OAAO,CAAC,SAAS,EAAE;sBAC1B,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;mBAC1B;uBAAM;sBACL,KAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,CAAC;mBAC7C;eACF;cACD,IAAI,KAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE;kBAC5B,OAAO,KAAK,CAAC;eACd;WACF,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,KAAK;cACnB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;WAC3C,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,KAAK;cACnB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;WAC3C,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,KAAK;cAClB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;WAC1C,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,UAAC,KAAK;cACvB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;WAC/C,CAAC,CAAC,EAAE,CAAC,SAAS,EAAE,UAAC,KAAK;cACrB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;WAC7C,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAC,KAAK;cACpB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;WAC5C,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,KAAK;cACnB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;WAC3C,CAAC,CAAC;;UAGH,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC;UAE3D,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC;cAClD,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;WAC5D,EAAE,EAAE,CAAC,CAAC,CAAC;UAER,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,UAAC,KAAK;cAC/B,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;WAC7C,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,UAAC,KAAK;cACtB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;WAC9C,CAAC,CAAC;UAEH,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;cACzB,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;kBACtB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;eAC7C;cACD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;kBACvB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;eACjD;cACD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;kBAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;eAC1D;cACD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;kBAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;eAC1D;WACF;UAED,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;OAC3B;MAED,wBAAO,GAAP;UACE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;OACtB;MAED,6BAAY,GAAZ,UAAa,KAAK;UAChB,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;UAC7D,IAAM,IAAI,GAAG,EAAE,CAAC;UAEhB,IAAI,KAAK,CAAC,OAAO,EAAE;cAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;WAAE;UACxC,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;cAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;WAAE;UAC1D,IAAI,KAAK,CAAC,QAAQ,EAAE;cAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;WAAE;UAE3C,IAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;UAChD,IAAI,OAAO,EAAE;cACX,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;WACpB;UAED,IAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;UACzC,IAAI,SAAS,EAAE;cACb,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,KAAK,EAAE;kBAC5C,KAAK,CAAC,cAAc,EAAE,CAAC;eACxB;WACF;eAAM,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;cACpC,IAAI,CAAC,YAAY,EAAE,CAAC;WACrB;OACF;MAED,gDAA+B,GAA/B,UAAgC,KAAK;;UAEnC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO;cACjC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE;cAC7C,KAAK,CAAC,cAAc,EAAE,CAAC;WACxB;OACF;MAED,0BAAS,GAAT,UAAU,GAAG,EAAE,KAAK;UAClB,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;UAEf,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;cAChC,IAAI,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;mBACxB,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;kBAChC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE;kBACxE,OAAO,KAAK,CAAC;eACd;WACF;UAED,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC,EAAE;cAClC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;kBACtE,OAAO,IAAI,CAAC;eACb;WACF;UACD,OAAO,KAAK,CAAC;OACd;;;;;MAKD,4BAAW,GAAX;UACE,IAAI,CAAC,KAAK,EAAE,CAAC;UACb,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;OACpC;;;;;;;;MASD,0BAAS,GAAT,UAAU,YAAY;UACpB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;UACpC,IAAI,YAAY,EAAE;cAChB,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC;WACpC;OACF;;;;;;MAOD,6BAAY,GAAZ;UACE,IAAI,IAAI,CAAC,SAAS,EAAE;cAClB,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;cACxB,IAAI,CAAC,KAAK,EAAE,CAAC;WACd;OACF;MAED,2BAAU,GAAV,UAAW,IAAI;UACb,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;OACrC;MAED,4BAAW,GAAX;UACE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;OACrC;MAED,8BAAa,GAAb;UACE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;OACtC;;;;;;;MAQD,6BAAY,GAAZ;UACE,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;UACzB,IAAI,GAAG,EAAE;cACP,GAAG,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;WACvB;UACD,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;OAC5E;;;;;;;MAQD,8BAAa,GAAb,UAAc,KAAK;UACjB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;OACnC;;;;MAKD,qBAAI,GAAJ;UACE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;UACnE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;UACpB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;OAC5D;;;;MAKD,uBAAM,GAAN;UACE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;UACnE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;UACtB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;OAC5D;;;;MAKD,qBAAI,GAAJ;UACE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;UACnE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;UACpB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;OAC5D;;;;MAKD,8BAAa,GAAb;UACE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;;UAEnE,IAAI,CAAC,KAAK,EAAE,CAAC;OACd;;;;;MAMD,6BAAY,GAAZ,UAAa,gBAAgB;UAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;UACxB,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;UAC1B,IAAI,CAAC,gBAAgB,EAAE;cACrB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;WAC5D;OACF;;;;MAKD,oBAAG,GAAH;UACE,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;UAC/B,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;cACvC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;WACrB;eAAM;cACL,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC,EAAE;kBAC9B,OAAO,KAAK,CAAC;eACd;cAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;kBACzC,IAAI,CAAC,aAAa,EAAE,CAAC;kBACrB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;kBACjD,IAAI,CAAC,YAAY,EAAE,CAAC;eACrB;WACF;OACF;;;;MAKD,sBAAK,GAAL;UACE,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;UAC/B,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;cACvC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;WAC3B;eAAM;cACL,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC,EAAE;kBAC9B,OAAO,KAAK,CAAC;eACd;WACF;OACF;;;;MAKD,4BAAW,GAAX,UAAY,EAAE;UACZ,OAAO;cACL,IAAI,CAAC,aAAa,EAAE,CAAC;cACrB,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;cAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;WACrB,CAAC;OACH;;;;;;;;MASD,4BAAW,GAAX,UAAY,GAAG,EAAE,KAAK;UAAtB,iBAoBC;UAnBC,OAAO,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,UAAC,MAAM;cACzC,KAAI,CAAC,aAAa,EAAE,CAAC;cAErB,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;kBAC/B,KAAK,CAAC,MAAM,CAAC,CAAC;eACf;mBAAM;kBACL,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;sBAC7B,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;mBACrC;kBACD,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,KAAI,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;eACvE;cAED,MAAM,CAAC,IAAI,EAAE,CAAC;cACd,KAAK,CAAC,MAAM,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;cAClD,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;cAC9C,KAAI,CAAC,YAAY,EAAE,CAAC;WACrB,CAAC,CAAC,IAAI,CAAC,UAAC,CAAC;cACR,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;WACpD,CAAC,CAAC;OACJ;;;;;MAMD,sCAAqB,GAArB,UAAsB,KAAK;UAA3B,iBAaC;UAZCA,GAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,GAAG,EAAE,IAAI;cACtB,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;cAC3B,IAAI,KAAI,CAAC,OAAO,CAAC,oBAAoB,IAAI,KAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,IAAI,EAAE;kBACtF,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,oBAAoB,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;eACvF;mBAAM;kBACL,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAC,OAAO;sBACnC,OAAO,KAAI,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;mBAC5C,CAAC,CAAC,IAAI,CAAC;sBACN,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;mBACjD,CAAC,CAAC;eACJ;WACF,CAAC,CAAC;OACJ;;;;;MAMD,gCAAe,GAAf;UACE,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;;UAG7B,IAAI,GAAG,CAAC,UAAU,EAAE,EAAE;cACpB,GAAG,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;WAChE;UAED,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;OACvB;MAED,8BAAa,GAAb,UAAc,OAAO,EAAE,OAAO;;UAE5B,OAAO,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG,GAAG,OAAO,CAAC;UACrD,QAAQ,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;;UAGpD,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;cAC7B,IAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC;cAC7C,IAAI,SAAS,EAAE;kBACb,IAAM,YAAY,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;kBAExC,IAAM,OAAO,GAAGA,GAAC,CAAC,CAAC,YAAY,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;kBACvE,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;eAC7B;WACF;OACF;MAED,2BAAU,GAAV;UACE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;OACvB;MAED,4BAAW,GAAX,UAAY,MAAM,EAAE,KAAK;UACvB,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;UAE/B,IAAI,GAAG,EAAE;cACP,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;cACzCA,GAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;;;cAI5B,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE;kBACrB,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;kBACpC,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;sBAC3C,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC,oBAAoB,CAAC;sBAC/C,KAAK,CAAC,mBAAmB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,CAAC;sBACzD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;mBAC3C;eACF;WACF;OACF;;;;;;MAOD,uBAAM,GAAN;UACE,IAAI,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;UAC7B,IAAI,GAAG,CAAC,UAAU,EAAE,EAAE;cACpB,IAAM,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;cAClD,GAAG,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;cACnC,GAAG,CAAC,MAAM,EAAE,CAAC;cAEb,IAAI,CAAC,aAAa,EAAE,CAAC;cACrB,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;cAC/B,IAAI,CAAC,YAAY,EAAE,CAAC;WACrB;OACF;;;;;;;;;;MAWD,4BAAW,GAAX;UACE,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;;UAGpD,IAAM,OAAO,GAAGA,GAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;UACvD,IAAM,QAAQ,GAAG;cACf,KAAK,EAAE,GAAG;cACV,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE;cACpB,GAAG,EAAE,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;WAChD,CAAC;;UAGF,IAAI,OAAO,CAAC,MAAM,EAAE;;cAElB,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,CAAC;WAC5D;UAED,OAAO,QAAQ,CAAC;OACjB;MAED,uBAAM,GAAN,UAAO,QAAQ;UACb,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;UAC7C,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;cACvC,IAAI,CAAC,aAAa,EAAE,CAAC;cACrB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;cACjC,IAAI,CAAC,YAAY,EAAE,CAAC;WACrB;OACF;MAED,uBAAM,GAAN,UAAO,QAAQ;UACb,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;UAC7C,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;cACvC,IAAI,CAAC,aAAa,EAAE,CAAC;cACrB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;cACjC,IAAI,CAAC,YAAY,EAAE,CAAC;WACrB;OACF;MAED,0BAAS,GAAT;UACE,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;UAC7C,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;cACvC,IAAI,CAAC,aAAa,EAAE,CAAC;cACrB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;cAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;WACrB;OACF;MAED,0BAAS,GAAT;UACE,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;UAC7C,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;cACvC,IAAI,CAAC,aAAa,EAAE,CAAC;cACrB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;cAC1B,IAAI,CAAC,YAAY,EAAE,CAAC;WACrB;OACF;MAED,4BAAW,GAAX;UACE,IAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;UAC7C,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE;cACvC,IAAI,CAAC,aAAa,EAAE,CAAC;cACrB,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;cAC5B,IAAI,CAAC,YAAY,EAAE,CAAC;WACrB;OACF;;;;;;MAOD,yBAAQ,GAAR,UAAS,GAAG,EAAE,OAAO,EAAE,UAAU;UAC/B,IAAI,SAAS,CAAC;UACd,IAAI,UAAU,EAAE;cACd,IAAM,QAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;cAC/B,IAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;cACpC,SAAS,GAAG;kBACV,KAAK,EAAE,KAAK,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK;kBAC/C,MAAM,EAAE,KAAK,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC;eACjD,CAAC;WACH;eAAM;cACL,SAAS,GAAG;kBACV,KAAK,EAAE,GAAG,CAAC,CAAC;kBACZ,MAAM,EAAE,GAAG,CAAC,CAAC;eACd,CAAC;WACH;UAED,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;OACxB;;;;MAKD,yBAAQ,GAAR;UACE,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;OACpC;;;;MAKD,sBAAK,GAAL;;;UAGE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE;cACpB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;WACxB;OACF;;;;;MAMD,wBAAO,GAAP;UACE,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;OAClF;;;;MAKD,sBAAK,GAAL;UACE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;OAC5C;;;;MAKD,iCAAgB,GAAhB;UACE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;OAC/B;MACH,aAAC;EAAD,CAAC,IAAA;;EC31BD;MACE,mBAAY,OAAO;UACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UACvB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;OAC9C;MAED,8BAAU,GAAV;UACE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;OAC1D;;;;;;MAOD,gCAAY,GAAZ,UAAa,KAAK;UAChB,IAAM,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,aAAa,CAAC;UACxD,IAAI,aAAa,IAAI,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM,EAAE;;cAEtE,IAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;cACvG,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;kBAC9D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,+BAA+B,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;eAC1E;cACD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;WAC5C;OACF;MACH,gBAAC;EAAD,CAAC,IAAA;;EC1BD;MACE,kBAAY,OAAO;UACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UACvB,IAAI,CAAC,cAAc,GAAGA,GAAC,CAAC,QAAQ,CAAC,CAAC;UAClC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;UACzC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;UAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;UAClC,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;UAEhC,IAAI,CAAC,SAAS,GAAGA,GAAC,CAAC;cACjB,6BAA6B;cAC7B,wCAAwC;cACxC,QAAQ;WACT,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;OACrC;;;;MAKD,6BAAU,GAAV;UACE,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;;cAEnC,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,UAAC,CAAC;kBACpC,CAAC,CAAC,cAAc,EAAE,CAAC;eACpB,CAAC;;cAEF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC;cACrC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;WACnE;eAAM;cACL,IAAI,CAAC,sBAAsB,EAAE,CAAC;WAC/B;OACF;;;;MAKD,yCAAsB,GAAtB;UAAA,iBAmEC;UAlEC,IAAI,UAAU,GAAGA,GAAC,EAAE,CAAC;UACrB,IAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;UAEvE,IAAI,CAAC,qBAAqB,CAAC,WAAW,GAAG,UAAC,CAAC;cACzC,IAAM,UAAU,GAAG,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;cAC/D,IAAM,aAAa,GAAG,KAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,KAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;cAC5E,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,aAAa,EAAE;kBACtD,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;kBAClC,KAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;kBAC3C,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;kBAC7C,gBAAgB,CAAC,IAAI,CAAC,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;eACtD;cACD,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;WACvC,CAAC;UAEF,IAAI,CAAC,qBAAqB,CAAC,WAAW,GAAG,UAAC,CAAC;cACzC,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;cACtC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;kBACtB,KAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;eACtC;WACF,CAAC;UAEF,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG;cAClC,UAAU,GAAGA,GAAC,EAAE,CAAC;cACjB,KAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;WACtC,CAAC;;;UAIF,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC;eACxE,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC;eACvD,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;;UAGjD,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE;cAC7B,KAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;cACjC,gBAAgB,CAAC,IAAI,CAAC,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;WAClD,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE;cACjB,KAAI,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;cACpC,gBAAgB,CAAC,IAAI,CAAC,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;WACtD,CAAC,CAAC;;UAGH,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,KAAK;cAC9B,IAAM,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC;;cAGtD,KAAK,CAAC,cAAc,EAAE,CAAC;cAEvB,IAAI,YAAY,IAAI,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE;kBACnE,KAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;kBACvB,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,+BAA+B,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;eAC1E;mBAAM;kBACLA,GAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,UAAC,GAAG,EAAE,IAAI;sBACnC,IAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;sBAE3C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;0BAC3C,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;uBAClD;2BAAM;0BACLA,GAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;8BACxB,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;2BAChD,CAAC,CAAC;uBACJ;mBACF,CAAC,CAAC;eACJ;WACF,CAAC,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;OAC1B;MAED,0BAAO,GAAP;UAAA,iBAKC;UAJC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;cAClD,KAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,KAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;WACvF,CAAC,CAAC;UACH,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;OACjC;MACH,eAAC;EAAD,CAAC,IAAA;;EC/GD,IAAI,UAAU,CAAC;EACf,IAAI,GAAG,CAAC,aAAa,EAAE;MACrB,IAAI,GAAG,CAAC,YAAY,EAAE;UACpB,OAAO,CAAC,CAAC,YAAY,CAAC,EAAE,UAAS,EAAE;cACjC,UAAU,GAAG,EAAE,CAAC;WACjB,CAAC,CAAC;OACJ;WAAM;UACL,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;OAChC;GACF;EAED;;;EAGA;MACE,kBAAY,OAAO;UACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UACvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;UACzC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;UAC7C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;UAC3C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;OAChC;MAED,uBAAI,GAAJ;UACE,IAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;UACtC,IAAI,UAAU,IAAI,GAAG,CAAC,aAAa,EAAE;cACnC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC;WACvC;OACF;;;;MAKD,8BAAW,GAAX;UACE,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;OAC1C;;;;MAKD,yBAAM,GAAN;UACE,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;cACtB,IAAI,CAAC,UAAU,EAAE,CAAC;WACnB;eAAM;cACL,IAAI,CAAC,QAAQ,EAAE,CAAC;WACjB;UACD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC;OAC/C;;;;MAKD,2BAAQ,GAAR;UAAA,iBAiCC;UAhCC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;UACvE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;UAE9C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;UACpD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;UAClC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;;UAGtB,IAAI,GAAG,CAAC,aAAa,EAAE;cACrB,IAAM,UAAQ,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;;cAGpF,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE;kBAChC,IAAM,QAAM,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;kBACvE,UAAQ,CAAC,UAAU,GAAG,QAAM,CAAC;kBAC7B,UAAQ,CAAC,EAAE,CAAC,gBAAgB,EAAE,UAAC,EAAE;sBAC/B,QAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;mBAC3B,CAAC,CAAC;eACJ;cAED,UAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,KAAK;kBACxB,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,eAAe,EAAE,UAAQ,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;eACxE,CAAC,CAAC;;cAGH,UAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC;cACrD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,UAAQ,CAAC,CAAC;WAC1C;eAAM;cACL,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,KAAK;kBAC7B,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,eAAe,EAAE,KAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;eACxE,CAAC,CAAC;WACJ;OACF;;;;MAKD,6BAAU,GAAV;;UAEE,IAAI,GAAG,CAAC,aAAa,EAAE;cACrB,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;cAChD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;cACvC,QAAQ,CAAC,UAAU,EAAE,CAAC;WACvB;UAED,IAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC;UACnF,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC;UAEjD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;UAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC;UAC7E,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;UAErC,IAAI,QAAQ,EAAE;cACZ,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;WAC5E;UAED,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;UAEvB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;OACtD;MAED,0BAAO,GAAP;UACE,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;cACtB,IAAI,CAAC,UAAU,EAAE,CAAC;WACnB;OACF;MACH,eAAC;EAAD,CAAC,IAAA;;ECzHD,IAAM,gBAAgB,GAAG,EAAE,CAAC;EAE5B;MACE,mBAAY,OAAO;UACjB,IAAI,CAAC,SAAS,GAAGA,GAAC,CAAC,QAAQ,CAAC,CAAC;UAC7B,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC;UAC/C,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;UAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;OAChC;MAED,8BAAU,GAAV;UAAA,iBAwBC;UAvBC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;cAC5D,IAAI,CAAC,OAAO,EAAE,CAAC;cACf,OAAO;WACR;UAED,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,WAAW,EAAE,UAAC,KAAK;cACpC,KAAK,CAAC,cAAc,EAAE,CAAC;cACvB,KAAK,CAAC,eAAe,EAAE,CAAC;cAExB,IAAM,WAAW,GAAG,KAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,KAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;cAC7E,IAAM,WAAW,GAAG,UAAC,KAAK;kBACxB,IAAI,MAAM,GAAG,KAAK,CAAC,OAAO,IAAI,WAAW,GAAG,gBAAgB,CAAC,CAAC;kBAE9D,MAAM,GAAG,CAAC,KAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;kBAC1F,MAAM,GAAG,CAAC,KAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;kBAE1F,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;eAC/B,CAAC;cAEF,KAAI,CAAC,SAAS,CAAC,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC,SAAS,EAAE;kBACzD,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;eAC9C,CAAC,CAAC;WACJ,CAAC,CAAC;OACJ;MAED,2BAAO,GAAP;UACE,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;UACtB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;OACpC;MACH,gBAAC;EAAD,CAAC,IAAA;;ECvCD;MACE,oBAAY,OAAO;UAAnB,iBAgBC;UAfC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UAEvB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;UACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;UAC3C,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;UAC7C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;UAE3C,IAAI,CAAC,OAAO,GAAGA,GAAC,CAAC,MAAM,CAAC,CAAC;UACzB,IAAI,CAAC,UAAU,GAAGA,GAAC,CAAC,YAAY,CAAC,CAAC;UAElC,IAAI,CAAC,QAAQ,GAAG;cACd,KAAI,CAAC,QAAQ,CAAC;kBACZ,CAAC,EAAE,KAAI,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,KAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;eACvD,CAAC,CAAC;WACJ,CAAC;OACH;MAED,6BAAQ,GAAR,UAAS,IAAI;UACX,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;UACrC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;UACpC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;cAClC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;WACtD;OACF;;;;MAKD,2BAAM,GAAN;UACE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;UACvC,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;cACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;cAC/D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;cACrE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;cACpC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;cAC3D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;WAC3C;eAAM;cACL,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;cAC1C,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;cACvD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;cACpE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;WAC5C;UAED,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,0BAA0B,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;OACtE;MAED,iCAAY,GAAZ;UACE,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;OAC5C;MACH,iBAAC;EAAD,CAAC,IAAA;;EClDD;MACE,gBAAY,OAAO;UAAnB,iBAuBC;UAtBC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UACvB,IAAI,CAAC,SAAS,GAAGA,GAAC,CAAC,QAAQ,CAAC,CAAC;UAC7B,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC;UACnD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;UAElC,IAAI,CAAC,MAAM,GAAG;cACZ,sBAAsB,EAAE,UAAC,EAAE,EAAE,CAAC;kBAC5B,IAAI,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE;sBACzB,CAAC,CAAC,cAAc,EAAE,CAAC;mBACpB;eACF;cACD,8EAA8E,EAAE;kBAC9E,KAAI,CAAC,MAAM,EAAE,CAAC;eACf;cACD,oBAAoB,EAAE;kBACpB,KAAI,CAAC,IAAI,EAAE,CAAC;eACb;cACD,6BAA6B,EAAE;kBAC7B,KAAI,CAAC,MAAM,EAAE,CAAC;eACf;WACF,CAAC;OACH;MAED,2BAAU,GAAV;UAAA,iBAqDC;UApDC,IAAI,CAAC,OAAO,GAAGA,GAAC,CAAC;cACf,2BAA2B;cAC3B,sCAAsC;cACtC,+CAA+C;cAC/C,yDAAyD;cACzD,yDAAyD;cACzD,yDAAyD;cACzD,cAAc;eACb,IAAI,CAAC,OAAO,CAAC,kBAAkB,GAAG,qBAAqB,GAAG,qBAAqB;cAChF,0BAA0B;eACzB,IAAI,CAAC,OAAO,CAAC,kBAAkB,GAAG,EAAE,GAAG,iDAAiD;cACzF,QAAQ;cACR,QAAQ;WACT,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;UAEzC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,UAAC,KAAK;cACjC,IAAI,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;kBACrC,KAAK,CAAC,cAAc,EAAE,CAAC;kBACvB,KAAK,CAAC,eAAe,EAAE,CAAC;kBAExB,IAAM,SAAO,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;kBAC5E,IAAM,UAAQ,GAAG,SAAO,CAAC,MAAM,EAAE,CAAC;kBAClC,IAAM,WAAS,GAAG,KAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;kBAE7C,IAAM,aAAW,GAAG,UAAC,KAAK;sBACxB,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,EAAE;0BACrC,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,UAAQ,CAAC,IAAI;0BAChC,CAAC,EAAE,KAAK,CAAC,OAAO,IAAI,UAAQ,CAAC,GAAG,GAAG,WAAS,CAAC;uBAC9C,EAAE,SAAO,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;sBAE7B,KAAI,CAAC,MAAM,CAAC,SAAO,CAAC,CAAC,CAAC,CAAC,CAAC;mBACzB,CAAC;kBAEF,KAAI,CAAC,SAAS;uBACX,EAAE,CAAC,WAAW,EAAE,aAAW,CAAC;uBAC5B,GAAG,CAAC,SAAS,EAAE,UAAC,CAAC;sBAChB,CAAC,CAAC,cAAc,EAAE,CAAC;sBACnB,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,aAAW,CAAC,CAAC;sBAC7C,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;mBAC5C,CAAC,CAAC;kBAEL,IAAI,CAAC,SAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;sBAC1B,SAAO,CAAC,IAAI,CAAC,OAAO,EAAE,SAAO,CAAC,MAAM,EAAE,GAAG,SAAO,CAAC,KAAK,EAAE,CAAC,CAAC;mBAC3D;eACF;WACF,CAAC,CAAC;;UAGH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,UAAC,CAAC;cACzB,CAAC,CAAC,cAAc,EAAE,CAAC;cACnB,KAAI,CAAC,MAAM,EAAE,CAAC;WACf,CAAC,CAAC;OACJ;MAED,wBAAO,GAAP;UACE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;OACvB;MAED,uBAAM,GAAN,UAAO,MAAM;UACX,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;cAC7B,OAAO,KAAK,CAAC;WACd;UAED,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;UAClC,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;UAEhE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;UAEnD,IAAI,OAAO,EAAE;cACX,IAAM,MAAM,GAAGA,GAAC,CAAC,MAAM,CAAC,CAAC;cACzB,IAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;cACnC,IAAM,GAAG,GAAG;kBACV,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC;kBAC5D,GAAG,EAAE,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE,CAAC;eAC1D,CAAC;;cAGF,IAAM,SAAS,GAAG;kBAChB,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;kBAC3B,CAAC,EAAE,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;eAC7B,CAAC;cAEF,UAAU,CAAC,GAAG,CAAC;kBACb,OAAO,EAAE,OAAO;kBAChB,IAAI,EAAE,GAAG,CAAC,IAAI;kBACd,GAAG,EAAE,GAAG,CAAC,GAAG;kBACZ,KAAK,EAAE,SAAS,CAAC,CAAC;kBAClB,MAAM,EAAE,SAAS,CAAC,CAAC;eACpB,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;cAE1B,IAAM,YAAY,GAAG,IAAI,KAAK,EAAE,CAAC;cACjC,YAAY,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;cAEtC,IAAM,UAAU,GAAG,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,YAAY,CAAC,KAAK,GAAG,GAAG,GAAG,YAAY,CAAC,MAAM,GAAG,GAAG,CAAC;cACnJ,UAAU,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;cACjE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;WAClD;eAAM;cACL,IAAI,CAAC,IAAI,EAAE,CAAC;WACb;UAED,OAAO,OAAO,CAAC;OAChB;;;;;;MAOD,qBAAI,GAAJ;UACE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;UAC1C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;OAChC;MACH,aAAC;EAAD,CAAC,IAAA;;EC1ID,IAAM,aAAa,GAAG,SAAS,CAAC;EAChC,IAAM,WAAW,GAAG,2EAA2E,CAAC;EAEhG;MACE,kBAAY,OAAO;UAAnB,iBAYC;UAXC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UACvB,IAAI,CAAC,MAAM,GAAG;cACZ,kBAAkB,EAAE,UAAC,EAAE,EAAE,CAAC;kBACxB,IAAI,CAAC,CAAC,CAAC,kBAAkB,EAAE,EAAE;sBAC3B,KAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;mBACrB;eACF;cACD,oBAAoB,EAAE,UAAC,EAAE,EAAE,CAAC;kBAC1B,KAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;eACvB;WACF,CAAC;OACH;MAED,6BAAU,GAAV;UACE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;OAC3B;MAED,0BAAO,GAAP;UACE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;OAC3B;MAED,0BAAO,GAAP;UACE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;cACvB,OAAO;WACR;UAED,IAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;UAC9C,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;UAEzC,IAAI,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;cACnC,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,aAAa,GAAG,OAAO,CAAC;cAC1D,IAAM,IAAI,GAAGA,GAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5D,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE;kBACxCA,GAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;eAClC;cAED,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;cACpC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;cAC1B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;WACrC;OACF;MAED,gCAAa,GAAb,UAAc,CAAC;UACb,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE;cAC/D,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,YAAY,EAAE,CAAC;cAC3E,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;WAChC;OACF;MAED,8BAAW,GAAX,UAAY,CAAC;UACX,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE;cAC/D,IAAI,CAAC,OAAO,EAAE,CAAC;WAChB;OACF;MACH,eAAC;EAAD,CAAC,IAAA;;EC7DD;;;EAGA;MACE,kBAAY,OAAO;UAAnB,iBAOC;UANC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;UACrC,IAAI,CAAC,MAAM,GAAG;cACZ,mBAAmB,EAAE;kBACnB,KAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;eACxC;WACF,CAAC;OACH;MAED,mCAAgB,GAAhB;UACE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;OACtC;MACH,eAAC;EAAD,CAAC,IAAA;;ECjBD;MACE,qBAAY,OAAO;UAAnB,iBAaC;UAZC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UAEvB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC;UACnD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,MAAM,GAAG;cACZ,mCAAmC,EAAE;kBACnC,KAAI,CAAC,MAAM,EAAE,CAAC;eACf;cACD,6BAA6B,EAAE;kBAC7B,KAAI,CAAC,MAAM,EAAE,CAAC;eACf;WACF,CAAC;OACH;MAED,sCAAgB,GAAhB;UACE,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;OACnC;MAED,gCAAU,GAAV;UAAA,iBAOC;UANC,IAAI,CAAC,YAAY,GAAGA,GAAC,CAAC,gCAAgC,CAAC,CAAC;UACxD,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE;cAC5B,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;WAC9B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;UAE/D,IAAI,CAAC,MAAM,EAAE,CAAC;OACf;MAED,6BAAO,GAAP;UACE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;OAC5B;MAED,4BAAM,GAAN;UACE,IAAM,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;UACrG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;OAClC;MACH,kBAAC;EAAD,CAAC,IAAA;;ECjCD;MACE,iBAAY,OAAO;UACjB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;UAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UACvB,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;UAC3C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;UAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CACrC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,CAC9C,CAAC;OACH;MAED,mCAAiB,GAAjB,UAAkB,YAAY;UAC5B,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;UACjD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE;cACxC,OAAO,EAAE,CAAC;WACX;UAED,IAAI,GAAG,CAAC,KAAK,EAAE;cACb,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;WAC/D;UAED,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC;eAC3C,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;eACrB,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC;eAC3B,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC;UAEhC,OAAO,IAAI,GAAG,QAAQ,GAAG,GAAG,CAAC;OAC9B;MAED,wBAAM,GAAN,UAAO,CAAC;UACN,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE;cACtC,OAAO,CAAC,CAAC,OAAO,CAAC;WAClB;UACD,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;UACrC,OAAO,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;OAC1B;MAED,4BAAU,GAAV;UACE,IAAI,CAAC,iBAAiB,EAAE,CAAC;UACzB,IAAI,CAAC,sBAAsB,EAAE,CAAC;UAC9B,IAAI,CAAC,qBAAqB,EAAE,CAAC;UAC7B,IAAI,CAAC,sBAAsB,EAAE,CAAC;UAC9B,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;OAC5B;MAED,yBAAO,GAAP;UACE,OAAO,IAAI,CAAC,gBAAgB,CAAC;OAC9B;MAED,iCAAe,GAAf,UAAgB,IAAI;UAClB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;cAC/C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC;kBACrD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;WAC3D;UAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;OACpC;MAED,qCAAmB,GAAnB,UAAoB,IAAI;UACtB,IAAM,eAAe,GAAG,CAAC,YAAY,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;UACnF,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;UAE1B,QAAQ,CAAC,IAAI,KAAK,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAKA,GAAC,CAAC,OAAO,CAAC,IAAI,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;OACnG;MAED,8BAAY,GAAZ,UAAa,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS;UAArD,iBAyJC;UAxJC,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC;cACzB,SAAS,EAAE,aAAa,GAAG,SAAS;cACpC,QAAQ,EAAE;kBACR,IAAI,CAAC,MAAM,CAAC;sBACV,SAAS,EAAE,2BAA2B;sBACtC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,oBAAoB,CAAC;sBACtE,OAAO,EAAE,OAAO;sBAChB,KAAK,EAAE,UAAC,CAAC;0BACP,IAAM,OAAO,GAAGA,GAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;0BACnC,IAAI,SAAS,IAAI,SAAS,EAAE;8BAC1B,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE;kCAClC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;kCACzC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;+BAC1C,CAAC,CAAC;2BACJ;+BAAM,IAAI,SAAS,EAAE;8BACpB,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE;kCAClC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;+BAC1C,CAAC,CAAC;2BACJ;+BAAM,IAAI,SAAS,EAAE;8BACpB,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE;kCAClC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC;+BAC1C,CAAC,CAAC;2BACJ;uBACF;sBACD,QAAQ,EAAE,UAAC,OAAO;0BAChB,IAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;0BACxD,IAAI,SAAS,EAAE;8BACb,YAAY,CAAC,GAAG,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;8BAChD,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;2BAC3C;0BACD,IAAI,CAAC,SAAS,EAAE;8BACd,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;2BAC1C;uBACF;mBACF,CAAC;kBACF,IAAI,CAAC,MAAM,CAAC;sBACV,SAAS,EAAE,iBAAiB;sBAC5B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC;sBAC1D,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;sBAC7B,IAAI,EAAE;0BACJ,MAAM,EAAE,UAAU;uBACnB;mBACF,CAAC;kBACF,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;sBACf,KAAK,EAAE,CAAC,SAAS,GAAG;0BAClB,4BAA4B;0BAC5B,oCAAoC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ;0BAC5E,SAAS;0BACT,+GAA+G;0BAC/G,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;0BAC3B,eAAe;0BACf,UAAU;0BACV,qDAAqD;0BACrD,SAAS;0BACT,gHAAgH;0BAChH,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;0BACxB,eAAe;0BACf,oIAAoI;0BACpI,UAAU;0BACV,kFAAkF;0BAClF,QAAQ;uBACT,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE;2BACd,SAAS,GAAG;8BACX,4BAA4B;8BAC5B,oCAAoC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ;8BAC5E,SAAS;8BACT,oHAAoH;8BACpH,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc;8BAC9B,eAAe;8BACf,UAAU;8BACV,qDAAqD;8BACrD,SAAS;8BACT,gHAAgH;8BAChH,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;8BACxB,eAAe;8BACf,oIAAoI;8BACpI,kFAAkF;8BAClF,QAAQ;2BACT,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;sBAChB,QAAQ,EAAE,UAAC,SAAS;0BAClB,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;8BAC5C,IAAM,OAAO,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC;8BACxB,OAAO,CAAC,MAAM,CAAC,KAAI,CAAC,EAAE,CAAC,OAAO,CAAC;kCAC7B,MAAM,EAAE,KAAI,CAAC,OAAO,CAAC,MAAM;kCAC3B,UAAU,EAAE,KAAI,CAAC,OAAO,CAAC,UAAU;kCACnC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;kCAChC,SAAS,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS;kCACjC,OAAO,EAAE,KAAI,CAAC,OAAO,CAAC,OAAO;+BAC9B,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;2BACd,CAAC,CAAC;;0BAEH,IAAI,YAAY,GAAG;8BACjB,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;2BACzF,CAAC;0BACF,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;8BACnD,IAAM,OAAO,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC;8BACxB,OAAO,CAAC,MAAM,CAAC,KAAI,CAAC,EAAE,CAAC,OAAO,CAAC;kCAC7B,MAAM,EAAE,YAAY;kCACpB,UAAU,EAAE,YAAY;kCACxB,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;kCAChC,SAAS,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS;kCACjC,OAAO,EAAE,KAAI,CAAC,OAAO,CAAC,OAAO;+BAC9B,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;2BACd,CAAC,CAAC;0BACH,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;8BACjDA,GAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;kCACb,IAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,CAAC;kCAC1F,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;kCACvC,KAAK,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC;uCACjC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC;uCACzB,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC;uCACzB,IAAI,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;kCACtC,KAAK,CAAC,KAAK,EAAE,CAAC;+BACf,CAAC,CAAC;2BACJ,CAAC,CAAC;uBACJ;sBACD,KAAK,EAAE,UAAC,KAAK;0BACX,KAAK,CAAC,eAAe,EAAE,CAAC;0BAExB,IAAM,OAAO,GAAGA,GAAC,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC;0BACnC,IAAM,OAAO,GAAGA,GAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;0BAChC,IAAM,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;0BACxC,IAAI,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;0BAEvC,IAAI,SAAS,KAAK,aAAa,EAAE;8BAC/B,IAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;8BAC1C,IAAM,QAAQ,GAAGA,GAAC,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;8BAGzF,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC;;8BAG/D,IAAM,KAAK,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;8BAC5B,KAAK,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC;mCACjC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC;mCACzB,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC;mCACzB,IAAI,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;8BACtC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;8BACxB,OAAO,CAAC,KAAK,EAAE,CAAC;2BACjB;+BAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,SAAS,CAAC,EAAE;8BAChE,IAAM,GAAG,GAAG,SAAS,KAAK,WAAW,GAAG,kBAAkB,GAAG,OAAO,CAAC;8BACrE,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;8BACzE,IAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;8BAEzF,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;8BACvB,cAAc,CAAC,IAAI,CAAC,OAAO,GAAG,SAAS,EAAE,KAAK,CAAC,CAAC;8BAChD,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,SAAS,EAAE,KAAK,CAAC,CAAC;2BACnD;uBACF;mBACF,CAAC;eACH;WACF,CAAC,CAAC,MAAM,EAAE,CAAC;OACb;MAED,mCAAiB,GAAjB;UAAA,iBAmYC;UAlYC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;cAChC,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;kBACzB,KAAI,CAAC,MAAM,CAAC;sBACV,SAAS,EAAE,iBAAiB;sBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CACtC,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAI,CAAC,OAAO,CACrD;sBACD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;sBAC9B,IAAI,EAAE;0BACJ,MAAM,EAAE,UAAU;uBACnB;mBACF,CAAC;kBACF,KAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;sBACf,SAAS,EAAE,gBAAgB;sBAC3B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS;sBAC7B,KAAK,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;sBAC5B,QAAQ,EAAE,UAAC,IAAI;0BACb,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;8BAC5B,IAAI,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,GAAG,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;2BACpG;0BAED,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;0BACrB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;0BACzB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,UAAU,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;0BAC/D,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,UAAU,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,EAAE,CAAC;0BAE1E,OAAO,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,SAAS,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;uBACvE;sBACD,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;mBAC9D,CAAC;eACH,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;kCAEM,QAAQ,EAAM,QAAQ;cAC7B,IAAM,IAAI,GAAG,OAAK,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;cAE9C,OAAK,OAAO,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,EAAE;kBACxC,OAAO,KAAI,CAAC,MAAM,CAAC;sBACjB,SAAS,EAAE,iBAAiB,GAAG,IAAI;sBACnC,QAAQ,EAAE,mBAAmB,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,QAAQ;sBAC3E,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;sBAC9B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;mBAC9D,CAAC,CAAC,MAAM,EAAE,CAAC;eACb,CAAC,CAAC;WACJ;;UAXD,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,GAAG,QAAQ,EAAE,QAAQ,EAAE;sBAAvF,QAAQ,EAAM,QAAQ;WAW9B;UAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;cAC/B,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,eAAe;kBAC1B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;kBAC/C,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,KAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;kBAC7D,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,aAAa,CAAC;eACrE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE;cACjC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,iBAAiB;kBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;kBACjD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;kBACjE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,eAAe,CAAC;eACvE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;cACpC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,oBAAoB;kBAC/B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;kBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,KAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;kBACvE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,kBAAkB,CAAC;eAC1E,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;cAChC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;kBACjD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;kBACtE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,qBAAqB,CAAC;eAC/D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE;cACxC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,wBAAwB;kBACnC,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC;kBACxD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,KAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC;kBAC/E,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,sBAAsB,CAAC;eAC9E,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;cACtC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,sBAAsB;kBACjC,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;kBACtD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW;kBACnC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,oBAAoB,CAAC;eAC5E,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;cACpC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,oBAAoB;kBAC/B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;kBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS;kBACjC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,kBAAkB,CAAC;eAC1E,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE;cACnC,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;;cAG7DA,GAAC,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,UAAC,GAAG,EAAE,QAAQ;kBACxD,QAAQ,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;kBACjD,IAAI,KAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE;sBACtC,IAAIA,GAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;0BACtD,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;uBACvC;mBACF;eACF,CAAC,CAAC;cAEH,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;kBACzB,KAAI,CAAC,MAAM,CAAC;sBACV,SAAS,EAAE,iBAAiB;sBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CACtC,uCAAuC,EAAE,KAAI,CAAC,OAAO,CACtD;sBACD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;sBAC5B,IAAI,EAAE;0BACJ,MAAM,EAAE,UAAU;uBACnB;mBACF,CAAC;kBACF,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC;sBACpB,SAAS,EAAE,mBAAmB;sBAC9B,cAAc,EAAE,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS;sBAC5C,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;sBACrE,KAAK,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;sBAC1B,QAAQ,EAAE,UAAC,IAAI;0BACb,OAAO,8BAA8B,GAAG,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;uBAC1E;sBACD,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,iBAAiB,CAAC;mBACzE,CAAC;eACH,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE;cACnC,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;kBACzB,KAAI,CAAC,MAAM,CAAC;sBACV,SAAS,EAAE,iBAAiB;sBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC,uCAAuC,EAAE,KAAI,CAAC,OAAO,CAAC;sBAC/F,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;sBAC5B,IAAI,EAAE;0BACJ,MAAM,EAAE,UAAU;uBACnB;mBACF,CAAC;kBACF,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC;sBACpB,SAAS,EAAE,mBAAmB;sBAC9B,cAAc,EAAE,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS;sBAC5C,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,SAAS;sBAC7B,KAAK,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;sBAC1B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,iCAAiC,CAAC,iBAAiB,CAAC;mBACzE,CAAC;eACH,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;cAChC,OAAO,KAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;WAChF,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;cACpC,OAAO,KAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;WACtF,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;cACpC,OAAO,KAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;WACtF,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;cAC7B,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC;kBACxD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,KAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC;kBAClF,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,4BAA4B,CAAC;eACtE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;cAC7B,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;kBACtD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC;kBAC9E,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,0BAA0B,CAAC;eACpE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;cAC9B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;cACpD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;cACzE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;WAC9D,CAAC,CAAC;UAEH,IAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;cAChC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC;cACtD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC;cAC7E,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,sBAAsB,CAAC;WAChE,CAAC,CAAC;UAEH,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC;cAC/B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC;cACrD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;cAC3E,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,qBAAqB,CAAC;WAC/D,CAAC,CAAC;UAEH,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;cAC9B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC;cACvD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;cAC5E,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;WAC9D,CAAC,CAAC;UAEH,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;cAC1B,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;cAClD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;cACxE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;WAC1D,CAAC,CAAC;UAEH,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;cACzB,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;cACjD,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;cACtE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC;WACzD,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;UAC5E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;UAChF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;UAC9E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;UAC5E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;UACpE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;UAElE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;cACpC,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;kBACzB,KAAI,CAAC,MAAM,CAAC;sBACV,SAAS,EAAE,iBAAiB;sBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,KAAI,CAAC,OAAO,CAAC;sBAClG,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS;sBACtC,IAAI,EAAE;0BACJ,MAAM,EAAE,UAAU;uBACnB;mBACF,CAAC;kBACF,KAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;sBACf,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;0BAClB,SAAS,EAAE,YAAY;0BACvB,QAAQ,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,CAAC;uBAClE,CAAC;sBACF,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;0BAClB,SAAS,EAAE,WAAW;0BACtB,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;uBAC5B,CAAC;mBACH,CAAC;eACH,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE;cACjC,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;kBACzB,KAAI,CAAC,MAAM,CAAC;sBACV,SAAS,EAAE,iBAAiB;sBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,KAAI,CAAC,OAAO,CAAC;sBACnG,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;sBAC9B,IAAI,EAAE;0BACJ,MAAM,EAAE,UAAU;uBACnB;mBACF,CAAC;kBACF,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC;sBACpB,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,WAAW;sBAC/B,cAAc,EAAE,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS;sBAC5C,SAAS,EAAE,sBAAsB;sBACjC,KAAK,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;sBAC5B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,mBAAmB,CAAC;mBAC7D,CAAC;eACH,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;cAChC,OAAO,KAAI,CAAC,EAAE,CAAC,WAAW,CAAC;kBACzB,KAAI,CAAC,MAAM,CAAC;sBACV,SAAS,EAAE,iBAAiB;sBAC5B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,sBAAsB,CAAC,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAI,CAAC,OAAO,CAAC;sBAC9F,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;sBAC9B,IAAI,EAAE;0BACJ,MAAM,EAAE,UAAU;uBACnB;mBACF,CAAC;kBACF,KAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;sBACf,KAAK,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;sBAC5B,SAAS,EAAE,YAAY;sBACvB,KAAK,EAAE;0BACL,qCAAqC;0BACrC,+FAA+F;0BAC/F,oDAAoD;0BACpD,sDAAsD;0BACtD,QAAQ;0BACR,iDAAiD;uBAClD,CAAC,IAAI,CAAC,EAAE,CAAC;mBACX,CAAC;eACH,EAAE;kBACD,QAAQ,EAAE,UAAC,KAAK;sBACd,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;sBACnE,QAAQ,CAAC,GAAG,CAAC;0BACX,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,GAAG,IAAI;0BACjD,MAAM,EAAE,KAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,GAAG,IAAI;uBACnD,CAAC,CAAC,SAAS,CAAC,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;2BACjE,EAAE,CAAC,WAAW,EAAE,KAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC,CAAC;mBACtD;eACF,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;cAC/B,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;kBAC/C,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,KAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC;kBACxE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;eAC3D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE;cAClC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;kBAClD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;kBAC9B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;eAC5D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE;cAChC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;kBAChD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;kBAC9B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;eAC5D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;cAC7B,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;kBAChD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG,KAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAAC;kBAC7E,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,6BAA6B,CAAC;eACvE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE;cACrC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,gBAAgB;kBAC3B,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;kBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU;kBACrC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,mBAAmB,CAAC;eAC7D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE;cACnC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,cAAc;kBACzB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;kBAC/C,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;kBACnC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;eAC3D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;cAC/B,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;kBAC/C,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,KAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;kBAChE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,aAAa,CAAC;eACvD,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;cAC/B,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;kBAC/C,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,KAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;kBAChE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,aAAa,CAAC;eACvD,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE;cAC/B,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC;kBACnD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;kBAC/B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;eAC3D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;OACJ;;;;;;;;MASD,wCAAsB,GAAtB;UAAA,iBAyDC;;UAvDC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE;cACvC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,4CAA4C;kBACtD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU;kBACnC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,GAAG,CAAC;eAC9D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;cACtC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,2CAA2C;kBACrD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU;kBACnC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,KAAK,CAAC;eAChE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;cACtC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,2CAA2C;kBACrD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa;kBACtC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,MAAM,CAAC;eACjE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;;UAGH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;cACpC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;kBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS;kBAClC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,MAAM,CAAC;eAClE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE;cACrC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC;kBACrD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU;kBACnC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,OAAO,CAAC;eACnE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;cACpC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC;kBACvD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS;kBAClC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,MAAM,CAAC;eAClE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;;UAGH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;cACtC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;kBAChD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;kBAC/B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;eAC9D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;OACJ;MAED,uCAAqB,GAArB;UAAA,iBAgBC;UAfC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,EAAE;cACzC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;kBAC/C,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;kBAC5B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;eAC3D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE;cACjC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;kBACjD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;kBAC9B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC;eACzD,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;OACJ;;;;;;;MAQD,wCAAsB,GAAtB;UAAA,iBAyDC;UAxDC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE;cACnC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,QAAQ;kBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC;kBACnD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;kBACpC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,KAAK,CAAC;eAChE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE;cACrC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,QAAQ;kBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC;kBACnD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;kBACpC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,QAAQ,CAAC;eACnE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE;cACrC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,QAAQ;kBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;kBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU;kBACnC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,MAAM,CAAC;eACjE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;cACtC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,QAAQ;kBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC;kBACnD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;kBACpC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,EAAE,OAAO,CAAC;eAClE,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;cACpC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,QAAQ;kBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;kBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;kBAC/B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;eAC5D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE;cACpC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,QAAQ;kBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC;kBACpD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;kBAC/B,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;eAC5D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;UACH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;cACtC,OAAO,KAAI,CAAC,MAAM,CAAC;kBACjB,SAAS,EAAE,QAAQ;kBACnB,QAAQ,EAAE,KAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;kBAChD,OAAO,EAAE,KAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ;kBACjC,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;eAC9D,CAAC,CAAC,MAAM,EAAE,CAAC;WACb,CAAC,CAAC;OACJ;MAED,uBAAK,GAAL,UAAM,UAAU,EAAE,MAAM;UACtB,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,QAAQ,GAAG,QAAQ,EAAE,QAAQ,EAAE,EAAE;cAChF,IAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;cAC/B,IAAM,SAAS,GAAGA,GAAC,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;cACtD,IAAM,OAAO,GAAGA,GAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;cAE5F,IAAM,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC;kBACjC,SAAS,EAAE,OAAO,GAAG,SAAS;eAC/B,CAAC,CAAC,MAAM,EAAE,CAAC;cAEZ,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;kBACxD,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;kBACxD,IAAI,GAAG,EAAE;sBACP,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,KAAK,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC;mBACpE;eACF;cACD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;WAC7B;OACF;;;;MAKD,oCAAkB,GAAlB,UAAmB,UAAU;UAA7B,iBA6DC;UA5DC,IAAM,KAAK,GAAG,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC;UAE1C,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;UAC7D,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;cAC1B,gBAAgB,EAAE;kBAChB,OAAO,SAAS,CAAC,WAAW,CAAC,KAAK,MAAM,CAAC;eAC1C;cACD,kBAAkB,EAAE;kBAClB,OAAO,SAAS,CAAC,aAAa,CAAC,KAAK,QAAQ,CAAC;eAC9C;cACD,qBAAqB,EAAE;kBACrB,OAAO,SAAS,CAAC,gBAAgB,CAAC,KAAK,WAAW,CAAC;eACpD;cACD,qBAAqB,EAAE;kBACrB,OAAO,SAAS,CAAC,gBAAgB,CAAC,KAAK,WAAW,CAAC;eACpD;cACD,uBAAuB,EAAE;kBACvB,OAAO,SAAS,CAAC,kBAAkB,CAAC,KAAK,aAAa,CAAC;eACxD;cACD,yBAAyB,EAAE;kBACzB,OAAO,SAAS,CAAC,oBAAoB,CAAC,KAAK,eAAe,CAAC;eAC5D;WACF,CAAC,CAAC;UAEH,IAAI,SAAS,CAAC,aAAa,CAAC,EAAE;cAC5B,IAAM,SAAS,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAC,IAAI;kBAC7D,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;uBAC/B,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;uBACnB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;eACxB,CAAC,CAAC;cACH,IAAM,UAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;cAExE,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;kBAChD,IAAM,KAAK,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC;;kBAEtB,IAAM,SAAS,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,UAAQ,GAAG,EAAE,CAAC,CAAC;kBACjE,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;eACzC,CAAC,CAAC;cACH,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,UAAQ,CAAC,CAAC,GAAG,CAAC,aAAa,EAAE,UAAQ,CAAC,CAAC;WAClF;UAED,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE;cAC1B,IAAM,UAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC;cACxC,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;kBAChD,IAAM,KAAK,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC;;kBAEtB,IAAM,SAAS,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,UAAQ,GAAG,EAAE,CAAC,CAAC;kBACjE,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;eACzC,CAAC,CAAC;cACH,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,UAAQ,CAAC,CAAC;WACrD;UAED,IAAI,SAAS,CAAC,aAAa,CAAC,EAAE;cAC5B,IAAM,YAAU,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC;cAC5C,KAAK,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;;kBAEtD,IAAM,SAAS,GAAG,CAACA,GAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,YAAU,GAAG,EAAE,CAAC,CAAC;kBACrE,KAAI,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC;eAC7C,CAAC,CAAC;WACJ;OACF;MAED,iCAAe,GAAf,UAAgB,UAAU,EAAE,KAAK;UAAjC,iBAIC;UAHCA,GAAC,CAAC,IAAI,CAAC,KAAK,EAAE,UAAC,QAAQ,EAAE,IAAI;cAC3B,KAAI,CAAC,EAAE,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;WAC5D,CAAC,CAAC;OACJ;MAED,kCAAgB,GAAhB,UAAiB,KAAK;UACpB,IAAM,SAAS,GAAG,EAAE,CAAC;UACrB,IAAM,OAAO,GAAGA,GAAC,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;UAC3C,IAAM,iBAAiB,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;UACzC,IAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;UACrE,IAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;UACxE,IAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;UAE5E,IAAI,SAAS,CAAC;;UAEd,IAAI,KAAK,CAAC,OAAO,KAAK,SAAS,EAAE;cAC/B,IAAM,UAAU,GAAGA,GAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;cAC5C,SAAS,GAAG;kBACV,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI;kBAChC,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG;eAChC,CAAC;WACH;eAAM;cACL,SAAS,GAAG;kBACV,CAAC,EAAE,KAAK,CAAC,OAAO;kBAChB,CAAC,EAAE,KAAK,CAAC,OAAO;eACjB,CAAC;WACH;UAED,IAAM,GAAG,GAAG;cACV,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;cAC1C,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;WAC3C,CAAC;UAEF,YAAY,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;UAChE,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;UAE5C,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,EAAE;cAC5D,cAAc,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;WACjD;UAED,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,EAAE;cAC5D,cAAc,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;WAClD;UAED,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;OAC/C;MACH,cAAC;EAAD,CAAC,IAAA;;ECh4BD;MACE,iBAAY,OAAO;UACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UAEvB,IAAI,CAAC,OAAO,GAAGA,GAAC,CAAC,MAAM,CAAC,CAAC;UACzB,IAAI,CAAC,SAAS,GAAGA,GAAC,CAAC,QAAQ,CAAC,CAAC;UAE7B,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;UAC1B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;UACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;UACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;UAC3C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAE/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;OAClD;MAED,kCAAgB,GAAhB;UACE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;OAC9B;MAED,4BAAU,GAAV;UAAA,iBAuBC;UAtBC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;UAElD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE;cAChC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;WACtB;eAAM;cACL,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;WAC3E;UAED,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;cACjC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;WACvD;UAED,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;UAE5B,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,uDAAuD,EAAE;cACrE,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;WACnD,CAAC,CAAC;UAEH,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;UAClD,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;cACjC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;WACrD;OACF;MAED,yBAAO,GAAP;UACE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC;UAElC,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;cACjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;WACtD;OACF;MAED,8BAAY,GAAZ;UACE,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;cACvC,OAAO,KAAK,CAAC;WACd;UAED,IAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;UACtE,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;UAChD,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;UAEzC,IAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;UAC7C,eAAe,CAAC,GAAG,CAAC;cAClB,MAAM,EAAE,aAAa;WACtB,CAAC,CAAC;;UAGH,IAAI,cAAc,GAAG,CAAC,CAAC;UACvB,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;cAC/B,cAAc,GAAGA,GAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAC;WAC/D;UAED,IAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;UACjD,IAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC;UAClD,IAAM,kBAAkB,GAAG,eAAe,GAAG,YAAY,CAAC;UAC1D,IAAM,cAAc,GAAG,eAAe,GAAG,cAAc,CAAC;UACxD,IAAM,sBAAsB,GAAG,kBAAkB,GAAG,cAAc,GAAG,aAAa,CAAC;UAEnF,IAAI,CAAC,aAAa,GAAG,cAAc,MAAM,aAAa,GAAG,sBAAsB,CAAC,EAAE;cAChF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;kBAChB,QAAQ,EAAE,OAAO;kBACjB,GAAG,EAAE,cAAc;kBACnB,KAAK,EAAE,WAAW;eACnB,CAAC,CAAC;WACJ;eAAM;cACL,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;kBAChB,QAAQ,EAAE,UAAU;kBACpB,GAAG,EAAE,CAAC;kBACN,KAAK,EAAE,MAAM;eACd,CAAC,CAAC;WACJ;OACF;MAED,iCAAe,GAAf,UAAgB,YAAY;UAC1B,IAAI,YAAY,EAAE;cAChB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;WACvC;eAAM;cACL,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;kBACjC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;eACvD;WACF;OACF;MAED,kCAAgB,GAAhB,UAAiB,YAAY;UAC3B,IAAI,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,YAAY,CAAC,CAAC;UAE7E,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;OACpC;MAED,gCAAc,GAAd,UAAe,UAAU;UACvB,IAAI,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,UAAU,CAAC,CAAC;UACzE,IAAI,UAAU,EAAE;cACd,IAAI,CAAC,UAAU,EAAE,CAAC;WACnB;eAAM;cACL,IAAI,CAAC,QAAQ,EAAE,CAAC;WACjB;OACF;MAED,0BAAQ,GAAR,UAAS,iBAAiB;UACxB,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;UACxC,IAAI,CAAC,iBAAiB,EAAE;cACtB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;WAClC;UACD,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;OAC/B;MAED,4BAAU,GAAV,UAAW,iBAAiB;UAC1B,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;UACxC,IAAI,CAAC,iBAAiB,EAAE;cACtB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;WAClC;UACD,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;OAChC;MACH,cAAC;EAAD,CAAC,IAAA;;ECnID;MACE,oBAAY,OAAO;UACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UAEvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;UAC1B,IAAI,CAAC,KAAK,GAAGA,GAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;UAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;UACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;UAElC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;OACrF;MAED,+BAAU,GAAV;UACE,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;UAE1E,IAAM,IAAI,GAAG;cACX,0CAA0C;cAC1C,sCAAkC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,aAAU;cACxE,wFAAwF;cACxF,QAAQ;cACR,0CAA0C;cAC1C,sCAAkC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,aAAU;cAC9D,uGAAuG;cACvG,QAAQ;cACR,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB;oBAC3BA,GAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;sBACpC,SAAS,EAAE,gCAAgC;sBAC3C,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe;sBACpC,OAAO,EAAE,IAAI;mBACd,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE;oBACjB,EAAE;WACP,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;UAEX,IAAM,WAAW,GAAG,yDAAyD,CAAC;UAC9E,IAAM,MAAM,GAAG,+CAAwC,WAAW,mBAAY,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,iBAAa,CAAC;UAEjH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC;cAC5B,SAAS,EAAE,aAAa;cACxB,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;cAC5B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;cAC9B,IAAI,EAAE,IAAI;cACV,MAAM,EAAE,MAAM;WACf,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;OAClC;MAED,4BAAO,GAAP;UACE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;UACjC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;OACvB;MAED,iCAAY,GAAZ,UAAa,MAAM,EAAE,IAAI;UACvB,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,UAAC,KAAK;cAC1B,IAAI,KAAK,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE;kBACpC,KAAK,CAAC,cAAc,EAAE,CAAC;kBACvB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;eACvB;WACF,CAAC,CAAC;OACJ;;;;MAKD,kCAAa,GAAb,UAAc,QAAQ,EAAE,SAAS,EAAE,QAAQ;UACzC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,EAAE,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;OAChE;;;;;;;MAQD,mCAAc,GAAd,UAAe,QAAQ;UAAvB,iBAiFC;UAhFC,OAAOA,GAAC,CAAC,QAAQ,CAAC,UAAC,QAAQ;cACzB,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;cACvD,IAAM,QAAQ,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;cACrD,IAAM,QAAQ,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;cACrD,IAAM,gBAAgB,GAAG,KAAI,CAAC,OAAO;mBAClC,IAAI,CAAC,sDAAsD,CAAC,CAAC;cAEhE,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAI,CAAC,OAAO,EAAE;kBAClC,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;;kBAG1C,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;sBACjB,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC;mBAC9B;kBAED,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;kBAE7B,IAAM,oBAAoB,GAAG;sBAC3B,KAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;;;sBAGlD,QAAQ,CAAC,IAAI,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC;mBACjC,CAAC;kBAEF,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;sBACtD,UAAU,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;mBACrC,CAAC,CAAC;kBAEH,IAAM,mBAAmB,GAAG;sBAC1B,KAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;;;sBAGlD,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;0BAClB,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;uBAC/B;mBACF,CAAC;kBAEF,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;sBACpD,UAAU,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;mBACpC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;kBAErB,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE;sBACvB,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;mBAC3B;kBAED,KAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;kBAClD,KAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;kBACtC,KAAI,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;kBAEvC,IAAM,kBAAkB,GAAG,QAAQ,CAAC,WAAW,KAAK,SAAS;wBACzD,QAAQ,CAAC,WAAW,GAAG,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC;kBAEhE,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;kBAErD,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,UAAC,KAAK;sBAC1B,KAAK,CAAC,cAAc,EAAE,CAAC;sBAEvB,QAAQ,CAAC,OAAO,CAAC;0BACf,KAAK,EAAE,QAAQ,CAAC,KAAK;0BACrB,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE;0BACnB,IAAI,EAAE,SAAS,CAAC,GAAG,EAAE;0BACrB,WAAW,EAAE,gBAAgB,CAAC,EAAE,CAAC,UAAU,CAAC;uBAC7C,CAAC,CAAC;sBACH,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;mBAClC,CAAC,CAAC;eACJ,CAAC,CAAC;cAEH,KAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAI,CAAC,OAAO,EAAE;;kBAEnC,SAAS,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;kBACtC,QAAQ,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;kBACrC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;kBAEtB,IAAI,QAAQ,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;sBAClC,QAAQ,CAAC,MAAM,EAAE,CAAC;mBACnB;eACF,CAAC,CAAC;cAEH,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;WAClC,CAAC,CAAC,OAAO,EAAE,CAAC;OACd;;;;MAKD,yBAAI,GAAJ;UAAA,iBAUC;UATC,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;UAE3D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;UACxC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAC,QAAQ;cAC1C,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;cAC3C,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC;WACpD,CAAC,CAAC,IAAI,CAAC;cACN,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;WAC5C,CAAC,CAAC;OACJ;MACH,iBAAC;EAAD,CAAC,IAAA;;EC1KD;MACE,qBAAY,OAAO;UAAnB,iBAaC;UAZC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UAEvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;UAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,MAAM,GAAG;cACZ,yEAAyE,EAAE;kBACzE,KAAI,CAAC,MAAM,EAAE,CAAC;eACf;cACD,4CAA4C,EAAE;kBAC5C,KAAI,CAAC,IAAI,EAAE,CAAC;eACb;WACF,CAAC;OACH;MAED,sCAAgB,GAAhB;UACE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;OAClD;MAED,gCAAU,GAAV;UACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;cAC9B,SAAS,EAAE,mBAAmB;cAC9B,QAAQ,EAAE,UAAC,KAAK;kBACd,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;kBACtE,QAAQ,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC;eAChE;WACF,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;UAC7C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;UAE9E,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;OAC3E;MAED,6BAAO,GAAP;UACE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;OACxB;MAED,4BAAM,GAAN;;UAEE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;cAC3C,IAAI,CAAC,IAAI,EAAE,CAAC;cACZ,OAAO;WACR;UAED,IAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;UACtD,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,UAAU,EAAE,EAAE;cACzC,IAAM,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;cAClD,IAAM,IAAI,GAAGA,GAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;cACpC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;cAEtD,IAAM,GAAG,GAAG,GAAG,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;cAC3C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;kBAChB,OAAO,EAAE,OAAO;kBAChB,IAAI,EAAE,GAAG,CAAC,IAAI;kBACd,GAAG,EAAE,GAAG,CAAC,GAAG;eACb,CAAC,CAAC;WACJ;eAAM;cACL,IAAI,CAAC,IAAI,EAAE,CAAC;WACb;OACF;MAED,0BAAI,GAAJ;UACE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;OACtB;MACH,kBAAC;EAAD,CAAC,IAAA;;EChED;MACE,qBAAY,OAAO;UACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UACvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;UAC1B,IAAI,CAAC,KAAK,GAAGA,GAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;UAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;UACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;OACnC;MAED,gCAAU,GAAV;UACE,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;UAE1E,IAAI,eAAe,GAAG,EAAE,CAAC;UACzB,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;cACrC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;cACtF,IAAM,YAAY,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;kBAC3E,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;cAC9C,eAAe,GAAG,aAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK,GAAG,YAAY,cAAU,CAAC;WAC9F;UAED,IAAM,IAAI,GAAG;cACX,uEAAuE;cACvE,iCAAiC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,UAAU;cAChF,+DAA+D;cAC/D,mEAAmE;cACnE,eAAe;cACf,QAAQ;cACR,sEAAsE;cACtE,iCAAiC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,UAAU;cACpE,yEAAyE;cACzE,4BAA4B;cAC5B,QAAQ;WACT,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;UACX,IAAM,WAAW,GAAG,0DAA0D,CAAC;UAC/E,IAAM,MAAM,GAAG,+CAAwC,WAAW,mBAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,iBAAa,CAAC;UAElH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC;cAC5B,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;cAC7B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;cAC9B,IAAI,EAAE,IAAI;cACV,MAAM,EAAE,MAAM;WACf,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;OAClC;MAED,6BAAO,GAAP;UACE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;UACjC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;OACvB;MAED,kCAAY,GAAZ,UAAa,MAAM,EAAE,IAAI;UACvB,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,UAAC,KAAK;cAC1B,IAAI,KAAK,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE;kBACpC,KAAK,CAAC,cAAc,EAAE,CAAC;kBACvB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;eACvB;WACF,CAAC,CAAC;OACJ;MAED,0BAAI,GAAJ;UAAA,iBA0BC;UAzBC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;UACxC,IAAI,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,UAAC,IAAI;;cAE/B,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;cACjC,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;cAE3C,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;;kBAE5B,IAAI,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,iBAAiB,EAAE;sBAC5C,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;mBACtD;uBAAM;sBACL,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;mBACjD;eACF;mBAAM;;kBAEL,IAAI,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC,aAAa,EAAE;sBACxC,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;mBACjD;uBAAM;;sBAEL,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,8BAA8B,EAAE,IAAI,CAAC,CAAC;mBAC3D;eACF;WACF,CAAC,CAAC,IAAI,CAAC;cACN,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;WAC5C,CAAC,CAAC;OACJ;;;;;;;MAQD,qCAAe,GAAf;UAAA,iBA2CC;UA1CC,OAAOA,GAAC,CAAC,QAAQ,CAAC,UAAC,QAAQ;cACzB,IAAM,WAAW,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;cAC3D,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;cACvD,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;cAEvD,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAI,CAAC,OAAO,EAAE;kBAClC,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;;kBAG1C,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAC,KAAK;sBAC7D,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;mBAC5D,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;kBAEZ,SAAS,CAAC,KAAK,CAAC,UAAC,KAAK;sBACpB,KAAK,CAAC,cAAc,EAAE,CAAC;sBAEvB,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;mBACnC,CAAC,CAAC;kBAEH,SAAS,CAAC,EAAE,CAAC,aAAa,EAAE;sBAC1B,IAAM,GAAG,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC;sBAC5B,KAAI,CAAC,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;mBACnC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;kBAEX,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE;sBACvB,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;mBAC5B;kBACD,KAAI,CAAC,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;eACzC,CAAC,CAAC;cAEH,KAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAI,CAAC,OAAO,EAAE;kBACnC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;kBAC1B,SAAS,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;kBACtC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;kBAEvB,IAAI,QAAQ,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;sBAClC,QAAQ,CAAC,MAAM,EAAE,CAAC;mBACnB;eACF,CAAC,CAAC;cAEH,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;WAClC,CAAC,CAAC;OACJ;MACH,kBAAC;EAAD,CAAC,IAAA;;ECzID;;;;;EAKA;MACE,sBAAY,OAAO;UAAnB,iBAYC;UAXC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UACvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;UAE1B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;UAC/C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAE/B,IAAI,CAAC,MAAM,GAAG;cACZ,oBAAoB,EAAE;kBACpB,KAAI,CAAC,IAAI,EAAE,CAAC;eACb;WACF,CAAC;OACH;MAED,uCAAgB,GAAhB;UACE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;OACnD;MAED,iCAAU,GAAV;UACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;cAC9B,SAAS,EAAE,oBAAoB;WAChC,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;UAC7C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;UAC9E,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;OAC5E;MAED,8BAAO,GAAP;UACE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;OACxB;MAED,6BAAM,GAAN,UAAO,MAAM;UACX,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;cACrB,IAAM,GAAG,GAAG,GAAG,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;cAC3C,IAAM,SAAS,GAAG,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;cACxD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;kBAChB,OAAO,EAAE,OAAO;kBAChB,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC,IAAI;kBAC3D,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,CAAC;eAC9E,CAAC,CAAC;WACJ;eAAM;cACL,IAAI,CAAC,IAAI,EAAE,CAAC;WACb;OACF;MAED,2BAAI,GAAJ;UACE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;OACtB;MACH,mBAAC;EAAD,CAAC,IAAA;;ECpDD;MACE,sBAAY,OAAO;UAAnB,iBAgBC;UAfC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UAEvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;UAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,MAAM,GAAG;cACZ,sBAAsB,EAAE,UAAC,EAAE,EAAE,CAAC;kBAC5B,KAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;eACvB;cACD,sDAAsD,EAAE;kBACtD,KAAI,CAAC,MAAM,EAAE,CAAC;eACf;cACD,oBAAoB,EAAE;kBACpB,KAAI,CAAC,IAAI,EAAE,CAAC;eACb;WACF,CAAC;OACH;MAED,uCAAgB,GAAhB;UACE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;OACnD;MAED,iCAAU,GAAV;UACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;cAC9B,SAAS,EAAE,oBAAoB;WAChC,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;UAC7C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;UAE9E,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;UAG3E,IAAI,GAAG,CAAC,IAAI,EAAE;cACZ,QAAQ,CAAC,WAAW,CAAC,0BAA0B,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;WAChE;OACF;MAED,8BAAO,GAAP;UACE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;OACxB;MAED,6BAAM,GAAN,UAAO,MAAM;UACX,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;cAC7B,OAAO,KAAK,CAAC;WACd;UAED,IAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;UAElC,IAAI,MAAM,EAAE;cACV,IAAM,GAAG,GAAG,GAAG,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;cAC3C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;kBAChB,OAAO,EAAE,OAAO;kBAChB,IAAI,EAAE,GAAG,CAAC,IAAI;kBACd,GAAG,EAAE,GAAG,CAAC,GAAG;eACb,CAAC,CAAC;WACJ;eAAM;cACL,IAAI,CAAC,IAAI,EAAE,CAAC;WACb;UAED,OAAO,MAAM,CAAC;OACf;MAED,2BAAI,GAAJ;UACE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;OACtB;MACH,mBAAC;EAAD,CAAC,IAAA;;EClED;MACE,qBAAY,OAAO;UACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UAEvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;UAC1B,IAAI,CAAC,KAAK,GAAGA,GAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;UAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;UACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;OACnC;MAED,gCAAU,GAAV;UACE,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;UAE1E,IAAM,IAAI,GAAG;cACX,oDAAoD;cACpD,sCAAkC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,qCAA8B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,qBAAkB;cAC9H,wFAAwF;cACxF,QAAQ;WACT,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;UACX,IAAM,WAAW,GAAG,0DAA0D,CAAC;UAC/E,IAAM,MAAM,GAAG,+CAAwC,WAAW,mBAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,iBAAa,CAAC;UAElH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC;cAC5B,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM;cAC7B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;cAC9B,IAAI,EAAE,IAAI;cACV,MAAM,EAAE,MAAM;WACf,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;OAClC;MAED,6BAAO,GAAP;UACE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;UACjC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;OACvB;MAED,kCAAY,GAAZ,UAAa,MAAM,EAAE,IAAI;UACvB,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,UAAC,KAAK;cAC1B,IAAI,KAAK,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE;kBACpC,KAAK,CAAC,cAAc,EAAE,CAAC;kBACvB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;eACvB;WACF,CAAC,CAAC;OACJ;MAED,qCAAe,GAAf,UAAgB,GAAG;;UAEjB,IAAM,QAAQ,GAAG,sHAAsH,CAAC;UACxI,IAAM,gBAAgB,GAAG,qCAAqC,CAAC;UAC/D,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;UAEpC,IAAM,QAAQ,GAAG,oDAAoD,CAAC;UACtE,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;UAEpC,IAAM,OAAO,GAAG,iCAAiC,CAAC;UAClD,IAAM,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;UAElC,IAAM,SAAS,GAAG,mDAAmD,CAAC;UACtE,IAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;UAEtC,IAAM,QAAQ,GAAG,gEAAgE,CAAC;UAClF,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;UAEpC,IAAM,WAAW,GAAG,6CAA6C,CAAC;UAClE,IAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;UAE1C,IAAM,QAAQ,GAAG,2BAA2B,CAAC;UAC7C,IAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;UAEpC,IAAM,SAAS,GAAG,2DAA2D,CAAC;UAC9E,IAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;UAEtC,IAAM,SAAS,GAAG,gBAAgB,CAAC;UACnC,IAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;UAEtC,IAAM,SAAS,GAAG,gBAAgB,CAAC;UACnC,IAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;UAEtC,IAAM,UAAU,GAAG,aAAa,CAAC;UACjC,IAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;UAExC,IAAI,MAAM,CAAC;UACX,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,EAAE,EAAE;cACvC,IAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;cAC7B,IAAI,KAAK,GAAG,CAAC,CAAC;cACd,IAAI,OAAO,OAAO,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;kBACrC,IAAM,eAAe,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;kBAC3D,IAAI,eAAe,EAAE;sBACnB,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;0BAC3D,KAAK,KAAK,OAAO,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;uBAC5G;mBACF;eACF;cACD,MAAM,GAAGA,GAAC,CAAC,UAAU,CAAC;mBACnB,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;mBACtB,IAAI,CAAC,KAAK,EAAE,0BAA0B,GAAG,SAAS,IAAI,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC;mBAC1F,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;WAC/C;eAAM,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;cACvC,MAAM,GAAGA,GAAC,CAAC,UAAU,CAAC;mBACnB,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;mBACtB,IAAI,CAAC,KAAK,EAAE,0BAA0B,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;mBAChE,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;mBAC1C,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;mBACvB,IAAI,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;WACtC;eAAM,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;cACrC,MAAM,GAAGA,GAAC,CAAC,UAAU,CAAC;mBACnB,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;mBACtB,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;mBACxC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;mBAC1C,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;WAChC;eAAM,IAAI,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;cACzC,MAAM,GAAGA,GAAC,CAAC,mEAAmE,CAAC;mBAC5E,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;mBACtB,IAAI,CAAC,KAAK,EAAE,2BAA2B,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;mBACtD,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;WAC/C;eAAM,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;cACvC,MAAM,GAAGA,GAAC,CAAC,UAAU,CAAC;mBACnB,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;mBACtB,IAAI,CAAC,KAAK,EAAE,oCAAoC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;mBAC9D,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;WAC/C;eAAM,IAAI,UAAU,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;cAC7C,MAAM,GAAGA,GAAC,CAAC,mEAAmE,CAAC;mBAC5E,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;mBACtB,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;mBACrB,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC;mBACpB,IAAI,CAAC,KAAK,EAAE,2BAA2B,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;WAC7D;eAAM,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,MAAM,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE;cAC7E,IAAM,GAAG,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;cACxE,MAAM,GAAGA,GAAC,CAAC,mEAAmE,CAAC;mBAC5E,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;mBACtB,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC;mBACrB,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC;mBACpB,IAAI,CAAC,KAAK,EAAE,yCAAyC,GAAG,GAAG,GAAG,aAAa,CAAC,CAAC;WACjF;eAAM,IAAI,QAAQ,IAAI,QAAQ,IAAI,SAAS,EAAE;cAC5C,MAAM,GAAGA,GAAC,CAAC,kBAAkB,CAAC;mBAC3B,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC;mBAChB,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;WAC/C;eAAM;;cAEL,OAAO,KAAK,CAAC;WACd;UAED,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;UAEnC,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;OAClB;MAED,0BAAI,GAAJ;UAAA,iBAkBC;UAjBC,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;UAC3D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;UACxC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAC,GAAG;;cAElC,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;cACjC,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;;cAG3C,IAAM,KAAK,GAAG,KAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;cAExC,IAAI,KAAK,EAAE;;kBAET,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;eACjD;WACF,CAAC,CAAC,IAAI,CAAC;cACN,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;WAC5C,CAAC,CAAC;OACJ;;;;;;;MAQD,qCAAe,GAAf,UAAgB,IAAI;UAApB,iBAoCC;UAnCC,OAAOA,GAAC,CAAC,QAAQ,CAAC,UAAC,QAAQ;cACzB,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;cACvD,IAAM,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;cAEvD,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAI,CAAC,OAAO,EAAE;kBAClC,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;kBAE1C,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE;sBAC9B,KAAI,CAAC,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;mBAC/C,CAAC,CAAC;kBAEH,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE;sBACvB,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;mBAC5B;kBAED,SAAS,CAAC,KAAK,CAAC,UAAC,KAAK;sBACpB,KAAK,CAAC,cAAc,EAAE,CAAC;sBAEvB,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;mBACnC,CAAC,CAAC;kBAEH,KAAI,CAAC,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;eACzC,CAAC,CAAC;cAEH,KAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAI,CAAC,OAAO,EAAE;kBACnC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;kBACvB,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;kBAEvB,IAAI,QAAQ,CAAC,KAAK,EAAE,KAAK,SAAS,EAAE;sBAClC,QAAQ,CAAC,MAAM,EAAE,CAAC;mBACnB;eACF,CAAC,CAAC;cAEH,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;WAClC,CAAC,CAAC;OACJ;MACH,kBAAC;EAAD,CAAC,IAAA;;ECnND;MACE,oBAAY,OAAO;UACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UAEvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;UAC1B,IAAI,CAAC,KAAK,GAAGA,GAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;UAC9B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC;UACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;OACnC;MAED,+BAAU,GAAV;UACE,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC;UAE1E,IAAM,IAAI,GAAG;cACX,yBAAyB;cACzB,2EAAgF;cAChF,mFAAmF;cACnF,sFAAsF;cACtF,MAAM;WACP,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;UAEX,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC;cAC5B,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;cAC7B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;cAC9B,IAAI,EAAE,IAAI,CAAC,kBAAkB,EAAE;cAC/B,MAAM,EAAE,IAAI;cACZ,QAAQ,EAAE,UAAC,KAAK;kBACd,KAAK,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC,GAAG,CAAC;sBAC7C,YAAY,EAAE,GAAG;sBACjB,UAAU,EAAE,QAAQ;mBACrB,CAAC,CAAC;eACJ;WACF,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;OAClC;MAED,4BAAO,GAAP;UACE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;UACjC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;OACvB;MAED,uCAAkB,GAAlB;UAAA,iBAWC;UAVC,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;UAC7D,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,UAAC,GAAG;cACjC,IAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;cAC5B,IAAM,IAAI,GAAGA,GAAC,CAAC,0CAA0C,CAAC,CAAC;cAC3D,IAAI,CAAC,MAAM,CAACA,GAAC,CAAC,cAAc,GAAG,GAAG,GAAG,gBAAgB,CAAC,CAAC,GAAG,CAAC;kBACzD,OAAO,EAAE,GAAG;kBACZ,cAAc,EAAE,EAAE;eACnB,CAAC,CAAC,CAAC,MAAM,CAACA,GAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC;cAC/E,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;WACpB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;OACb;;;;;;MAOD,mCAAc,GAAd;UAAA,iBAQC;UAPC,OAAOA,GAAC,CAAC,QAAQ,CAAC,UAAC,QAAQ;cACzB,KAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAI,CAAC,OAAO,EAAE;kBAClC,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;kBAC1C,QAAQ,CAAC,OAAO,EAAE,CAAC;eACpB,CAAC,CAAC;cACH,KAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;WAClC,CAAC,CAAC,OAAO,EAAE,CAAC;OACd;MAED,yBAAI,GAAJ;UAAA,iBAKC;UAJC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;UACxC,IAAI,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC;cACzB,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;WAC5C,CAAC,CAAC;OACJ;MACH,iBAAC;EAAD,CAAC,IAAA;;ECxED,IAAM,yBAAyB,GAAG,EAAE,CAAC;EAErC;MACE,oBAAY,OAAO;UAAnB,iBAuBC;UAtBC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UACvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;UAC1B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,MAAM,GAAG;cACZ,uDAAuD,EAAE;kBACvD,KAAI,CAAC,MAAM,EAAE,CAAC;eACf;cACD,8DAA8D,EAAE;kBAC9D,KAAI,CAAC,IAAI,EAAE,CAAC;eACb;cACD,qBAAqB,EAAE,UAAC,EAAE,EAAE,CAAC;;;kBAG3B,IAAI,GAAG,CAAC,IAAI,EAAE;sBACZ,OAAO;mBACR;kBAED,IAAI,CAAC,CAAC,CAAC,aAAa,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;sBACjF,KAAI,CAAC,IAAI,EAAE,CAAC;mBACb;eACF;WACF,CAAC;OACH;MAED,qCAAgB,GAAhB;UACE,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;OACzE;MAED,+BAAU,GAAV;UACE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;cAC9B,SAAS,EAAE,kBAAkB;WAC9B,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;UAC7C,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;UAExD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;OAC1E;MAED,4BAAO,GAAP;UACE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;OACxB;MAED,2BAAM,GAAN;UACE,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;UAC7D,IAAI,SAAS,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE;cACrD,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC;cAC1D,IAAI,IAAI,EAAE;kBACR,IAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;kBAChC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;sBAChB,OAAO,EAAE,OAAO;sBAChB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,yBAAyB;sBACvE,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM;mBAC1B,CAAC,CAAC;kBACH,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,4BAA4B,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;eAClE;WACF;eAAM;cACL,IAAI,CAAC,IAAI,EAAE,CAAC;WACb;OACF;MAED,yBAAI,GAAJ;UACE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;OACtB;MACH,iBAAC;EAAD,CAAC,IAAA;;ECjED,IAAM,YAAY,GAAG,CAAC,CAAC;EAEvB;MACE,qBAAY,OAAO;UAAnB,iBAuBC;UAtBC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UAEvB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;UAC1B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC;UAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;UAC/B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;UACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC;UACxD,IAAI,CAAC,KAAK,GAAGA,GAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;UAE5D,IAAI,CAAC,MAAM,GAAG;cACZ,kBAAkB,EAAE,UAAC,EAAE,EAAE,CAAC;kBACxB,IAAI,CAAC,CAAC,CAAC,kBAAkB,EAAE,EAAE;sBAC3B,KAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;mBACrB;eACF;cACD,oBAAoB,EAAE,UAAC,EAAE,EAAE,CAAC;kBAC1B,KAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;eACvB;cACD,4CAA4C,EAAE;kBAC5C,KAAI,CAAC,IAAI,EAAE,CAAC;eACb;WACF,CAAC;OACH;MAED,sCAAgB,GAAhB;UACE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;OAC9B;MAED,gCAAU,GAAV;UAAA,iBAeC;UAdC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;UAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;cAC9B,SAAS,EAAE,mBAAmB;cAC9B,SAAS,EAAE,IAAI;cACf,SAAS,EAAE,EAAE;WACd,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;UAE7C,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;UACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;UAC7E,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE,UAAC,CAAC;cAC7C,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;cACpDA,GAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;cACtC,KAAI,CAAC,OAAO,EAAE,CAAC;WAChB,CAAC,CAAC;OACJ;MAED,6BAAO,GAAP;UACE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;OACxB;MAED,gCAAU,GAAV,UAAW,KAAK;UACd,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;UACpD,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;UAEzB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC;OACrF;MAED,8BAAQ,GAAR;UACE,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;UAC9D,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;UAE9B,IAAI,KAAK,CAAC,MAAM,EAAE;cAChB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;WACxB;eAAM;cACL,IAAI,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;cAE1C,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;kBACtB,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,KAAK,EAAE,CAAC;eAC7D;cAED,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;WAC7D;OACF;MAED,4BAAM,GAAN;UACE,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;UAC9D,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;UAE9B,IAAI,KAAK,CAAC,MAAM,EAAE;cAChB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;WACxB;eAAM;cACL,IAAI,UAAU,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;cAE1C,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;kBACtB,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,EAAE,CAAC;eAC5D;cAED,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;WAC5D;OACF;MAED,6BAAO,GAAP;UACE,IAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;UAE3D,IAAI,KAAK,CAAC,MAAM,EAAE;cAChB,IAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;;cAEtC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;cACpC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,CAAC;cAE/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;cAC1B,IAAI,CAAC,IAAI,EAAE,CAAC;cACZ,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;cAC9E,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;WACrC;OACF;MAED,kCAAY,GAAZ,UAAa,KAAK;UAChB,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;UAC7C,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;UAChC,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;UACpD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;cAC5B,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;WAC7B;UACD,OAAO,IAAI,CAAC;OACb;MAED,yCAAmB,GAAnB,UAAoB,OAAO,EAAE,KAAK;UAChC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;UACjC,OAAO,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,EAAE,GAAG;cACzB,IAAM,KAAK,GAAGA,GAAC,CAAC,+BAA+B,CAAC,CAAC;cACjD,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;cAC9D,KAAK,CAAC,IAAI,CAAC;kBACT,OAAO,EAAE,OAAO;kBAChB,MAAM,EAAE,IAAI;eACb,CAAC,CAAC;cACH,OAAO,KAAK,CAAC;WACd,CAAC,CAAC;OACJ;MAED,mCAAa,GAAb,UAAc,CAAC;UACb,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE;cACjC,OAAO;WACR;UAED,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE;cAChC,CAAC,CAAC,cAAc,EAAE,CAAC;cACnB,IAAI,CAAC,OAAO,EAAE,CAAC;WAChB;eAAM,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;cACpC,CAAC,CAAC,cAAc,EAAE,CAAC;cACnB,IAAI,CAAC,MAAM,EAAE,CAAC;WACf;eAAM,IAAI,CAAC,CAAC,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;cACtC,CAAC,CAAC,cAAc,EAAE,CAAC;cACnB,IAAI,CAAC,QAAQ,EAAE,CAAC;WACjB;OACF;MAED,mCAAa,GAAb,UAAc,KAAK,EAAE,OAAO,EAAE,QAAQ;UACpC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;UAC/B,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;cACnD,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;cACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;WACnC;eAAM;cACL,QAAQ,EAAE,CAAC;WACZ;OACF;MAED,iCAAW,GAAX,UAAY,GAAG,EAAE,OAAO;UAAxB,iBAWC;UAVC,IAAM,MAAM,GAAGA,GAAC,CAAC,8CAA8C,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;UAC/E,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,OAAO,EAAE,UAAC,KAAK;cACrC,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;cACpB,IAAI,KAAK,CAAC,MAAM,EAAE;kBAChB,MAAM,CAAC,IAAI,CAAC,KAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;kBAClD,KAAI,CAAC,IAAI,EAAE,CAAC;eACb;WACF,CAAC,CAAC;UAEH,OAAO,MAAM,CAAC;OACf;MAED,iCAAW,GAAX,UAAY,CAAC;UAAb,iBAoCC;UAnCC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE;cAC5E,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC,YAAY,EAAE,CAAC;cAC3E,IAAM,SAAO,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;cACrC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,SAAO,EAAE;kBAChC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;kBAEtB,IAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;kBAClE,IAAI,GAAG,EAAE;sBACP,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;sBACrB,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;sBAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,GAAG;0BAC3B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAO,CAAC,EAAE;8BAC5B,KAAI,CAAC,WAAW,CAAC,GAAG,EAAE,SAAO,CAAC,CAAC,QAAQ,CAAC,KAAI,CAAC,QAAQ,CAAC,CAAC;2BACxD;uBACF,CAAC,CAAC;;sBAEH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;;sBAG/D,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;0BAC5B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;8BAChB,IAAI,EAAE,GAAG,CAAC,IAAI;8BACd,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,YAAY;2BAC1D,CAAC,CAAC;uBACJ;2BAAM;0BACL,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;8BAChB,IAAI,EAAE,GAAG,CAAC,IAAI;8BACd,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG,YAAY;2BACzC,CAAC,CAAC;uBACJ;mBACF;eACF;mBAAM;kBACL,IAAI,CAAC,IAAI,EAAE,CAAC;eACb;WACF;OACF;MAED,0BAAI,GAAJ;UACE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;OACtB;MAED,0BAAI,GAAJ;UACE,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;OACtB;MACH,kBAAC;EAAD,CAAC,IAAA;;EC5ND;;;;;MAKE,iBAAY,KAAK,EAAE,OAAO;UACxB,IAAI,CAAC,EAAE,GAAGA,GAAC,CAAC,UAAU,CAAC,EAAE,CAAC;UAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;UAEnB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;UAChB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;UAClB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;UACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;UAEvB,IAAI,CAAC,UAAU,EAAE,CAAC;OACnB;;;;MAKD,4BAAU,GAAV;UACE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;UACjE,IAAI,CAAC,WAAW,EAAE,CAAC;UACnB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;UAClB,OAAO,IAAI,CAAC;OACb;;;;MAKD,yBAAO,GAAP;UACE,IAAI,CAAC,QAAQ,EAAE,CAAC;UAChB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;UACpC,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;OACnD;;;;MAKD,uBAAK,GAAL;UACE,IAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;UACnC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;UACzB,IAAI,CAAC,QAAQ,EAAE,CAAC;UAChB,IAAI,CAAC,WAAW,EAAE,CAAC;UAEnB,IAAI,QAAQ,EAAE;cACZ,IAAI,CAAC,OAAO,EAAE,CAAC;WAChB;OACF;MAED,6BAAW,GAAX;UAAA,iBAiBC;;UAfC,IAAM,OAAO,GAAGA,GAAC,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;UACnD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;cAC/B,KAAI,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;WAC1C,CAAC,CAAC;UAEH,IAAM,OAAO,GAAGA,GAAC,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAEA,GAAC,CAAC,UAAU,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;;UAG/E,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;cAC/B,KAAI,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;WACtC,CAAC,CAAC;UAEH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;cACpC,KAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;WAC5B,CAAC,CAAC;OACJ;MAED,0BAAQ,GAAR;UAAA,iBAWC;;UATC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,UAAC,GAAG;cAC9C,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;WACxB,CAAC,CAAC;UAEH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;cAClC,KAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;WACtB,CAAC,CAAC;;UAEH,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;OACpC;MAED,sBAAI,GAAJ,UAAK,IAAI;UACP,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;UAExD,IAAI,IAAI,KAAK,SAAS,EAAE;cACtB,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;cAC7B,OAAO,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;WACtF;eAAM;cACL,IAAI,WAAW,EAAE;kBACf,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;eACnC;mBAAM;kBACL,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;eACrC;cACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;cACrB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;WACnC;OACF;MAED,4BAAU,GAAV;UACE,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,OAAO,CAAC;OACrE;MAED,wBAAM,GAAN;UACE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;UACvD,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;UACtC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;OACrC;MAED,yBAAO,GAAP;;UAEE,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,EAAE;cACvC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC;WACpC;UACD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;UACxD,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;UAExC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;OACpC;MAED,8BAAY,GAAZ;UACE,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;UACxC,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;UAE/C,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;UAChF,IAAI,QAAQ,EAAE;cACZ,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;WACrC;UACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,GAAG,SAAS,EAAE,IAAI,CAAC,CAAC;OACrD;MAED,kCAAgB,GAAhB,UAAiB,GAAG;UAClB,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;UACjC,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,IAAI,IAAI,CAAC,EAAE,CAAC;UAC7D,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,EAAE;cAC9B,OAAO;WACR;;UAGD,IAAI,MAAM,CAAC,UAAU,EAAE;cACrB,MAAM,CAAC,UAAU,EAAE,CAAC;WACrB;;UAGD,IAAI,MAAM,CAAC,MAAM,EAAE;cACjB,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;WAC7C;OACF;MAED,wBAAM,GAAN,UAAO,GAAG,EAAE,WAAW,EAAE,gBAAgB;UACvC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;cAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;WAC1B;UAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;UAE1C,IAAI,CAAC,gBAAgB,EAAE;cACrB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;WAC5B;OACF;MAED,8BAAY,GAAZ,UAAa,GAAG;UACd,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;UACjC,IAAI,MAAM,CAAC,gBAAgB,EAAE,EAAE;cAC7B,IAAI,MAAM,CAAC,MAAM,EAAE;kBACjB,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;eAC7C;cAED,IAAI,MAAM,CAAC,OAAO,EAAE;kBAClB,MAAM,CAAC,OAAO,EAAE,CAAC;eAClB;WACF;UAED,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;OAC1B;MAED,sBAAI,GAAJ,UAAK,GAAG,EAAE,GAAG;UACX,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;cAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;WACxB;UACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;OACvB;MAED,4BAAU,GAAV,UAAW,GAAG;UACZ,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE;cAC9C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;WAC3B;UAED,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;OACxB;;;;MAKD,mDAAiC,GAAjC,UAAkC,SAAS,EAAE,KAAK;UAAlD,iBAKC;UAJC,OAAO,UAAC,KAAK;cACX,KAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;cAClD,KAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;WAC3C,CAAC;OACH;MAED,qCAAmB,GAAnB,UAAoB,SAAS,EAAE,KAAK;UAApC,iBAMC;UALC,OAAO,UAAC,KAAK;cACX,KAAK,CAAC,cAAc,EAAE,CAAC;cACvB,IAAM,OAAO,GAAGA,GAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;cAChC,KAAI,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC;WACzF,CAAC;OACH;MAED,wBAAM,GAAN;UACE,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;UACxC,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;UAE/C,IAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;UACpC,IAAM,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;UACvC,IAAM,UAAU,GAAG,YAAY,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;UACtD,IAAM,UAAU,GAAG,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;UAE1E,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,QAAQ,CAAC,CAAC;UACpD,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE;cACnC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;WAC3C;eAAM,IAAI,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,gBAAgB,EAAE,EAAE;cACpE,OAAO,MAAM,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;WAC/C;OACF;MACH,cAAC;EAAD,CAAC,IAAA;;ACjODA,KAAC,CAAC,EAAE,CAAC,MAAM,CAAC;;;;;;;MAOV,UAAU,EAAE;UACV,IAAM,IAAI,GAAGA,GAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;UAC3C,IAAM,mBAAmB,GAAG,IAAI,KAAK,QAAQ,CAAC;UAC9C,IAAM,cAAc,GAAG,IAAI,KAAK,QAAQ,CAAC;UAEzC,IAAM,OAAO,GAAGA,GAAC,CAAC,MAAM,CAAC,EAAE,EAAEA,GAAC,CAAC,UAAU,CAAC,OAAO,EAAE,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;;UAGhG,OAAO,CAAC,QAAQ,GAAGA,GAAC,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,EAAEA,GAAC,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,EAAEA,GAAC,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;UACnG,OAAO,CAAC,KAAK,GAAGA,GAAC,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,EAAEA,GAAC,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;UAC9E,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC;UAErF,IAAI,CAAC,IAAI,CAAC,UAAC,GAAG,EAAE,IAAI;cAClB,IAAM,KAAK,GAAGA,GAAC,CAAC,IAAI,CAAC,CAAC;cACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;kBAC7B,IAAM,OAAO,GAAG,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;kBAC5C,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;kBAClC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;eACnE;WACF,CAAC,CAAC;UAEH,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;UAC3B,IAAI,KAAK,CAAC,MAAM,EAAE;cAChB,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;cACzC,IAAI,mBAAmB,EAAE;kBACvB,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;eAC7D;mBAAM,IAAI,OAAO,CAAC,KAAK,EAAE;kBACxB,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;eAChC;WACF;UAED,OAAO,IAAI,CAAC;OACb;GACF,CAAC,CAAC;;ACpBHA,KAAC,CAAC,UAAU,GAAGA,GAAC,CAAC,MAAM,CAACA,GAAC,CAAC,UAAU,EAAE;MACpC,OAAO,EAAE,QAAa;MACtB,EAAE,EAAE,EAAE;MAEN,OAAO,EAAE,EAAE;MAEX,OAAO,EAAE;UACP,OAAO,EAAE;cACP,QAAQ,EAAE,MAAM;cAChB,WAAW,EAAE,SAAS;cACtB,UAAU,EAAE,QAAQ;cACpB,UAAU,EAAEE,QAAQ;cACpB,WAAW,EAAE,SAAS;cACtB,YAAY,EAAE,UAAU;cACxB,QAAQ,EAAE,MAAM;;;cAGhB,aAAa,EAAE,WAAW;cAC1B,UAAU,EAAE,QAAQ;cACpB,UAAU,EAAE,QAAQ;cACpB,aAAa,EAAE,WAAW;cAC1B,SAAS,EAAE,OAAO;cAClB,SAAS,EAAE,OAAO;cAClB,YAAY,EAAE,UAAU;cACxB,aAAa,EAAE,WAAW;cAC1B,aAAa,EAAE,WAAW;cAC1B,cAAc,EAAE,YAAY;cAC5B,cAAc,EAAE,YAAY;cAC5B,aAAa,EAAE,WAAW;cAC1B,YAAY,EAAE,UAAU;cACxB,YAAY,EAAE,UAAU;WACzB;UAED,OAAO,EAAE,EAAE;UAEX,IAAI,EAAE,OAAO;UAEb,gBAAgB,EAAE,IAAI;UACtB,cAAc,EAAE,EAAE;;UAGlB,OAAO,EAAE;cACP,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;cACpB,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;cACxC,CAAC,UAAU,EAAE,CAAC,UAAU,CAAC,CAAC;cAC1B,CAAC,UAAU,EAAE,CAAC,UAAU,CAAC,CAAC;cAC1B,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;cACpB,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;cACnC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;cACpB,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;cACxC,CAAC,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;WAC7C;;UAGD,UAAU,EAAE,IAAI;UAChB,OAAO,EAAE;cACP,KAAK,EAAE;kBACL,CAAC,WAAW,EAAE,CAAC,cAAc,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;kBAC7D,CAAC,OAAO,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;kBACnD,CAAC,QAAQ,EAAE,CAAC,aAAa,CAAC,CAAC;eAC5B;cACD,IAAI,EAAE;kBACJ,CAAC,MAAM,EAAE,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;eACvC;cACD,KAAK,EAAE;kBACL,CAAC,KAAK,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;kBAChE,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;eACtD;cACD,GAAG,EAAE;kBACH,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;kBACpB,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;kBACxC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;kBAC7B,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;kBACpB,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;eAChC;WACF;;UAGD,OAAO,EAAE,KAAK;UAEd,KAAK,EAAE,IAAI;UACX,MAAM,EAAE,IAAI;UACZ,eAAe,EAAE,IAAI;UAErB,KAAK,EAAE,KAAK;UACZ,OAAO,EAAE,CAAC;UACV,aAAa,EAAE,IAAI;UACnB,SAAS,EAAE,IAAI;UACf,gBAAgB,EAAE,IAAI;UACtB,aAAa,EAAE,QAAQ;UACvB,OAAO,EAAE,MAAM;UACf,SAAS,EAAE,MAAM;UACjB,aAAa,EAAE,CAAC;UAChB,uBAAuB,EAAE,CAAC;UAE1B,SAAS,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UAE3D,SAAS,EAAE;cACT,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa;cACtD,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE,eAAe;cACxD,QAAQ,EAAE,iBAAiB,EAAE,SAAS;WACvC;UAED,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;;UAG/D,MAAM,EAAE;cACN,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;cACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;cACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;cACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;cACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;cACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;cACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;cACxF,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;WACzF;;UAGD,UAAU,EAAE;cACV,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC;cAC7F,CAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,SAAS,CAAC;cACvF,CAAC,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,CAAC;cAC/F,CAAC,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,CAAC;cAC1G,CAAC,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,iBAAiB,EAAE,MAAM,CAAC;cAC9F,CAAC,eAAe,EAAE,WAAW,EAAE,cAAc,EAAE,kBAAkB,EAAE,YAAY,EAAE,aAAa,EAAE,gBAAgB,EAAE,UAAU,CAAC;cAC7H,CAAC,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,QAAQ,CAAC;cACnG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,WAAW,EAAE,QAAQ,CAAC;WAC9F;UAED,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;UAErE,cAAc,EAAE,sBAAsB;UAEtC,kBAAkB,EAAE;cAClB,GAAG,EAAE,EAAE;cACP,GAAG,EAAE,EAAE;WACR;UAED,aAAa,EAAE,KAAK;UACpB,WAAW,EAAE,KAAK;UAElB,oBAAoB,EAAE,IAAI;UAE1B,SAAS,EAAE;cACT,MAAM,EAAE,IAAI;cACZ,OAAO,EAAE,IAAI;cACb,MAAM,EAAE,IAAI;cACZ,cAAc,EAAE,IAAI;cACpB,OAAO,EAAE,IAAI;cACb,OAAO,EAAE,IAAI;cACb,SAAS,EAAE,IAAI;cACf,aAAa,EAAE,IAAI;cACnB,kBAAkB,EAAE,IAAI;cACxB,iBAAiB,EAAE,IAAI;WACxB;UAED,UAAU,EAAE;cACV,IAAI,EAAE,WAAW;cACjB,QAAQ,EAAE,IAAI;cACd,WAAW,EAAE,IAAI;WAClB;UAED,MAAM,EAAE;cACN,EAAE,EAAE;kBACF,OAAO,EAAE,iBAAiB;kBAC1B,QAAQ,EAAE,MAAM;kBAChB,QAAQ,EAAE,MAAM;kBAChB,KAAK,EAAE,KAAK;kBACZ,WAAW,EAAE,OAAO;kBACpB,QAAQ,EAAE,MAAM;kBAChB,QAAQ,EAAE,QAAQ;kBAClB,QAAQ,EAAE,WAAW;kBACrB,cAAc,EAAE,eAAe;kBAC/B,gBAAgB,EAAE,cAAc;kBAChC,cAAc,EAAE,aAAa;kBAC7B,cAAc,EAAE,eAAe;kBAC/B,cAAc,EAAE,cAAc;kBAC9B,cAAc,EAAE,aAAa;kBAC7B,iBAAiB,EAAE,qBAAqB;kBACxC,iBAAiB,EAAE,mBAAmB;kBACtC,kBAAkB,EAAE,SAAS;kBAC7B,mBAAmB,EAAE,QAAQ;kBAC7B,WAAW,EAAE,YAAY;kBACzB,WAAW,EAAE,UAAU;kBACvB,WAAW,EAAE,UAAU;kBACvB,WAAW,EAAE,UAAU;kBACvB,WAAW,EAAE,UAAU;kBACvB,WAAW,EAAE,UAAU;kBACvB,WAAW,EAAE,UAAU;kBACvB,YAAY,EAAE,sBAAsB;kBACpC,QAAQ,EAAE,iBAAiB;eAC5B;cAED,GAAG,EAAE;kBACH,OAAO,EAAE,iBAAiB;kBAC1B,OAAO,EAAE,MAAM;kBACf,aAAa,EAAE,MAAM;kBACrB,KAAK,EAAE,KAAK;kBACZ,WAAW,EAAE,OAAO;kBACpB,OAAO,EAAE,MAAM;kBACf,OAAO,EAAE,QAAQ;kBACjB,OAAO,EAAE,WAAW;kBACpB,aAAa,EAAE,eAAe;kBAC9B,eAAe,EAAE,cAAc;kBAC/B,aAAa,EAAE,aAAa;kBAC5B,aAAa,EAAE,eAAe;kBAC9B,aAAa,EAAE,cAAc;kBAC7B,aAAa,EAAE,aAAa;kBAC5B,gBAAgB,EAAE,qBAAqB;kBACvC,gBAAgB,EAAE,mBAAmB;kBACrC,iBAAiB,EAAE,SAAS;kBAC5B,kBAAkB,EAAE,QAAQ;kBAC5B,UAAU,EAAE,YAAY;kBACxB,UAAU,EAAE,UAAU;kBACtB,UAAU,EAAE,UAAU;kBACtB,UAAU,EAAE,UAAU;kBACtB,UAAU,EAAE,UAAU;kBACtB,UAAU,EAAE,UAAU;kBACtB,UAAU,EAAE,UAAU;kBACtB,WAAW,EAAE,sBAAsB;kBACnC,OAAO,EAAE,iBAAiB;eAC3B;WACF;UACD,KAAK,EAAE;cACL,OAAO,EAAE,iBAAiB;cAC1B,aAAa,EAAE,wBAAwB;cACvC,cAAc,EAAE,yBAAyB;cACzC,WAAW,EAAE,sBAAsB;cACnC,YAAY,EAAE,uBAAuB;cACrC,UAAU,EAAE,qBAAqB;cACjC,WAAW,EAAE,sBAAsB;cACnC,UAAU,EAAE,qBAAqB;cACjC,UAAU,EAAE,qBAAqB;cACjC,WAAW,EAAE,sBAAsB;cACnC,WAAW,EAAE,sBAAsB;cACnC,QAAQ,EAAE,wBAAwB;cAClC,SAAS,EAAE,yBAAyB;cACpC,WAAW,EAAE,sBAAsB;cACnC,MAAM,EAAE,gBAAgB;cACxB,OAAO,EAAE,iBAAiB;cAC1B,QAAQ,EAAE,kBAAkB;cAC5B,OAAO,EAAE,iBAAiB;cAC1B,MAAM,EAAE,gBAAgB;cACxB,QAAQ,EAAE,kBAAkB;cAC5B,MAAM,EAAE,gBAAgB;cACxB,OAAO,EAAE,iBAAiB;cAC1B,QAAQ,EAAE,kBAAkB;cAC5B,MAAM,EAAE,gBAAgB;cACxB,QAAQ,EAAE,wBAAwB;cAClC,OAAO,EAAE,iBAAiB;cAC1B,WAAW,EAAE,sBAAsB;cACnC,OAAO,EAAE,iBAAiB;cAC1B,aAAa,EAAE,uBAAuB;cACtC,QAAQ,EAAE,kBAAkB;cAC5B,SAAS,EAAE,mBAAmB;cAC9B,UAAU,EAAE,oBAAoB;cAChC,MAAM,EAAE,gBAAgB;cACxB,QAAQ,EAAE,kBAAkB;cAC5B,eAAe,EAAE,yBAAyB;cAC1C,WAAW,EAAE,qBAAqB;cAClC,aAAa,EAAE,uBAAuB;cACtC,OAAO,EAAE,iBAAiB;cAC1B,YAAY,EAAE,uBAAuB;cACrC,OAAO,EAAE,iBAAiB;cAC1B,WAAW,EAAE,qBAAqB;cAClC,MAAM,EAAE,gBAAgB;cACxB,eAAe,EAAE,yBAAyB;cAC1C,OAAO,EAAE,iBAAiB;WAC3B;OACF;GACF,CAAC,CAAC;;;;"}