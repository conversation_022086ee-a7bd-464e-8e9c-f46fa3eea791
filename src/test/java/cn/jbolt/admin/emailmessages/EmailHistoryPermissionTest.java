package cn.jbolt.admin.emailmessages;

import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.mail.gpt.InitEnv;
import com.jfinal.kit.Kv;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;

/**
 * 邮件历史权限控制测试
 * 测试getEmailHistory方法的权限控制逻辑
 */
public class EmailHistoryPermissionTest {

    private static EmailMessagesService emailMessagesService;
    private static final Long TEST_USER_ID = 1L;
    private static final String MANAGED_EMAIL = "<EMAIL>";
    private static final String EXTERNAL_EMAIL = "<EMAIL>";

    public static void main(String[] args) {
        InitEnv.initEnvironment();
        emailMessagesService = new EmailMessagesService();

        System.out.println("=== 邮件历史权限控制测试 ===");

        // 设置测试用户
        JBoltUserKit.setUserId(TEST_USER_ID);

        // 清理测试数据
        cleanTestData();

        // 准备测试数据
        prepareTestData();

        // 运行测试
        testManagedEmailPermission();
        testExternalEmailPermission();
        testEmptyEmailParameter();
        testNullEmailParameter();
        testUserNotLoggedIn();
        testPermissionCheckQuery();

        System.out.println("=== 测试完成 ===");
    }
    
    private static void cleanTestData() {
        // 清理user_email表的测试数据
        Db.update("DELETE FROM user_email WHERE user_id = ?", TEST_USER_ID);
        
        // 清理email_messages表的测试数据
        Db.update("DELETE FROM email_messages WHERE from_address IN (?, ?) OR to_address LIKE ? OR to_address LIKE ?", 
            MANAGED_EMAIL, EXTERNAL_EMAIL, "%" + MANAGED_EMAIL + "%", "%" + EXTERNAL_EMAIL + "%");
    }
    
    private static void prepareTestData() {
        // 插入用户管理的邮箱
        Db.update("INSERT INTO user_email (user_id, email, email_name, type) VALUES (?, ?, ?, ?)", 
            TEST_USER_ID, MANAGED_EMAIL, "用户邮箱", 0);
        
        // 插入测试邮件数据
        // 1. 用户管理邮箱发送的邮件
        Db.update("INSERT INTO email_messages (id, email_account, message_id, message_hash, subject, from_address, to_address, sent_date, folder_name, is_delete, created_at) " +
                  "VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, NOW())",
            1001L, MANAGED_EMAIL, "msg1", "hash1", "测试邮件1", MANAGED_EMAIL, EXTERNAL_EMAIL, "INBOX", 0);

        // 2. 外部邮箱发送给用户管理邮箱的邮件
        Db.update("INSERT INTO email_messages (id, email_account, message_id, message_hash, subject, from_address, to_address, sent_date, folder_name, is_delete, created_at) " +
                  "VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, NOW())",
            1002L, MANAGED_EMAIL, "msg2", "hash2", "测试邮件2", EXTERNAL_EMAIL, MANAGED_EMAIL, "INBOX", 0);

        // 3. 外部邮箱之间的邮件（不应该被查询到）
        Db.update("INSERT INTO email_messages (id, email_account, message_id, message_hash, subject, from_address, to_address, sent_date, folder_name, is_delete, created_at) " +
                  "VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, NOW())",
            1003L, "<EMAIL>", "msg3", "hash3", "测试邮件3", EXTERNAL_EMAIL, "<EMAIL>", "INBOX", 0);
    }
    
    public static void testManagedEmailPermission() {
        System.out.println("测试用户管理的邮箱权限...");
        // 测试查询用户管理的邮箱 - 应该能看到所有相关邮件
        Kv params = Kv.by("email", MANAGED_EMAIL);
        Ret result = emailMessagesService.getEmailHistory(1, 10, params);

        if (result.isOk()) {
            System.out.println("✓ 查询用户管理的邮箱成功");
        } else {
            System.out.println("✗ 查询用户管理的邮箱失败: " + result.getStr("msg"));
        }
    }

    public static void testExternalEmailPermission() {
        System.out.println("测试外部邮箱权限...");
        // 测试查询外部邮箱 - 只能看到与用户管理邮箱的来往邮件
        Kv params = Kv.by("email", EXTERNAL_EMAIL);
        Ret result = emailMessagesService.getEmailHistory(1, 10, params);

        if (result.isOk()) {
            System.out.println("✓ 查询外部邮箱成功（限制权限）");
        } else {
            System.out.println("✗ 查询外部邮箱失败: " + result.getStr("msg"));
        }
    }

    public static void testEmptyEmailParameter() {
        System.out.println("测试空邮箱参数...");
        // 测试空邮箱参数
        Kv params = Kv.by("email", "");
        Ret result = emailMessagesService.getEmailHistory(1, 10, params);

        if (!result.isOk() && "邮箱地址不能为空".equals(result.getStr("msg"))) {
            System.out.println("✓ 空邮箱参数验证正确");
        } else {
            System.out.println("✗ 空邮箱参数验证失败");
        }
    }

    public static void testNullEmailParameter() {
        System.out.println("测试null邮箱参数...");
        // 测试null邮箱参数
        Kv params = Kv.create();
        Ret result = emailMessagesService.getEmailHistory(1, 10, params);

        if (!result.isOk() && "邮箱地址不能为空".equals(result.getStr("msg"))) {
            System.out.println("✓ null邮箱参数验证正确");
        } else {
            System.out.println("✗ null邮箱参数验证失败");
        }
    }

    public static void testUserNotLoggedIn() {
        System.out.println("测试用户未登录情况...");
        // 测试用户未登录的情况
        JBoltUserKit.setUserId(null);

        Kv params = Kv.by("email", MANAGED_EMAIL);
        Ret result = emailMessagesService.getEmailHistory(1, 10, params);

        if (!result.isOk() && "用户未登录".equals(result.getStr("msg"))) {
            System.out.println("✓ 用户未登录验证正确");
        } else {
            System.out.println("✗ 用户未登录验证失败");
        }

        // 恢复用户登录状态
        JBoltUserKit.setUserId(TEST_USER_ID);
    }

    /**
     * 测试权限检查的SQL查询逻辑
     */
    public static void testPermissionCheckQuery() {
        System.out.println("测试权限检查SQL查询...");
        // 验证用户管理的邮箱检查
        int managedCount = Db.queryInt("SELECT COUNT(*) FROM user_email WHERE user_id = ? AND email = ?",
            TEST_USER_ID, MANAGED_EMAIL);

        if (managedCount > 0) {
            System.out.println("✓ 能找到用户管理的邮箱");
        } else {
            System.out.println("✗ 未找到用户管理的邮箱");
        }

        // 验证外部邮箱检查
        int externalCount = Db.queryInt("SELECT COUNT(*) FROM user_email WHERE user_id = ? AND email = ?",
            TEST_USER_ID, EXTERNAL_EMAIL);

        if (externalCount == 0) {
            System.out.println("✓ 正确识别外部邮箱");
        } else {
            System.out.println("✗ 外部邮箱识别错误");
        }
    }
}
