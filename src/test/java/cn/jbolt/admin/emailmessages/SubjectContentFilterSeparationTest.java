package cn.jbolt.admin.emailmessages;

import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.core.model.Kv;
import cn.jbolt.core.model.Ret;
import cn.jbolt.test.InitEnv;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

/**
 * 主题和内容筛选分离功能测试
 * 测试主题筛选和内容筛选可以独立工作且组合使用
 */
public class SubjectContentFilterSeparationTest {

    private static EmailMessagesService emailMessagesService;
    private static final Long TEST_USER_ID = 1L;

    public static void main(String[] args) {
        InitEnv.initEnvironment();
        emailMessagesService = new EmailMessagesService();

        System.out.println("=== Subject and Content Filter Separation Test ===");

        // Set test user
        JBoltUserKit.setUserId(TEST_USER_ID);

        // Prepare test data
        prepareTestData();

        // Run tests
        testSubjectFilterOnly();
        testContentFilterOnly();
        testCombinedFilters();
        testBackwardCompatibility();

        // Clean test data
        cleanTestData();

        System.out.println("=== Test Complete ===");
    }

    /**
     * Prepare test data
     */
    public static void prepareTestData() {
        System.out.println("Preparing test data...");
        
        // Clean existing test data
        cleanTestData();
        
        // Insert test email data
        String insertSql = "INSERT INTO email_messages (id, email_account, message_id, message_hash, subject, " +
                          "from_display, from_address, to_display, to_address, folder_name, sent_date, created_at, " +
                          "content_html, content_text, is_delete) VALUES " +
                          "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW(), ?, ?, 0)";
        
        // Test email 1: Specific subject, different content
        Db.update(insertSql, 9999101L, "<EMAIL>", "test-msg-101", "hash101", "Important Meeting",
                 "John Smith", "<EMAIL>", "Test User", "<EMAIL>", "INBOX",
                 "<p>This email contains project details and timeline information.</p>", 
                 "This email contains project details and timeline information.");
        
        // Test email 2: Different subject, specific content
        Db.update(insertSql, 9999102L, "<EMAIL>", "test-msg-102", "hash102", "Weekly Report",
                 "Jane Doe", "<EMAIL>", "Test User", "<EMAIL>", "INBOX",
                 "<p>Meeting scheduled for tomorrow at 10 AM in conference room.</p>", 
                 "Meeting scheduled for tomorrow at 10 AM in conference room.");
        
        // Test email 3: Contains both subject and content keywords
        Db.update(insertSql, 9999103L, "<EMAIL>", "test-msg-103", "hash103", "Project Meeting Update",
                 "Bob Wilson", "<EMAIL>", "Test User", "<EMAIL>", "INBOX",
                 "<p>Important project milestone achieved. Meeting details attached.</p>", 
                 "Important project milestone achieved. Meeting details attached.");
        
        // Test email 4: Neither subject nor content matches
        Db.update(insertSql, 9999104L, "<EMAIL>", "test-msg-104", "hash104", "Daily Standup",
                 "Alice Brown", "<EMAIL>", "Test User", "<EMAIL>", "INBOX",
                 "<p>Team sync discussion about current tasks and blockers.</p>", 
                 "Team sync discussion about current tasks and blockers.");
        
        System.out.println("Test data preparation complete");
    }

    /**
     * Clean test data
     */
    public static void cleanTestData() {
        Db.update("DELETE FROM email_messages WHERE id BETWEEN 9999101 AND 9999199");
    }

    /**
     * Test subject filter only
     */
    public static void testSubjectFilterOnly() {
        System.out.println("\nTesting subject filter only...");
        
        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("subjectFilter", "Meeting")
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);
        
        Ret result = emailMessagesService.getDashboardEmails(params);
        
        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            
            // Should find emails with "Meeting" in subject (emails 1 and 3)
            long meetingCount = emails.stream().filter(email -> {
                String subject = email.getStr("subject");
                return subject != null && subject.contains("Meeting");
            }).count();
            
            if (meetingCount >= 2) {
                System.out.println("PASS - Subject filter test passed, found " + meetingCount + " emails with 'Meeting' in subject");
            } else {
                System.out.println("FAIL - Subject filter test failed, expected at least 2 emails, found " + meetingCount);
            }
        } else {
            System.out.println("FAIL - Subject filter test failed: " + result.getStr("msg"));
        }
    }

    /**
     * Test content filter only
     */
    public static void testContentFilterOnly() {
        System.out.println("\nTesting content filter only...");
        
        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("contentFilter", "project")
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);
        
        Ret result = emailMessagesService.getDashboardEmails(params);
        
        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            
            // Should find emails with "project" in content (emails 1 and 3)
            long projectCount = emails.stream().filter(email -> {
                String contentHtml = email.getStr("content_html");
                String contentText = email.getStr("content_text");
                return (contentHtml != null && contentHtml.toLowerCase().contains("project")) ||
                       (contentText != null && contentText.toLowerCase().contains("project"));
            }).count();
            
            if (projectCount >= 2) {
                System.out.println("PASS - Content filter test passed, found " + projectCount + " emails with 'project' in content");
            } else {
                System.out.println("FAIL - Content filter test failed, expected at least 2 emails, found " + projectCount);
            }
        } else {
            System.out.println("FAIL - Content filter test failed: " + result.getStr("msg"));
        }
    }

    /**
     * Test combined filters
     */
    public static void testCombinedFilters() {
        System.out.println("\nTesting combined subject and content filters...");
        
        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("subjectFilter", "Project")
                      .set("contentFilter", "milestone")
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);
        
        Ret result = emailMessagesService.getDashboardEmails(params);
        
        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            
            // Should find only email 3 which has "Project" in subject AND "milestone" in content
            boolean foundTargetEmail = emails.stream().anyMatch(email -> {
                String subject = email.getStr("subject");
                String contentHtml = email.getStr("content_html");
                String contentText = email.getStr("content_text");
                
                boolean hasProjectInSubject = subject != null && subject.contains("Project");
                boolean hasMilestoneInContent = (contentHtml != null && contentHtml.contains("milestone")) ||
                                               (contentText != null && contentText.contains("milestone"));
                
                return hasProjectInSubject && hasMilestoneInContent;
            });
            
            if (foundTargetEmail) {
                System.out.println("PASS - Combined filters test passed, found email matching both criteria");
            } else {
                System.out.println("FAIL - Combined filters test failed, no email found matching both criteria");
            }
        } else {
            System.out.println("FAIL - Combined filters test failed: " + result.getStr("msg"));
        }
    }

    /**
     * Test backward compatibility - ensure no regression
     */
    public static void testBackwardCompatibility() {
        System.out.println("\nTesting backward compatibility...");
        
        // Test that empty filters don't cause issues
        Kv params = Kv.by("userId", TEST_USER_ID)
                      .set("page", 1)
                      .set("pageSize", 10)
                      .set("days", 30);
        
        Ret result = emailMessagesService.getDashboardEmails(params);
        
        if (result.isOk()) {
            @SuppressWarnings("unchecked")
            List<Record> emails = (List<Record>) result.get("data");
            
            if (emails.size() >= 4) {
                System.out.println("PASS - Backward compatibility test passed, found " + emails.size() + " emails without filters");
            } else {
                System.out.println("FAIL - Backward compatibility test failed, expected at least 4 emails, found " + emails.size());
            }
        } else {
            System.out.println("FAIL - Backward compatibility test failed: " + result.getStr("msg"));
        }
    }
}
