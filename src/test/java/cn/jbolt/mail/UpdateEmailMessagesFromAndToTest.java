package cn.jbolt.mail;

import cn.jbolt.admin.emails.EmailNameCache;
import cn.jbolt.common.util.StringKit;
import cn.jbolt.mail.gpt.InitEnv;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public class UpdateEmailMessagesFromAndToTest {
    public static void main(String[] args) {
        InitEnv.initEnvironment();
        EmailNameCache.me.refreshCache();
        List<Record> records = Db.find("select * from email_messages");
//        List<String> batchUpdateList = Lists.newArrayList();
        int i = 0;
        for (Record record : records) {
            i++;
            Long id = record.getLong("id");
            String fromAddress = record.getStr("from_address");
            String toAddress = record.getStr("to_address");
            String fromDisplayName = StringKit.getDisplayName(fromAddress);
            String toDisplayName = StringKit.getDisplayName(toAddress);
            if (fromDisplayName.length() > 512) {
                fromDisplayName = fromDisplayName.substring(0, 512);
            }
            if (toDisplayName.length() > 512) {
                toDisplayName = toDisplayName.substring(0, 512);
            }
            fromDisplayName = fromDisplayName.replace("'", "\\'");
            toDisplayName = toDisplayName.replace("'", "\\'");
            Db.update("update email_messages set from_display='" + fromDisplayName + "', to_display='" + toDisplayName + "' where id=" + id + ";");
//            batchUpdateList.add("update email_messages set from_display='" + fromDisplayName + "', to_display='" + toDisplayName + "' where id=" + id + ";");
//            if (batchUpdateList.size() > 100) {
//                Db.batch(batchUpdateList, batchUpdateList.size());
//                batchUpdateList.clear();
//                System.out.println("当前进度: " + i);
//            }
        }
//        Db.batch(batchUpdateList, batchUpdateList.size());
//        batchUpdateList.clear();
        System.out.println("当前进度: " + i + ", 全部完成!");
    }
}
