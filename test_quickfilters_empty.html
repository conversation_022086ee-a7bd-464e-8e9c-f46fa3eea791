<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuickFilters 默认空状态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>QuickFilters 默认空状态测试</h1>
    <p>此测试验证 quickFilters 在页面加载时是否保持默认的空状态。</p>
    
    <div id="test-results"></div>
    
    <h2>测试详情</h2>
    <div id="test-details"></div>

    <script>
        // 模拟 quickFilters 初始化
        window.quickFilters = window.quickFilters || {
            active: {},             // 当前激活的过滤条件
            logic: 'or',           // 过滤逻辑：'and' 或 'or'
            presets: {}            // 预设过滤条件
        };

        // 模拟其他相关变量
        window.currentCustomerOnly = false;

        function runTests() {
            const results = [];
            const details = [];

            // 测试1: quickFilters.active 应该为空对象
            const test1 = Object.keys(window.quickFilters.active).length === 0;
            results.push({
                name: 'quickFilters.active 为空对象',
                passed: test1,
                expected: '{}',
                actual: JSON.stringify(window.quickFilters.active)
            });

            // 测试2: quickFilters.logic 应该为 'or'
            const test2 = window.quickFilters.logic === 'or';
            results.push({
                name: 'quickFilters.logic 为默认值 "or"',
                passed: test2,
                expected: 'or',
                actual: window.quickFilters.logic
            });

            // 测试3: currentCustomerOnly 应该为 false
            const test3 = window.currentCustomerOnly === false;
            results.push({
                name: 'currentCustomerOnly 为 false',
                passed: test3,
                expected: 'false',
                actual: String(window.currentCustomerOnly)
            });

            // 测试4: quickFilters.presets 应该为空对象
            const test4 = Object.keys(window.quickFilters.presets).length === 0;
            results.push({
                name: 'quickFilters.presets 为空对象',
                passed: test4,
                expected: '{}',
                actual: JSON.stringify(window.quickFilters.presets)
            });

            // 显示测试结果
            displayResults(results);
            displayDetails(results);
        }

        function displayResults(results) {
            const container = document.getElementById('test-results');
            const passedCount = results.filter(r => r.passed).length;
            const totalCount = results.length;

            let html = `<div class="info">
                <strong>测试总结:</strong> ${passedCount}/${totalCount} 个测试通过
            </div>`;

            results.forEach(result => {
                const className = result.passed ? 'pass' : 'fail';
                const status = result.passed ? '✓ 通过' : '✗ 失败';
                html += `<div class="${className}">
                    <strong>${status}:</strong> ${result.name}
                </div>`;
            });

            container.innerHTML = html;
        }

        function displayDetails(results) {
            const container = document.getElementById('test-details');
            let html = '';

            results.forEach((result, index) => {
                html += `<h3>测试 ${index + 1}: ${result.name}</h3>`;
                html += `<p><strong>状态:</strong> ${result.passed ? '✓ 通过' : '✗ 失败'}</p>`;
                html += `<p><strong>期望值:</strong> <code>${result.expected}</code></p>`;
                html += `<p><strong>实际值:</strong> <code>${result.actual}</code></p>`;
                
                if (!result.passed) {
                    html += `<p style="color: #721c24;"><strong>问题:</strong> 实际值与期望值不匹配</p>`;
                }
                html += '<hr>';
            });

            // 显示完整的 quickFilters 对象
            html += '<h3>完整的 quickFilters 对象:</h3>';
            html += `<pre>${JSON.stringify(window.quickFilters, null, 2)}</pre>`;

            container.innerHTML = html;
        }

        // 模拟不加载本地存储的函数
        function loadQuickFiltersFromLocal() {
            try {
                // 默认不加载本地存储的过滤状态，保持为空
                console.log('[快速过滤] 跳过本地存储加载，保持默认空状态');
                
                // 确保quickFilters为空状态
                window.quickFilters.active = {};
                window.quickFilters.logic = 'or';
                
                // 清除所有可能的激活按钮状态
                document.querySelectorAll('.quick-filter-btn.active').forEach(btn => {
                    btn.classList.remove('active');
                });
                
                // 确保客户邮件筛选状态为false
                window.currentCustomerOnly = false;
                
                console.log('[快速过滤] 已重置为空状态');
                
            } catch (error) {
                console.warn('[快速过滤] 重置过滤状态失败:', error);
            }
        }

        // 运行测试
        document.addEventListener('DOMContentLoaded', function() {
            // 模拟加载过程
            loadQuickFiltersFromLocal();
            
            // 运行测试
            runTests();
        });
    </script>
</body>
</html>
